const { Router } = require('express')
const router = Router()
const isAuth = require('../../../middlewares/isAuth')

const LevelsClass = require('../../../controllers/levels')
const LevelsController = new LevelsClass()

router.get('/get-all-levels', isAuth, LevelsController.getAllLevels)
router.delete('/', isAuth, LevelsController.deleteProductLevel)
router.put('/update-last-level', isAuth, LevelsController.updateLastLevel)

module.exports = router
