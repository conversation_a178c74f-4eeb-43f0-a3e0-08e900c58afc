const { Schema, model } = require('mongoose')

const mensagemSchema = Schema({
  companys: {
    type: [String],
    required: true,
  },

  destiny: {
    type: [String],
    required: true,
  },

  type: {
    type: String,
    required: true,
  },

  message: {
    type: String,
    required: true,
  },

  dataEnvio: {
    type: Date,
    required: true,
  },

  visualized: {
    type: Array,
    require: false,
  },
})

const MensagemModel = model('messages', mensagemSchema)

module.exports = MensagemModel
