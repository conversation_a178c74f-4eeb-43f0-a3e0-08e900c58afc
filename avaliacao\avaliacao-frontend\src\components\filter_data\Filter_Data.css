.container-modal-data {
  left: 50%;
  top: 50%;
  position: absolute;
  width: 650px;
  height: 290px;
  border-radius: 19px;
  background-color: rgb(239, 242, 247);
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
  transform: translate(-50%, -50%);
  padding: 25px;
}
.container-input-date-filter-data {
  margin-top: 15px;
  padding: 25px;
  margin: 10px;
  width: 600px;
  height: 110px;
}
.container-buttons-filter-data {
  align-items: center;
  display: flex;
  justify-content: center;
}
.container-buttons-filter-data-button {
  width: 50%;
  height: 49px;
  background-color: sienna;
  color: #fff;
  border-radius: 1px;
  text-align: center;
  cursor: pointer;
  border: none;
  border-right: 1px solid #fff;
  font-size: 15px;
}
.container-buttons-filter-data-button:hover {
  background: #eea405;
  color: whitesmoke;
}
.isSelect {
  width: 50%;
  height: 49px;
  background: #eea405;
  color: whitesmoke;
  border-radius: 1px;
  text-align: center;
  cursor: pointer;
  border: none;
  border-right: 1px solid #fff;
  font-size: 15px;
}

.container-checkbox-filter-data {
  display: flex;
  margin-bottom: 35px;
}
.container-checkbox {
  margin-left: 20px;
  padding-left: 15px;
}
.container-checkbox span {
  padding: 10px;
}
.container-input-mes {
  margin-top: 19px;
}
.container-input-periodo {
  margin-top: 15px;
  display: grid;
  grid-template-columns: auto auto;
}
.container-filter-checkbox-periodo {
  align-items: center;
  display: flex;
  justify-content: center;
}
.span-data-filter {
  margin-right: 35px;
}
.date-mes-filter-data {
  border: 1px solid #c9c9c9;
  border-radius: 1px;
  height: 32px;
  width: 90%;
  margin-top: 5px;
  background-color: #fff;
  color: #666;
  text-align: start;
  padding-left: 16px;
  padding-right: 16px;
}
.date-begin {
  border: 1px solid #c9c9c9;
  border-radius: 1px;
  height: 32px;
  width: 85%;
  margin-top: 5px;
  background-color: #fff;
  color: #666;
  text-align: start;
  padding-left: 16px;
  padding-right: 16px;
}

.date-end {
  border: 1px solid #c9c9c9;
  border-radius: 1px;
  height: 32px;
  width: 85%;
  margin-top: 5px;
  background-color: #fff;
  color: #666;
  text-align: start;
  padding-left: 16px;
  padding-right: 16px;
}

.footer-modal-filter-data {
  display: flex;
  justify-content: flex-end;
  margin-top: 50px;
  margin-left: 200px;
}
.btn-modal-filter-data-cancelar {
  margin-left: 20px;
  padding: 6px 20px;
  border: 1px solid transparent;
  border-radius: 5px;
  color: white;
  background-color: red;
  font-size: medium;
  cursor: pointer;
}
.btn-modal-filter-data-cancelar:hover {
  background-color: rgb(247, 74, 74);
}
.btn-modal-filter-data-salvar {
  margin-left: 20px;
  padding: 6px 20px;
  border: 1px solid transparent;
  border-radius: 5px;
  color: white;
  background-color: green;
  font-size: medium;
  cursor: pointer;
}
.btn-modal-filter-data-salvar:hover {
  background-color: rgb(15, 214, 15);
}
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(0deg, rgba(107, 103, 105, 0.3), rgba(75, 72, 74, 0.3));
  background-size: cover;
}

@media (max-width: 1366px) {
  .container-modal-data {
    left: 50%;
    top: 45%;
    position: absolute;
    width: 650px;
    height: 290px;
    border-radius: 19px;
    background-color: rgb(239, 242, 247);
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
    transform: translate(-50%, -50%);
    padding: 25px;
  }
}
