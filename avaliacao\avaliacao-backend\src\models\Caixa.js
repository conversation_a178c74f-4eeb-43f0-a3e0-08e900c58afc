const { Schema, model } = require('mongoose')

// Definindo Schema
const CaixaSchema = Schema({
  unidade: {
    type: String,
  },
  cnpj: {
    type: String,
  },
  dataAbertura: {
    type: Date,
  },
  dataFechamento: {
    type: Date,
    default: null,
  },
  vlrAbertura: {
    type: Number,
  },
  idUsuario: {
    type: String,
  },
  condPagamento: {
    type: [Object],
  },
  sangria: {
    type: Number,
  },
  reforco: {
    type: Number,
  },
  relatorios: {
    avaliacoes_realizadas: {
      qtd: {
        type: Number,
        default: 0,
      },
      total: {
        type: Number,
        default: 0,
      },
    },
    avaliacoes_finalizadas: {
      qtd: {
        type: Number,
        default: 0,
      },
      total: {
        type: Number,
        default: 0,
      },
    },
    avaliacoes_canceladas: {
      qtd: {
        type: Number,
        default: 0,
      },
      total: {
        type: Number,
        default: 0,
      },
    },
    avaliacoes_nao_aceitou_valor: {
      qtd: {
        type: Number,
        default: 0,
      },
      total: {
        type: Number,
        default: 0,
      },
    },
    avaliacoes_manuais: {
      qtd: {
        type: Number,
        default: 0,
      },
      total: {
        type: Number,
        default: 0,
      },
    },
    avaliacoes_via_sistema: {
      qtd: {
        type: Number,
        default: 0,
      },
      total: {
        type: Number,
        default: 0,
      },
    },
    avaliacoes_cliente_nao_esperou: {
      qtd: {
        type: Number,
        default: 0,
      },
      total: {
        type: Number,
        default: 0,
      },
    },
    avaliacoes_teste: {
      qtd: {
        type: Number,
        default: 0,
      },
      total: {
        type: Number,
        default: 0,
      },
    },
    avaliacoes_erro_sistema: {
      qtd: {
        type: Number,
        default: 0,
      },
      total: {
        type: Number,
        default: 0,
      },
    },
    avaliacoes_erro_operador: {
      qtd: {
        type: Number,
        default: 0,
      },
      total: {
        type: Number,
        default: 0,
      },
    },
    avaliacoes_outros: {
      qtd: {
        type: Number,
        default: 0,
      },
      total: {
        type: Number,
        default: 0,
      },
    },
    avaliacoes_nao_enquadrou_perfil: {
      qtd: {
        type: Number,
        default: 0,
      },
      total: {
        type: Number,
        default: 0,
      },
    },
  },
})

// Definindo collection
const Caixa = model('caixaAvaliacao', CaixaSchema)
module.exports = Caixa
