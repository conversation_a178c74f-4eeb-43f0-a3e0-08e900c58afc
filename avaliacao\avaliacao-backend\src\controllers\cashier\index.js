const CashierModel = require('../../models/Caixa')
const EvaluationsModel = require('../../models/Avaliacao')
const moment = require('moment-timezone')

class CashiersClass {
  constructor() { }

  async makeReinforcement(req, res) {
    const { id, reforco } = req.body

    const cashier = await CashierModel.findOne({ _id: id });

    if (!cashier) {
      return res.status(404).json({
        status: false,
        message: 'Caixa não encontrado.'
      })
    }

    try {
      await CashierModel.findOneAndUpdate({ _id: id }, { reforco: Number(reforco) + cashier.reforco })
      return res.status(200).json({
        status: true,
        message: 'Reforço realizado.'
      })
    } catch (err) {
      return res.status(400).json(err)
    }
  }

  async makeClose(req, res) {
    const { cashierId } = req.body
    const { _id } = req.usuario

    const cashier = await CashierModel.findOne({ _id: cashierId, idUsuario: _id })

    if (!cashier) {
      return res.status(404).json({
        status: false,
        message: 'Caixa não existe.'
      })
    }

    const allEvaluationsFromCashier = await EvaluationsModel.find({ caixa: cashierId }).lean()
    const allMoneyEvaluationsFromCashier = allEvaluationsFromCashier?.filter(evaluation => evaluation?.tipo === 'Dinheiro' && !evaluation?.cancelado && !evaluation?.rejected)
    const allVoucherEvaluationsFromCashier = allEvaluationsFromCashier?.filter(evaluation => evaluation?.tipo === 'Vale' && !evaluation?.cancelado && !evaluation?.rejected)
    const allPixEvaluationsFromCashier = allEvaluationsFromCashier?.filter(evaluation => evaluation?.tipo === 'Pix' && !evaluation?.cancelado && !evaluation?.rejected)

    let totalMoneyValue = allMoneyEvaluationsFromCashier?.reduce((acc, evaluation) => {
      return Number(acc) + Number(evaluation?.totalDinheiroEfetivado || 0)
    }, 0)

    let totalVoucherValue = allVoucherEvaluationsFromCashier?.reduce((acc, evaluation) => {
      return Number(acc) + Number(evaluation?.totalValeEfetivado || 0)
    }, 0)

    let totalPixValue = allPixEvaluationsFromCashier?.reduce((acc, evaluation) => {
      return Number(acc) + Number(evaluation?.totalPixEfetivado || 0)
    }, 0)

    const paymentCondition = [
      {
        dinheiro: totalMoneyValue
      },
      {
        vale: totalVoucherValue
      },
      {
        valorPrevisto: Number(cashier?.reforco) + Number(cashier?.vlrAbertura) - totalMoneyValue
      },
      {
        totalAvaliacoes: totalMoneyValue + totalVoucherValue + totalPixValue
      },
      {
        pix: totalPixValue,
      }
    ]

    const reports = await createReport(allEvaluationsFromCashier)

    const newCashierData = await CashierModel.findOneAndUpdate(
      { _id: cashierId },
      {
        condPagamento: paymentCondition,
        dataFechamento: moment.utc().subtract({ hours: 3 }).toDate(),
        relatorios: reports
      },
      {
        new: true
      }
    )

    return res.status(200).json({
      status: true,
      cashier: newCashierData
    })
  }

  async check(req, res) {
    const { id } = req.params;

    const cashier = await CashierModel.findOne({ _id: id }).lean();

    if (!cashier) {
      return res.status(404).json({ status: false, error: 'Caixa não encontrado.' });
    };

    return res.status(200).json({ isClosed: cashier?.dataFechamento ? true : false })
  };
}

const createReport = async (allEvaluationsFromCashier) => {
  const allMoneyEvaluationsFromCashier = allEvaluationsFromCashier?.filter(
    evaluation => evaluation?.tipo === "Dinheiro"
  );
  const allPixEvaluationsFromCashier = allEvaluationsFromCashier?.filter(
    evaluation => evaluation?.tipo === "Pix"
  );

  const allVoucherEvaluationsFromCashier = allEvaluationsFromCashier?.filter(
    evaluation =>
      evaluation?.tipo === "Vale" &&
      evaluation?.cancelado === false &&
      evaluation?.rejected === false &&
      evaluation.finalizado === true
  );
  const totalCanceledEvaluations = allEvaluationsFromCashier?.filter(
    evaluation =>
      evaluation?.finalizado && evaluation?.cancelado && !evaluation?.rejected
  );
  const totalRejectedEvaluations = allEvaluationsFromCashier?.filter(
    evaluation =>
      evaluation.finalizado && evaluation.cancelado && evaluation?.rejected
  );

  let totalMoneyValue = 0;
  let totalPixValue = 0;
  let totalVoucherValue = 0;
  let qtdMoneyEvaluation = 0;

  allMoneyEvaluationsFromCashier
    .filter(evaluation => {
      return (
        (evaluation?.tipo === "Dinheiro" &&
          evaluation?.totalDinheiroEfetivado !== "(Não informado)") ||
        (evaluation?.tipo === "Pix" &&
          evaluation?.totalPixEfetivado !== "(Não informado)") ||
        (evaluation?.tipo === "Vale" &&
          evaluation?.totalValeEfetivado !== "(Não informado)") ||
        (!evaluation?.tipo &&
          evaluation?.totalValeEfetivado !== "(Não informado)")
      );
    })
    .forEach(evaluation => {
      if (evaluation?.cancelado === true || evaluation?.rejected === true)
        totalMoneyValue += 0;
      else {
        totalMoneyValue += Number(evaluation?.totalDinheiroEfetivado || 0);
        qtdMoneyEvaluation += 1;
      }
    });

  allPixEvaluationsFromCashier
    .filter(evaluation => {
      return (
        (evaluation?.tipo === "Dinheiro" &&
          evaluation?.totalDinheiroEfetivado !== "(Não informado)") ||
        (evaluation?.tipo === "Pix" &&
          evaluation?.totalPixEfetivado !== "(Não informado)") ||
        (evaluation?.tipo === "Vale" &&
          evaluation?.totalValeEfetivado !== "(Não informado)") ||
        (!evaluation?.tipo &&
          evaluation?.totalValeEfetivado !== "(Não informado)")
      );
    })
    .forEach(evaluation => {
      if (evaluation?.cancelado === true || evaluation?.rejected === true)
        totalPixValue += 0;
      else {
        totalPixValue += Number(evaluation?.totalPixEfetivado || 0);
      }
    });

  allVoucherEvaluationsFromCashier
    .filter(evaluation => {
      return (
        (evaluation?.tipo === "Dinheiro" &&
          evaluation?.totalDinheiroEfetivado !== "(Não informado)") ||
        (evaluation?.tipo === "Vale" &&
          evaluation?.totalValeEfetivado !== "(Não informado)") ||
        (!evaluation?.tipo &&
          evaluation?.totalValeEfetivado !== "(Não informado)")
      );
    })
    .forEach(evaluation => {
      if (evaluation?.cancelado === true || evaluation?.rejected === true)
        totalVoucherValue += 0;
      else totalVoucherValue += Number(evaluation?.totalValeEfetivado || 0);
    });

  const formatValues = (acc, evaluation) => {
    if (
      evaluation?.tipo === "Dinheiro" &&
      evaluation?.totalDinheiroEfetivado !== "(Não informado)"
    ) {
      return acc + Number(evaluation?.totalDinheiroEfetivado || 0);
    }

    if (
      evaluation?.tipo === "Pix" &&
      evaluation?.totalPixEfetivado !== "(Não informado)"
    ) {
      return acc + Number(evaluation?.totalPixEfetivado || 0);
    }

    if (
      evaluation?.tipo === "Vale" &&
      evaluation?.totalValeEfetivado !== "(Não informado)"
    ) {
      return acc + Number(evaluation?.totalValeEfetivado || 0);
    }

    if (
      !evaluation?.tipo &&
      evaluation?.totalValeEfetivado !== "(Não informado)"
    ) {
      return acc + Number(evaluation?.totalDinheiroEfetivado || 0);
    }

    return acc + 0;
  };

  const realizadas = allEvaluationsFromCashier?.filter(item => item.finalizado)

  return {
    avaliacoes_realizadas: {
      qtd:
        realizadas?.length || 0
    },
    avaliacoes_finalizadas: {
      total: totalMoneyValue + totalVoucherValue + totalPixValue,
      qtd:
        allEvaluationsFromCashier?.filter(
          item => item.finalizado && !item.cancelado && !item.rejected
        )?.length || 0
    },
    avaliacoes_canceladas: {
      total: totalCanceledEvaluations?.reduce(formatValues, 0),
      qtd: totalCanceledEvaluations?.length || 0
    },
    avaliacoes_nao_aceitou_valor: {
      total: totalRejectedEvaluations
        ?.filter(evaluation =>
          evaluation?.motivo?.toLowerCase()?.includes("não aceitou")
        )
        ?.reduce(formatValues, 0),
      qtd:
        totalRejectedEvaluations?.filter(evaluation =>
          evaluation?.motivo?.toLowerCase()?.includes("não aceitou")
        )?.length || 0
    },
    avaliacoes_manuais: {
      total: realizadas
        ?.filter(
          evaluation =>
            evaluation?.manual
          // evaluation?.finalizado &&
          // !evaluation?.cancelado &&
          // !evaluation?.rejected
        )
        ?.reduce(formatValues, 0),
      qtd:
        realizadas?.filter(
          evaluation =>
            evaluation?.manual
          // evaluation?.finalizado &&
          // !evaluation?.cancelado &&
          // !evaluation?.rejected
        )?.length || 0
    },
    avaliacoes_via_sistema: {
      total: realizadas
        ?.filter(
          evaluation =>
            !evaluation?.manual
          // evaluation?.finalizado &&
          // !evaluation?.cancelado &&
          // !evaluation?.rejected
        )
        ?.reduce(formatValues, 0),
      qtd:
        realizadas?.filter(
          evaluation =>
            !evaluation?.manual
          // evaluation?.finalizado &&
          // !evaluation?.cancelado &&
          // !evaluation?.rejected
        )?.length || 0
    },
    avaliacoes_nao_enquadrou_perfil: {
      total: totalRejectedEvaluations
        ?.filter(evaluation =>
          evaluation?.motivo?.toLowerCase()?.includes("não enquadrou")
        )
        ?.reduce(formatValues, 0),
      qtd:
        totalRejectedEvaluations?.filter(evaluation =>
          evaluation?.motivo?.toLowerCase()?.includes("não enquadrou")
        )?.length || 0
    },
    avaliacoes_cliente_nao_esperou: {
      total: totalRejectedEvaluations
        ?.filter(evaluation =>
          evaluation?.motivo?.toLowerCase()?.includes("quis esperar")
        )
        ?.reduce(formatValues, 0),
      qtd:
        totalRejectedEvaluations?.filter(evaluation =>
          evaluation?.motivo?.toLowerCase()?.includes("quis esperar")
        )?.length || 0
    },
    avaliacoes_teste: {
      total: totalRejectedEvaluations
        ?.filter(evaluation =>
          evaluation?.motivo?.toLowerCase()?.includes("teste")
        )
        ?.reduce(formatValues, 0),
      qtd:
        totalRejectedEvaluations?.filter(evaluation =>
          evaluation?.motivo?.toLowerCase()?.includes("teste")
        )?.length || 0
    },
    avaliacoes_erro_sistema: {
      total: totalRejectedEvaluations
        ?.filter(evaluation =>
          evaluation?.motivo?.toLowerCase()?.includes("erro sistema")
        )
        ?.reduce(formatValues, 0),
      qtd:
        totalRejectedEvaluations?.filter(evaluation =>
          evaluation?.motivo?.toLowerCase()?.includes("erro sistema")
        )?.length || 0
    },
    avaliacoes_erro_operador: {
      total: totalRejectedEvaluations
        ?.filter(evaluation =>
          evaluation?.motivo?.toLowerCase()?.includes("erro operador")
        )
        ?.reduce(formatValues, 0),
      qtd:
        totalRejectedEvaluations?.filter(evaluation =>
          evaluation?.motivo?.toLowerCase()?.includes("erro operador")
        )?.length || 0
    },
    avaliacoes_outros: {
      total: totalRejectedEvaluations
        ?.filter(
          evaluation =>
            ![
              "Não aceitou o valor",
              "Não enquadrou no perfil da loja",
              "Cliente não quis esperar",
              'Teste',
              "Erro sistema",
              "Erro operador"
            ].includes(evaluation?.motivo)
        )
        ?.reduce(formatValues, 0),
      qtd:
        totalRejectedEvaluations?.filter(
          evaluation =>
            ![
              "Não aceitou o valor",
              "Não enquadrou no perfil da loja",
              "Cliente não quis esperar",
              'Teste',
              "Erro sistema",
              "Erro operador"
            ].includes(evaluation?.motivo)
        )?.length || 0
    }
  };
};

module.exports = CashiersClass
