const { Router } = require('express')
const router = Router()
const isAuth = require('../../../middlewares/isAuth')

const AddressClass = require('../../../controllers/address')
const AddressController = new AddressClass()

router.get('/', isAuth, AddressController.getAllStates)
router.get('/cities/:stateId', isAuth, AddressController.getCitiesByState)
router.get('/cities', isAuth, AddressController.getAllCities)

module.exports = router
