.container-modal-edit-produt {
  left: 50%;
  top: 45%;
  position: absolute;
  width: 900px;
  height: 500px;
  border-radius: 19px;
  background-color: white;
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  padding: 25px;
}
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(0deg, rgba(107, 103, 105, 0.3), rgba(75, 72, 74, 0.3));
  background-size: cover;
}
.header-modal {
  display: flex;
  justify-content: space-between;
  width: 100%;
}
.titulo-header {
  display: none;
  font-size: 15pt;
  color: #3f3d3d;
  margin-right: 669px;
}
.container-pesquisa {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
  height: 41px;
}
.input-search-modal {
  display: flex;
  justify-content: space-around;
  width: 100%;
}
.input-search-modal .input-modal {
  margin: 4px;
  margin-right: -2px;
  border: 1px solid #c9c9c9;
  height: 33px;
  width: 100%;
  margin-top: 5px;
  background-color: #fff;
  color: #666;
  text-align: start;
  padding-left: 16px;
  padding-right: 16px;
}
.input-search-modal-edit {
  background-color: gray;
  color: white;
  border-radius: 5px;
  cursor: pointer;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-start-start-radius: 0;
  border: 1px solid transparent;
  padding: 0 17px;
  height: 35px;
  margin-top: 5px;
  margin-right: 25px;
}
.input-search-modal-edit:hover {
  background-color: rgb(109, 96, 96);
  color: white;
}
.close-modal-modal-edit-teste {
  background-color: transparent;
  border: none;
  font-size: 25px;
  cursor: pointer;
  margin-right: 10px;
  margin-top: 10px;
}
.titulo_input {
  display: block;
  font-size: 8pt;
  color: #888;
  font-size: 9pt;
  font-weight: 400;
  padding: 12px 0 3px 3px;
}
.input-modal {
  margin: 4px;
  border: 1px solid #c9c9c9;
  height: 32px;
  width: 100%;
  margin-top: 5px;
  background-color: #fff;
  color: #666;
  text-align: start;
  padding-left: 16px;
  padding-right: 16px;
}
/*Tabs Modal */
.container-tabs {
  display: flex;
  position: relative;
  width: 100%;
  height: 390px;
  max-height: 450px;
  background: #fff;
  margin-top: 15px;
  word-break: break-all;
  border: 1px solid #c9c9c9;
  flex-direction: column;
}
.bloc-tabs {
  display: flex;
}
.tabs {
  padding: 5px;
  text-align: center;
  width: 100%;
  background: rgba(128, 128, 128, 0.075);
  cursor: pointer;
  border-bottom: 1px solid rgba(0, 0, 0, 0.274);
  box-sizing: content-box;
  position: relative;
  outline: none;
  text-decoration: none;
  color: black;
}
.tabs:not(:last-child) {
  border-right: 1px solid rgba(0, 0, 0, 0.274);
}
.active-tabs {
  background: white;
  border-bottom: 1px solid transparent;
}
.active-tabs::before {
  content: '';
  display: block;
  position: absolute;
  top: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: calc(100% + 2px);
  height: 5px;
  background: rgb(88, 147, 241);
}
span {
  border: none;
}
.content-tabs {
  flex-grow: 1;
}
.container-btn-tab {
  /* display: grid;
  grid-template-columns: auto auto auto; */
  display: grid;
  grid-template-columns: auto auto auto;
  align-items: center;
  justify-content: center;
  column-gap: 25px;
}
.content {
  /* background: white;
  padding: 15px;
  width: 95%;
  height: 70%;
  display: none; */
  background: white;
  padding: 15px;
  display: none;
}
.content h2 {
  padding: 0px 0 5px 0px;
}
.content hr {
  width: 100px;
  height: 2px;
  background: #222;
  margin-bottom: 5px;
}
.content p {
  width: 100%;
  height: 100%;
}
.active-content {
  display: block;
}
/*End Tabs modal*/
.container-btn-tab-pis {
  display: grid;
  grid-template-columns: auto auto auto auto auto;
}
.container-btn-tab-pis-confis {
  display: grid;
  grid-template-columns: auto auto auto;
}
/*Footer*/
.footer-modal-edit-product {
  display: flex;
  justify-content: flex-end;
  padding: 10px;
  max-height: 120px;
  margin-top: 1px;
}
.button-cancelar-product {
  background: #fff 0% 0% no-repeat padding-box !important;
  border: 1px solid #37a661 !important;
  border-radius: 8px !important;
  color: #37a661 !important;
  opacity: 1 !important;
  height: 40px !important;
  width: 15% !important;
  cursor: pointer !important;
  margin-left: 25px !important;
}
.button-cancelar-product:hover {
  background-color: #c1d3bf !important;
  border-color: #49b476 !important;
}
.button-salvar-product {
  background: green !important;
  border: 1px solid #37a661 !important;
  border-radius: 8px !important;
  color: #fff !important;
  height: 40px !important;
  width: 15% !important;
  cursor: pointer !important;
  margin-right: 19px !important;
  margin-left: 10px;
}
.button-salvar-product:hover {
  background-color: #4caf50 !important;
}
.container-mini-modal {
  left: 42%;
  top: 45%;
  position: absolute;
  width: 900px;
  height: 500px;
  border-radius: 19px;
  background-color: white;
  box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  padding: 25px;
}
.container-mini-modal ol {
  counter-reset: li;
  list-style: none;
  /* *list-style: decimal; */
  padding: 0;
  margin-bottom: 4em;
  text-shadow: 0 1px 0 rgba(255, 255, 255, 0.5);
  width: 900px;
  height: 28em;
  line-height: 2em;
  padding: 0;
  margin: 0;
  overflow: auto;
  overflow-x: hidden;
}
.container-mini-modal ol ol {
  margin: 0 0 0 2em;
}
.product-mini-container {
  position: relative;
  display: block;
  padding: 0.4em 0.4em 0.4em 2em;
  padding: 0.4em;
  align-items: center;
  margin: 0.5em 0;
  background: #ddd;
  color: #444;
  text-decoration: none;
  border-radius: 0.9em;
  transition: all 0.3s ease-out;
  cursor: pointer;
  height: 25px;
}
.product-mini-container:hover {
  background: #eee;
}

.modal-edit-input-formated {
  display: flex;
  align-items: stretch;
  border: 1px solid #ccc;
  height: 32px;
  width: 235px;
}

.modal-edit-input-formated span {
  display: flex;
  white-space: nowrap;
  background: #ccc;
  align-items: center;
  justify-content: center;
  padding: 0 6px;
}

.modal-edit-input-formated input {
  border: 0;
  height: unset;
  padding: 4px 6px;
}

@media only screen and (max-width: 1366px) {
  .container-modal-edit-produt {
    left: 50%;
    top: 55%;
    position: absolute;
    width: 900px;
    height: 500px;
    border-radius: 19px;
    background-color: white;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    padding: 25px;
  }
  .swal-wide {
    background: whitesmoke;
    width: 950px !important;
    height: 500px !important;
  }
  .container-mini-modal {
    left: 50%;
    top: 55%;
    position: absolute;
    width: 900px;
    height: 500px;
    border-radius: 19px;
    background-color: whitesmoke;
    box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;
    transform: translate(-50%, -50%);
    display: flex;
    flex-direction: column;
    padding: 25px;
  }
}
