"use strict";function ownKeys(object, enumerableOnly) {var keys = Object.keys(object);if (Object.getOwnPropertySymbols) {var symbols = Object.getOwnPropertySymbols(object);if (enumerableOnly) symbols = symbols.filter(function (sym) {return Object.getOwnPropertyDescriptor(object, sym).enumerable;});keys.push.apply(keys, symbols);}return keys;}function _objectSpread(target) {for (var i = 1; i < arguments.length; i++) {var source = arguments[i] != null ? arguments[i] : {};if (i % 2) {ownKeys(source, true).forEach(function (key) {_defineProperty(target, key, source[key]);});} else if (Object.getOwnPropertyDescriptors) {Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));} else {ownKeys(source).forEach(function (key) {Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));});}}return target;}function _defineProperty(obj, key, value) {if (key in obj) {Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });} else {obj[key] = value;}return obj;}function _classCallCheck(instance, Constructor) {if (!(instance instanceof Constructor)) {throw new TypeError("Cannot call a class as a function");}}function _defineProperties(target, props) {for (var i = 0; i < props.length; i++) {var descriptor = props[i];descriptor.enumerable = descriptor.enumerable || false;descriptor.configurable = true;if ("value" in descriptor) descriptor.writable = true;Object.defineProperty(target, descriptor.key, descriptor);}}function _createClass(Constructor, protoProps, staticProps) {if (protoProps) _defineProperties(Constructor.prototype, protoProps);if (staticProps) _defineProperties(Constructor, staticProps);return Constructor;}var bcrypt = require('bcryptjs');
var jwt = require('jsonwebtoken');
var jwtConfig = require('../../config/jwt');var _require =
require('mongoose'),Types = _require.Types;
var moment = require('moment');

var User = require('../../models/Usuario');
var PdvUser = require('../../models/UsuarioPDV');
var Caixa = require('../../models/Caixa');
var SettingsModel = require('../../models/Settings');
var RefreshTokenModel = require('../../models/RefreshToken');

var ObjectId = Types.ObjectId;var

UsersClass = /*#__PURE__*/function () {
  function UsersClass() {_classCallCheck(this, UsersClass);}_createClass(UsersClass, [{ key: "list", value: function list(

    req, res) {var filter, users;return regeneratorRuntime.async(function list$(_context) {while (1) {switch (_context.prev = _context.next) {case 0:
              filter = req.query;_context.next = 3;return regeneratorRuntime.awrap(
              PdvUser.find({
                $or: [_objectSpread({}, filter), { unidade: 'Todas' }] }));case 3:users = _context.sent;return _context.abrupt("return",

              res.json(users));case 5:case "end":return _context.stop();}}});} }, { key: "login", value: function login(


    req, res) {var _req$body, username, password, user, verifyPassword, requestUser, settings, token, resp, caixa;return regeneratorRuntime.async(function login$(_context2) {while (1) {switch (_context2.prev = _context2.next) {case 0:_req$body =
              req.body, username = _req$body.username, password = _req$body.password;_context2.next = 3;return regeneratorRuntime.awrap(
              User.findOne({ usuario: username }));case 3:user = _context2.sent;if (
              user) {_context2.next = 6;break;}return _context2.abrupt("return",
              res.status(401).send({
                message: 'Usuário ou senha inválidos' }));case 6:



              verifyPassword = false;_context2.prev = 7;_context2.next = 10;return regeneratorRuntime.awrap(


              bcrypt.compare(password, user.get('senha')));case 10:verifyPassword = _context2.sent;_context2.next = 15;break;case 13:_context2.prev = 13;_context2.t0 = _context2["catch"](7);case 15:if (


              verifyPassword) {_context2.next = 17;break;}return _context2.abrupt("return",
              res.status(401).send({
                message: 'Usuário ou senha inválidos' }));case 17:_context2.next = 19;return regeneratorRuntime.awrap(



              getUserInfo((user?._id)));case 19:requestUser = _context2.sent;_context2.next = 22;return regeneratorRuntime.awrap(
              SettingsModel?.find({ app: 'avaliacao' }).lean());case 22:settings = _context2.sent;
              token = jwt.sign(requestUser, jwtConfig.SECRET_JWT, { expiresIn: jwtConfig.expiresIn });

              // salvar o token token
              _context2.prev = 24;_context2.next = 27;return regeneratorRuntime.awrap(
              RefreshTokenModel.findOneAndUpdate(
              { id_user: requestUser?._id },
              {
                accessToken: [token],
                refreshToken: token,
                expiredAt: moment.utc().add({ hours: 12 }).toDate(),
                createdAt: moment.utc().toDate(),
                updatedAt: moment.utc().toDate() },

              { upsert: true }));case 27:resp = _context2.sent;_context2.next = 30;return regeneratorRuntime.awrap(


              Caixa.findOne({ dataFechamento: null, idUsuario: requestUser._id }).lean());case 30:caixa = _context2.sent;return _context2.abrupt("return",

              res.json({
                usuario: _objectSpread(_objectSpread({},
                requestUser), {}, {
                  settings: settings }),

                token: token,
                caixa: caixa }));case 34:_context2.prev = 34;_context2.t1 = _context2["catch"](24);


              console.log(_context2.t1);case 37:case "end":return _context2.stop();}}}, null, null, [[7, 13], [24, 34]]);} }, { key: "fetch", value: function fetch(



    req, res) {var user, findUser, caixa, settings;return regeneratorRuntime.async(function fetch$(_context3) {while (1) {switch (_context3.prev = _context3.next) {case 0:
              user = req.usuario;
              findUser = {};_context3.prev = 2;_context3.next = 5;return regeneratorRuntime.awrap(


              getUserInfo(user._id));case 5:findUser = _context3.sent;_context3.next = 11;break;case 8:_context3.prev = 8;_context3.t0 = _context3["catch"](2);

              console.log(_context3.t0);case 11:_context3.next = 13;return regeneratorRuntime.awrap(


              Caixa.findOne({ dataFechamento: null, idUsuario: findUser._id }).lean());case 13:caixa = _context3.sent;_context3.next = 16;return regeneratorRuntime.awrap(
              SettingsModel?.find({ app: 'avaliacao' }).lean());case 16:settings = _context3.sent;return _context3.abrupt("return",

              res.status(200).json({
                usuario: _objectSpread(_objectSpread({},
                findUser), {}, {
                  settings: settings }),

                caixa: caixa }));case 18:case "end":return _context3.stop();}}}, null, null, [[2, 8]]);} }, { key: "checkIsManager", value: function checkIsManager(



    req, res) {var _req$body2, user, password, authUser, condition, findUser, verifyPassword;return regeneratorRuntime.async(function checkIsManager$(_context4) {while (1) {switch (_context4.prev = _context4.next) {case 0:_req$body2 =
              req.body, user = _req$body2.user, password = _req$body2.password;
              authUser = req.usuario;if (!(

              authUser?.tipo === 'Administrador')) {_context4.next = 4;break;}return _context4.abrupt("return",
              res.status(401).json({
                status: false,
                error: 'Você não possui permissão!' }));case 4:



              condition = {
                codigo: user,
                tipo: {
                  $in: ['Administrador', 'Gerente'] } };_context4.next = 7;return regeneratorRuntime.awrap(



              PdvUser.findOne(condition).lean());case 7:findUser = _context4.sent;if (

              findUser) {_context4.next = 10;break;}return _context4.abrupt("return",
              res.status(401).send({
                status: false,
                error: 'Usuário ou senha inválidos' }));case 10:



              verifyPassword = false;_context4.prev = 11;_context4.next = 14;return regeneratorRuntime.awrap(


              bcrypt.compare(password, (findUser?.['senha'])));case 14:verifyPassword = _context4.sent;if (

              verifyPassword) {_context4.next = 17;break;}return _context4.abrupt("return",
              res.status(401).send({
                status: false,
                error: 'Usuário ou senha inválidos' }));case 17:return _context4.abrupt("return",



              res.sendStatus(200));case 20:_context4.prev = 20;_context4.t0 = _context4["catch"](11);return _context4.abrupt("return",

              res.status(401).send({
                status: false,
                error: 'Usuário ou senha inválidos' }));case 23:case "end":return _context4.stop();}}}, null, null, [[11, 20]]);} }, { key: "createUser", value: function createUser(




    req, res) {var _req$body3, usuario, senha, tipo, unidades, request;return regeneratorRuntime.async(function createUser$(_context5) {while (1) {switch (_context5.prev = _context5.next) {case 0:_req$body3 =
              req.body, usuario = _req$body3.usuario, senha = _req$body3.senha, tipo = _req$body3.tipo, unidades = _req$body3.unidades;

              request = {
                usuario: usuario,
                senha: senha,
                tipo: tipo,
                unidades: unidades };


              bcrypt.genSalt(10, function (erro, salt) {
                bcrypt.hash(request.senha, salt, function (erro, hash) {
                  if (erro) {
                    console.log(erro);
                  } else {
                    request.senha = hash;
                    new User(request).
                    save().
                    then(function (obj) {return res.send(obj);})["catch"](
                    function (erro) {return res.status(400).json(erro);});
                  }
                });
              });case 3:case "end":return _context5.stop();}}});} }, { key: "totemLogin", value: function totemLogin(


    req, res) {var _ref, username, password, user, verifyPassword, token;return regeneratorRuntime.async(function totemLogin$(_context6) {while (1) {switch (_context6.prev = _context6.next) {case 0:_ref =
              req?.body, username = _ref.username, password = _ref.password;_context6.next = 3;return regeneratorRuntime.awrap(

              User.findOne({ usuario: username, tipo: 'avaliacaoTotem' }).lean());case 3:user = _context6.sent;if (!(

              !user || user === null)) {_context6.next = 6;break;}return _context6.abrupt("return",
              res.status(404).json({
                status: false,
                error: 'Usuário ou senha inválidos.' }));case 6:_context6.next = 8;return regeneratorRuntime.awrap(



              bcrypt.compare(password, (user?.senha)));case 8:verifyPassword = _context6.sent;if (

              verifyPassword) {_context6.next = 11;break;}return _context6.abrupt("return",
              res.status(404).json({
                status: false,
                error: 'Usuário ou senha inválidos.' }));case 11:



              token = jwt.sign(user, jwtConfig.SECRET_JWT, { expiresIn: '30d' });_context6.next = 14;return regeneratorRuntime.awrap(

              RefreshTokenModel.findOneAndUpdate(
              { id_user: user?._id },
              {
                accessToken: [token],
                refreshToken: token,
                expiredAt: moment.utc().add({ days: 30 }).toDate(),
                createdAt: moment.utc().toDate(),
                updatedAt: moment.utc().toDate() },

              { upsert: true }));case 14:return _context6.abrupt("return",


              res.status(200).json(_objectSpread(_objectSpread({},
              user), {}, {
                token: token })));case 15:case "end":return _context6.stop();}}});} }]);return UsersClass;}();




function getUserInfo(id) {var _enterprise, _$project;var user;return regeneratorRuntime.async(function getUserInfo$(_context7) {while (1) {switch (_context7.prev = _context7.next) {case 0:
          id = ObjectId(id);_context7.next = 3;return regeneratorRuntime.awrap(

          User.aggregate([
          {
            $match: {
              _id: id } },


          {
            $unwind: {
              path: '$unidades',
              preserveNullAndEmptyArrays: true } },


          {
            $unwind: {
              path: '$permission',
              preserveNullAndEmptyArrays: true } },


          {
            $lookup: {
              localField: 'unidades.cnpj',
              from: 'empresas',
              foreignField: 'cnpj',
              as: 'enterprise' } },


          {
            $unwind: {
              path: '$enterprise',
              preserveNullAndEmptyArrays: true } },


          {
            $project: (_$project = {
              usuario: 1,
              senha: 1,
              tipo: 1 }, _defineProperty(_$project, "senha",
            1), _defineProperty(_$project, "permission",
            1), _defineProperty(_$project, "enterprise", (_enterprise = {

              nomeUnidade: 1,
              cnpj: 1,
              codUnd: 1,
              ativo: 1,
              endereco: 1,
              numero: 1,
              bairro: 1,
              uf: 1,
              cep: 1 }, _defineProperty(_enterprise, "cnpj",
            1), _defineProperty(_enterprise, "telefone",
            1), _defineProperty(_enterprise, "celular",
            1), _defineProperty(_enterprise, "cidade",
            1), _defineProperty(_enterprise, "nomeReduzido",
            1), _defineProperty(_enterprise, "codCidade",
            1), _defineProperty(_enterprise, "plus",
            1), _defineProperty(_enterprise, "beta",
            1), _enterprise)), _$project) }]));case 3:user = _context7.sent;return _context7.abrupt("return",





          user?.[0] || {});case 5:case "end":return _context7.stop();}}});}


module.exports = UsersClass;