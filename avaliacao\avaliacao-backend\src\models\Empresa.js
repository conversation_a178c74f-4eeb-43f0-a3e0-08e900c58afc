const { Schema, model } = require('mongoose')

// Definindo Schema
const EmpresaSchema = Schema({
  codUnd: {
    type: String,
  },
  ativo: {
    type: Boolean,
  },
  razaoSocial: {
    type: String,
  },
  nomeReduzido: {
    type: String,
  },
  endereco: {
    type: String,
  },
  numero: {
    type: String,
  },
  bairro: {
    type: String,
  },
  codUf: {
    type: Number,
  },
  uf: {
    type: String,
  },
  codCidade: {
    type: String,
  },
  cidade: {
    type: String,
  },
  cep: {
    type: String,
  },
  cnpj: {
    type: String,
  },
  ie: {
    type: String,
  },
  telefone: {
    type: String,
  },
  celular: {
    type: String,
  },
  email: {
    type: String,
  },
  dtInauguracao: {
    type: Date,
  },
  vlrRoyalties: {
    type: Number,
  },
  tipoNota: {
    type: String,
  },
  visualizar: {
    type: Boolean,
  },
  dataCriacao: {
    type: Date,
  },
  dataAlteracao: {
    type: Date,
  },
  emitirBoleto: {
    type: Boolean,
  },
  contingencyNFEMode: {
    type: Boolean,
    default: false,
  },
  blocked: {
    type: Boolean,
    default: false,
  },
  blockMessage: {
    type: String,
    default: null,
  },
  publicIP: {
    type: String,
  },
  ipUpdatedAt: {
    type: Date,
    default: Date.now(),
  },
  validateIP: {
    type: Boolean,
  }
})

// Definindo collection
const Empresa = model('empresa', EmpresaSchema)
module.exports = Empresa
