const { Schema, model } = require('mongoose')
const { getCurrentTime } = require('../utils')

const currentTime = getCurrentTime()

// Definindo Schema
const UsuarioSchema = Schema({
  usuario: {
    type: String,
    require: true,
    unique: true,
  },
  senha: {
    type: String,
    require: true,
  },
  tipo: {
    type: String,
    require: true,
  },
  unidades: {
    type: [Object],
    require: true,
  },
  api_ip: {
    type: String,
    default: 'http://127.0.0.1',
  },
  create_at: {
    type: Date,
    default: currentTime,
  },
  update_at: {
    type: Date,
    default: null,
  },
  delete_at: {
    type: Date,
    default: null,
  },
})

// Definindo collection
const Usuario = model('usuario', UsuarioSchema)
module.exports = Usuario
