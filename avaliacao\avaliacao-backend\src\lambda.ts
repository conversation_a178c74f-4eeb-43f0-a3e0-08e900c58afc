import serverless from 'serverless-http'
import dotenv from 'dotenv'
const Sentry = require('@sentry/aws-serverless')
dotenv.config()

require('./instrument')
import { getApp } from './app'
import { loadSecrets } from './services/secretsLoader'

const isProduction = ['beta', 'production', 'test', 'homolog'].includes(process.env.NODE_ENV!)

let cachedApp: any

const handler = async (event: any, context: any) => {
  if (!cachedApp) {
    await loadSecrets()
    context.callbackWaitsForEmptyEventLoop = false
    const app = await getApp()
    cachedApp = serverless(app, { provider: 'aws' })
  }

  const result = (await cachedApp(event, context)) as any
  return result
}

export const run = isProduction ? Sentry.wrapHandler(handler) : handler
