import { BsInfoCircle } from "react-icons/bs";
import "./styles.css";
import { Tooltip } from "react-tooltip";
import { InputHTMLAttributes } from "react";

interface InputProps {
  label?: string;
  id: string;
  type: "text" | "email" | "password" | "number";
  value: string;
  onChange: (value: string) => void;
  tooltip?: string;
  max?: number;
  min?: number;
  maxLength?: number;
}

const Input: React.FC<InputProps> = ({
  label,
  id,
  type,
  value,
  onChange,
  tooltip,
  max,
  min,
  maxLength,
}) => {
  return (
    <div className="input-component-container">
      <div style={{ display: "flex", alignItems: "flex-start", gap: ".5rem" }}>
        {label && <label htmlFor={id}>{label}</label>}
        {tooltip && (
          <>
            <BsInfoCircle
              id="info-tooltip"
              data-tooltip-content={tooltip}
              size={12}
              color="peru"
            />
          </>
        )}
        <Tooltip anchorId="info-tooltip" />
      </div>
      <input
        type={type}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        id={id}
        max={max}
        min={min}
        maxLength={maxLength}
      />
    </div>
  );
};

export default Input;
