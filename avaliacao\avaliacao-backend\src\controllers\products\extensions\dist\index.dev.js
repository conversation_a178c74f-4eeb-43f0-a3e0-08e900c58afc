'use strict'

var Produto = require('../../../models/Produto')

var Nivel1 = require('../../../models/Nivel1')

var Nivel2 = require('../../../models/Nivel2')

var Nivel3 = require('../../../models/Nivel3')

var Nivel4 = require('../../../models/Nivel4')

function geraProduto(req, nivelAtual) {
  var descricao,
    pesquisa,
    coeficiente,
    vlrCusto,
    vlrVenda,
    produto,
    vlrMin,
    nivel,
    contador,
    codigo,
    codigoBarra
  return regeneratorRuntime.async(function geraProduto$(_context) {
    while (1) {
      switch ((_context.prev = _context.next)) {
        case 0:
          descricao = req && req.descricao ? req.descricao : '' // parte que verifica se é pesquisa

          nivel = req.nivel || 'nivel'.concat(req.nvl)

          if (!req.pesquisa || req.pesquisa === 'não') {
            pesquisa = false
            coeficiente = null
            vlrCusto = req.valor
            vlrVenda = req.valorMax
            vlrMin = req.valorMin
          } else {
            pesquisa = true
            coeficiente = req.coeficiente
            vlrCusto = null
            vlrVenda = null
            vlrMin = null
          }

          _context.next = 5
          return regeneratorRuntime.awrap(Produto.countDocuments())

        case 5:
          contador = _context.sent
          codigo = contador
          _context.next = 9
          return regeneratorRuntime.awrap(geraCod())

        case 9:
          codigoBarra = _context.sent
          _context.t0 = nivel
          _context.next =
            _context.t0 === 'nivel1'
              ? 13
              : _context.t0 === 'nivel2'
              ? 15
              : _context.t0 === 'nivel3'
              ? 20
              : _context.t0 === 'nivel4'
              ? 28
              : 39
          break

        case 13:
          produto = {
            descricao: descricao,
            nivel1: nivelAtual._id,
            vlrCusto: vlrCusto,
            vlrVenda: vlrVenda,
            vlrMin: vlrMin,
            codigo: codigo,
            codBarras: codigoBarra,
            pesquisa: pesquisa,
            coeficiente: coeficiente,
            favorito: false,
            peso: req.peso,
          }
          return _context.abrupt('return', produto)

        case 15:
          _context.next = 17
          return regeneratorRuntime.awrap(Nivel1.findById(nivelAtual.chave))

        case 17:
          nvl1 = _context.sent
          produto = {
            descricao: ''.concat(nvl1.descricao, ' - ').concat(descricao),
            nivel1: nivelAtual.chave,
            nivel2: nivelAtual._id,
            vlrCusto: vlrCusto,
            vlrVenda: vlrVenda,
            vlrMin: vlrMin,
            codigo: codigo,
            codBarras: codigoBarra,
            pesquisa: pesquisa,
            coeficiente: coeficiente,
            favorito: false,
            peso: req.peso,
          }
          return _context.abrupt('return', produto)

        case 20:
          _context.next = 22
          return regeneratorRuntime.awrap(Nivel2.findById(nivelAtual.chave))

        case 22:
          nvl2 = _context.sent
          _context.next = 25
          return regeneratorRuntime.awrap(Nivel1.findById(nvl2.chave))

        case 25:
          nvl1 = _context.sent
          produto = {
            descricao: ''
              .concat(nvl1.descricao, ' - ')
              .concat(nvl2.descricao, ' - ')
              .concat(descricao),
            nivel1: nvl2.chave,
            nivel2: nivelAtual.chave,
            nivel3: nivelAtual._id,
            vlrCusto: vlrCusto,
            vlrVenda: vlrVenda,
            vlrMin: vlrMin,
            codigo: codigo,
            codBarras: codigoBarra,
            pesquisa: pesquisa,
            coeficiente: coeficiente,
            favorito: false,
            peso: req.peso,
          }
          return _context.abrupt('return', produto)

        case 28:
          _context.next = 30
          return regeneratorRuntime.awrap(Nivel3.findById(nivelAtual.chave))

        case 30:
          nvl3 = _context.sent
          _context.next = 33
          return regeneratorRuntime.awrap(Nivel2.findById(nvl3.chave))

        case 33:
          nvl2 = _context.sent
          _context.next = 36
          return regeneratorRuntime.awrap(Nivel1.findById(nvl2.chave))

        case 36:
          nvl1 = _context.sent
          produto = {
            descricao: ''
              .concat(nvl1.descricao, ' - ')
              .concat(nvl2.descricao, ' - ')
              .concat(nvl3.descricao, ' - ')
              .concat(descricao),
            nivel1: nvl1._id,
            nivel2: nvl2._id,
            nivel3: nvl3._id,
            nivel4: nivelAtual._id,
            vlrCusto: vlrCusto,
            vlrVenda: vlrVenda,
            vlrMin: vlrMin,
            peso: req.peso,
            codigo: codigo,
            codBarras: codigoBarra,
            pesquisa: pesquisa,
            coeficiente: coeficiente,
            favorito: false,
          }
          return _context.abrupt('return', produto)

        case 39:
          return _context.abrupt('break', 40)

        case 40:
        case 'end':
          return _context.stop()
      }
    }
  })
}

function geraCod() {
  var contador,
    codigo,
    codigoBarra,
    um,
    dois,
    tres,
    quatro,
    cinco,
    seis,
    sete,
    oito,
    nove,
    dez,
    onze,
    doze,
    soma,
    multiplo,
    resultado
  return regeneratorRuntime.async(function geraCod$(_context2) {
    while (1) {
      switch ((_context2.prev = _context2.next)) {
        case 0:
          _context2.next = 2
          return regeneratorRuntime.awrap(Produto.countDocuments())

        case 2:
          contador = _context2.sent
          codigo = contador++ // gera o codigo de barras essa parte

          codigoBarra = 123000000000
          codigoBarra += Number(codigo)
          codigoBarra = codigoBarra.toString()
          um = Number(codigoBarra.substring(0, 1))
          dois = Number(codigoBarra.substring(1, 2))
          tres = Number(codigoBarra.substring(2, 3))
          quatro = Number(codigoBarra.substring(3, 4))
          cinco = Number(codigoBarra.substring(4, 5))
          seis = Number(codigoBarra.substring(5, 6))
          sete = Number(codigoBarra.substring(6, 7))
          oito = Number(codigoBarra.substring(7, 8))
          nove = Number(codigoBarra.substring(8, 9))
          dez = Number(codigoBarra.substring(9, 10))
          onze = Number(codigoBarra.substring(10, 11))
          doze = Number(codigoBarra.substring(11, 12))
          soma =
            um * 1 +
            dois * 3 +
            tres * 1 +
            quatro * 3 +
            cinco * 1 +
            seis * 3 +
            sete * 1 +
            oito * 3 +
            nove * 1 +
            dez * 3 +
            onze * 1 +
            doze * 3
          soma = soma.toString()

          if (soma.slice(1) === '0') {
            codigoBarra += '0'
          } else {
            multiplo = Number(soma.slice(0, -1))
            multiplo = multiplo * 10 + 10
            resultado = multiplo - soma
            codigoBarra += resultado
          }

          return _context2.abrupt('return', codigoBarra)

        case 23:
        case 'end':
          return _context2.stop()
      }
    }
  })
}

module.exports = {
  geraProduto: geraProduto,
}
