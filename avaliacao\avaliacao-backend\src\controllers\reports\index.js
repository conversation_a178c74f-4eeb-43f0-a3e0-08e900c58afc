const moment = require('moment-timezone')
const EvaluationModel = require('../../models/Avaliacao')
const { generateExcelFile } = require('../../modules/excel/services/generateExcelFile')
const fs = require('fs')
const fsExtra = require('fs-extra')

class ReportsClass {
  async exportEvaluationReport(req, res) {
    try {
      const evaluations =
        (await EvaluationModel.aggregate([
          {
            $match: {
              cnpj: req.body.cnpj,
              dataInicio: { $gte: moment.utc(req.body.startDate).toDate() },
              dataFinal: { $lte: moment.utc(req.body.finalDate).add({ days: 1 }).toDate() },
            },
          },
          {
            $lookup: {
              localField: 'cpf',
              from: 'clientes',
              foreignField: 'cpf',
              as: 'client',
            },
          },
          {
            $unwind: {
              path: '$client',
              preserveNullAndEmptyArrays: true,
            },
          },
          {
            $project: {
              dataInicio: 1,
              dataFinal: 1,
              tipo: 1,
              motivo: 1,
              totalValeEfetivado: 1,
              totalDinheiroEfetivado: 1,
              totalPixEfetivado: 1,
              finalizado: 1,
              rejected: 1,
              cancelado: 1,
              client: {
                nome: 1,
                cpf: 1,
                celular: 1,
                rua: 1,
                numero: 1,
                bairro: 1,
                cidade: 1,
                estado: 1,
              },
            },
          },
        ])) || []

      if (!evaluations?.length) {
        return res.status(400).json({
          status: false,
          error: 'Não há avaliações na data selecionada.',
        })
      }

      const formattedEvaluations = evaluations?.length
        ? evaluations?.map(evaluation => {
            let newEvaluationData = {
              ...evaluation,
              ...evaluation.client,
            }
            delete newEvaluationData.client
            delete newEvaluationData._id

            if (!req.body.dataInicio) delete newEvaluationData.dataInicio
            if (!req.body.dataFinal) delete newEvaluationData.dataFinal
            if (!req.body.tipo) delete newEvaluationData.tipo
            if (!req.body.motivo) delete newEvaluationData.motivo
            if (req.body.valorTotal) {
              if (evaluation?.tipo === 'Dinheiro') {
                newEvaluationData.valorTotal = evaluation?.totalDinheiroEfetivado
              }

              if (evaluation?.tipo === 'Vale') {
                newEvaluationData.valorTotal = evaluation?.totalValeEfetivado
              }

              if (evaluation?.tipo === 'Pix') {
                newEvaluationData.valorTotal = evaluation?.totalPixEfetivado
              }
            }
            if (!req.body.nome) delete newEvaluationData.nome
            if (!req.body.cpf) delete newEvaluationData.cpf
            if (!req.body.celular) delete newEvaluationData.celular
            if (!req.body.rua) delete newEvaluationData.rua
            if (!req.body.numero) delete newEvaluationData.numero
            if (!req.body.bairro) delete newEvaluationData.bairro
            if (!req.body.cidade) delete newEvaluationData.cidade
            if (!req.body.estado) delete newEvaluationData.estado
            if (req.body.status) {
              if (evaluation?.finalizado && !evaluation?.cancelado && !evaluation?.rejected) {
                newEvaluationData.status = 'Finalizado'
              }

              if (evaluation?.finalizado && evaluation?.cancelado && !evaluation?.rejected) {
                newEvaluationData.status = 'Cancelado'
              }

              if (evaluation?.finalizado && evaluation?.cancelado && evaluation?.rejected) {
                newEvaluationData.status = 'Rejeitado'
              }
            }

            delete newEvaluationData.totalValeEfetivado
            delete newEvaluationData.totalDinheiroEfetivado
            delete newEvaluationData.totalPixEfetivado
            delete newEvaluationData.finalizado
            delete newEvaluationData.cancelado
            delete newEvaluationData.rejected

            return newEvaluationData
          })
        : []

      const excelFilePath = await generateExcelFile(formattedEvaluations)
      const file = fs.readFileSync(excelFilePath)
      const file64 = Buffer.from(file).toString('base64')

      await fsExtra.emptyDir('../../../tmp')

      return res.status(200).json({
        file: file64,
      })
    } catch (error) {
      console.log(error)
      return res.status(400).json({
        status: false,
        error: 'Erro ao pesquisar avaliações.',
      })
    }
  }
}

module.exports = ReportsClass
