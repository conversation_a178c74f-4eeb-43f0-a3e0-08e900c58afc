import { Component } from 'react'
import { AuthContext } from '../../context/auth'
import moment from 'moment'

class BoxCupom extends Component {
  static contextType = AuthContext
  constructor(props) {
    super(props)
  }

  render() {
    const {
      cnpj,
      empresa,
      openingBoxDate,
      contentPrint,
      hasCashierInfo = true,
      isContentToPrint,
    } = this.props

    const { user } = this.context

    return (
      <div id="ticket">
        <header
          style={{
            marginBottom: '2rem',
            marginTop: '2rem',
            textAlign: 'center',
          }}
        >
          <div>
            <div className="company">
              <h1>{empresa?.nomeReduzido}</h1>
              <p>CNPJ: {cnpj}</p>
              <p>{empresa?.endereco},</p>
              <p>{empresa?.numero}</p>
              <p>{empresa?.bairro}</p>
              <p>
                {empresa?.enderecoCidade} TEL: {empresa?.telefone}
              </p>
              <p>------------------------------------------</p>
            </div>
            {isContentToPrint.props.id === 'contentTicket' ? (
              <div></div>
            ) : (
              <div className="stateInformation">
                <p>Abertura: {openingBoxDate}</p>
                <p>Fechamento: {moment.utc().subtract({ hours: 3 }).format('DD/MM/YYYY HH:mm:ss')}</p>
                <p>Operador: {user?.usuario}</p>
              </div>
            )}
          </div>
        </header>
        <br />
        {contentPrint}
        <br />
      </div>
    )
  }
}

export default BoxCupom
