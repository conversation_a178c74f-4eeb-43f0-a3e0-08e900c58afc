const express = require('express')
const routesV1 = require('./routes/v1/routes')
const routesV2 = require('./routes/v2/routes')
const isAuth = require('./middlewares/isAuth')
const verifyTmpDir = require('./utils/verifyTmpDir')
const morgan = require('morgan')
const bodyParser = require('body-parser')

let app

const initializeExpressApp = async () => {
  require('./database/index')

  app = express()

  const { VERSION } = process.env
  const ENVS_TO_CORS = ['local']

  if (ENVS_TO_CORS.includes(process.env.NODE_ENV)) {
    const cors = require('cors')
    app.use(cors())
  }

  app.use(morgan('dev'))
  app.use(bodyParser.urlencoded({ extended: true, limit: '50mb' }))
  app.use(bodyParser.json({ limit: '50mb' }))
  app.use(bodyParser.text({ limit: '10mb' }))

  app.use('/api/v2', routesV2)
  app.use('/api', routesV1)
  app.get('/health', isAuth, (_, res) => res.status(200).json({ v: VERSION }))
  app.get('/', (_, res) => res.sendStatus(200))

  verifyTmpDir()

  return app
}

const getApp = async () => {
  if (!app) {
    await initializeExpressApp()
  }
  return app
}

module.exports = { getApp }
