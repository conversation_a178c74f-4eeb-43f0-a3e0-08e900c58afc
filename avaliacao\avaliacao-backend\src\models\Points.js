const { model, Schema } = require("mongoose");

const PointsSchema = new Schema({
  name: {
    type: String,
  },
  cpf: {
    type: String,
  },
  points: {
    type: Number,
  },
  type: {
    type: String,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  expiredAt: {
    type: Date,
    default: null,
  },
  origin: {
    type: String,
  },
  isCanceled: {
    type: Boolean,
    default: false,
  },
  referenceId: {
    type: String,
  },
  cnpj: {
    type: String,
  },
});

const PointsModel = model("point", PointsSchema);
module.exports = PointsModel
