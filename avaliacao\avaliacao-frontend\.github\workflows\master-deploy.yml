name: Deploy master

on:
  push:
    branches: [master]

jobs:
  wait_for_previous_deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Check for Running Deployments
        id: check_deploy
        run: |
          while : ; do
            RUNNING_WORKFLOWS=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            "https://api.github.com/repos/${{ github.repository }}/actions/runs?branch=master&status=in_progress" \
            | jq -r '.workflow_runs[] | select(.name == "Deploy master") | .id')

            if [ -z "$RUNNING_WORKFLOWS" ]; then
              echo "No running deployments found. Proceeding with new deployment."
              break
            else
              echo "Found a running deployment with ID $RUNNING_WORKFLOWS. Waiting for it to finish..."
              sleep 30
            fi
          done

  build_deploy:
    runs-on: ubuntu-latest
    needs: wait_for_previous_deploy
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_AMPLIFY }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_AMPLIFY }}
      AWS_DEFAULT_REGION: sa-east-1
      AWS_DEFAULT_OUTPUT: json
      APP_ID: ${{ secrets.AWS_AMPLIFY_APP_ID }}
      BRANCH_NAME: master
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Fetch PR Title and Author
        id: fetch_pr_info
        run: |
          PR_INFO=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
          https://api.github.com/repos/${{ github.repository }}/commits/${{ github.sha }}/pulls \
          | jq -r '.[0]')

          PR_NUMBER=$(echo "$PR_INFO" | jq -r '.number')

          if [ "$PR_NUMBER" = "null" ]; then
            echo "PR_TITLE=No associated PR" >> $GITHUB_ENV
            echo "PR_AUTHOR=Unknown" >> $GITHUB_ENV
          else
            PR_TITLE=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            https://api.github.com/repos/${{ github.repository }}/pulls/$PR_NUMBER \
            | jq -r '.title')
            PR_AUTHOR=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            https://api.github.com/repos/${{ github.repository }}/pulls/$PR_NUMBER \
            | jq -r '.user.login')
            echo "PR_TITLE=$PR_TITLE" >> $GITHUB_ENV
            echo "PR_AUTHOR=$PR_AUTHOR" >> $GITHUB_ENV
          fi

      - name: AWS Amplify Start Job
        id: StartJob
        run: echo "jobId=$(aws amplify start-job --app-id $APP_ID --branch-name $BRANCH_NAME --job-type RELEASE | jq -r '.jobSummary.jobId')" >> $GITHUB_OUTPUT
      - name: AWS Amplify Build
        env:
          JOB_ID: ${{ steps.StartJob.outputs.jobId }}
        run: |
          while [[ "$(aws amplify get-job --app-id $APP_ID --branch-name $BRANCH_NAME --job-id $JOB_ID | jq -r '.job.summary.status')" =~ ^(PENDING|RUNNING)$ ]]; do sleep 1; done
          RESULT="$(aws amplify get-job --app-id $APP_ID --branch-name $BRANCH_NAME --job-id $JOB_ID | jq -r '.job.steps | .[] | select(.stepName == "BUILD") | .status')"
          echo "Build result: $RESULT"
          LOG_URL="$(aws amplify get-job --app-id $APP_ID --branch-name $BRANCH_NAME --job-id $JOB_ID | jq -r '.job.steps | .[] | select(.stepName == "BUILD") | .logUrl')"
          if [[ $RESULT == "SUCCEED" ]]; then
            echo "Build succeeded."
            echo "Step logs in Amplify:"
            curl $LOG_URL
          else
            echo "Build failed."
            echo "Step logs in Amplify:"
            curl $LOG_URL
            exit 1
          fi
      - name: AWS Amplify Deploy
        env:
          JOB_ID: ${{ steps.StartJob.outputs.jobId }}
        run: |
          RESULT="$(aws amplify get-job --app-id $APP_ID --branch-name $BRANCH_NAME --job-id $JOB_ID | jq -r '.job.steps | .[] | select(.stepName == "DEPLOY") | .status')"
          echo "Deploy result: $RESULT"
          LOG_URL="$(aws amplify get-job --app-id $APP_ID --branch-name $BRANCH_NAME --job-id $JOB_ID | jq -r '.job.steps | .[] | select(.stepName == "DEPLOY") | .logUrl')"
          if [[ $RESULT == "SUCCEED" ]]; then
            echo "Deploy succeeded."
            echo "Step logs in Amplify:"
            curl $LOG_URL
          else
            echo "Deploy failed."
            echo "Step logs in Amplify:"
            curl $LOG_URL
            exit 1
          fi

      - name: Send success notification to Discord
        if: success()
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
          PR_TITLE: ${{ env.PR_TITLE }}
          PR_AUTHOR: ${{ env.PR_AUTHOR }}
        uses: Ilshidur/action-discord@master
        with:
          args: |
            Deploy efetuado com sucesso (avaliacao-frontend / master)
            PR: ${{ env.PR_TITLE  }}
            Autor da PR: ${{ env.PR_AUTHOR }}

  error:
    runs-on: ubuntu-latest
    needs: build_deploy
    if: failure()
    steps:
      - name: Notify Discord on failure
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
          PR_TITLE: ${{ env.PR_TITLE }}
          PR_AUTHOR: ${{ env.PR_AUTHOR }}
        uses: Ilshidur/action-discord@master
        with:
          args: |
            Deploy falhou (avaliacao-frontend / master)
            PR: ${{ env.PR_TITLE }}
            Autor da PR: ${{ env.PR_AUTHOR }}

  notify_cancel:
    runs-on: ubuntu-latest
    if: ${{ always() && needs.deploy.result == 'cancelled' }}
    steps:
      - name: Notify Discord on cancellation
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
          PR_TITLE: ${{ env.PR_TITLE }}
          PR_AUTHOR: ${{ env.PR_AUTHOR }}
        uses: Ilshidur/action-discord@master
        with:
          args: |
            Deploy foi cancelado (avaliacao-frontend / master)
            PR: ${{ env.PR_TITLE }}
            Autor da PR: ${{ env.PR_AUTHOR }}
