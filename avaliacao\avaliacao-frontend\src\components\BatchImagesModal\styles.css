.batch-images-modal-container {
  display: flex;
  flex-flow: column;
  gap: 1.5rem;
  padding: 2rem;
  background-color: #FFF;
  width: 18%;
  border-radius: 5px;
}

.batch-images-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.type-options {
  display: flex;
  align-items: center;
  gap: 30px;
}

.type-options section {
  display: flex;
  align-items: center;
  gap: .5rem;
}

.batch-images-buttons-container {
  width: 100%;
  display: flex;
  gap: 20px;
  align-items: center;
}

.batch-images-buttons-container button {
  padding: .5rem;
  width: 100%;
  border: none;
  outline: none;
  border-radius: .4rem;
  color: #FFF;
}

.batch-images-buttons-container .btn-confirm {
  background-color: peru;
}

.batch-images-buttons-container .btn-confirm:disabled {
  background-color: #CCC;
  cursor: not-allowed;
}

.batch-images-buttons-container .btn-cancel {
  background-color: #555;
}

button {
  cursor: pointer;
  transition: all 0.2s;
}

button:hover {
  opacity: 0.8;
}

.preview-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.preview-image {
  width: 100%;
  height: 150px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s;
}

.preview-image:hover {
  opacity: 0.8;
}

.img-viewer .styles-module_navigation__1pqAE {
  opacity: 1;
}

.img-viewer .styles-module_close__2I1sI {
  opacity: 1;
}

@media (max-width: 1281px) {
  .batch-images-modal-container {
    width: 50%;
  }
}

@media (max-width: 980px) {
  .batch-images-modal-container {
    width: 100%;
  }

  .type-options {
    display: flex;
    flex-flow: column;
    align-items: flex-start;
    gap: 10px;
  }
}

@media (max-width: 430px) {
  .preview-row {
    grid-template-columns: repeat(2, 1fr);
  }

  .preview-image {
    height: 125px;
  }
}


