const { S3, config } = require('aws-sdk')
const fs = require('fs')

const { APP_AWS_REGION, APP_AWS_BUCKET, APP_AWS_KEY_ID, APP_AWS_ACCESS_KEY } = process.env

config.update({
  accessKeyId: APP_AWS_KEY_ID,
  secretAccessKey: APP_AWS_ACCESS_KEY,
  region: APP_AWS_REGION,
})
const s3 = new S3()

const uploadToS3 = async (image, pathname) => {
  try {
    const fileContent = fs.readFileSync(image?.path)
    const Key = `${pathname}/${image?.filename}`

    const uploadedImage = await s3
      .upload({
        Bucket: APP_AWS_BUCKET,
        Body: fileContent,
        Key,
        ACL: 'public-read',
      })
      .promise()

    return uploadedImage
  } catch (err) {
    console.log(err)
  }
}

const uploadToS3FromBuffer = async (image, pathname) => {
  try {
    const Key = `${pathname}/${image?.originalname}`
    const uploadedImage = await s3
      .upload({
        Bucket: APP_AWS_BUCKET,
        Body: image?.buffer,
        Key,
        ACL: 'public-read',
      })
      .promise()

    return uploadedImage
  } catch (err) {
    console.log(err)
  }
}

module.exports = {
  uploadToS3,
  uploadToS3FromBuffer,
}
