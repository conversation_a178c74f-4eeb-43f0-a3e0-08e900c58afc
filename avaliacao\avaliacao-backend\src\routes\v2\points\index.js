const { Router } = require('express')
const isAuth = require('../../../middlewares/isAuth')
const Points = require('../../../controllers/points')

const PointsRoutes = Router()
const PointsController = new Points()

PointsRoutes.post('/create', isAuth, PointsController.createPoints)
PointsRoutes.get('/getAllPointsByClient/:cpf', isAuth, PointsController.getAllPointsByClient)
PointsRoutes.get('/getPointsFromEvaluation/:evaluationId', isAuth, PointsController.getPointsFromEvaluation)

module.exports = PointsRoutes
