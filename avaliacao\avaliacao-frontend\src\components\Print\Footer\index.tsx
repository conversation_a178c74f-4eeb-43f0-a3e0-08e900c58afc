import QRCode from "qrcode.react";

const PrintFooter: React.FC = () => {
  const footerText = [
    '-----------------------------------------------',
    '✳️ SAC CRESCI E PERDI',
    'Serviço de Atendimento ao Cliente - Envie',
    'mensagem pelo WhatsApp para (11) 94353-8000',
    '-----------------------------------------------',
    // 'PONTOS CRESCI E PERDI',
    // 'Agora ao comprar e desapegar com a',
    // 'cresci e perdi, você acumula pontos',
    // 'e poderá utiliza-los como',
    // 'pagamento de suas compras.',
    // '-----------------------------------------------',
    'SEJA UM FRANQUEADO 🚀',
    'Faça parte dessa história e empreenda com a',
    'franquia líder no segmento de moda circular do',
    'Brasil. Saiba mais sobre nossos modelos de',
    'franquias!',
    'Entre em Contato com nossa Equipe de Expansão',
    'pelo WhatsApp (11) 99447-3008 ou acesse',
    'www.crescieperdi.com.br',
  ]

  return (
    <div style={{ display: 'block', textAlign: "center", fontFamily: "'Poppins', sans-serif", fontSize: "10px", fontWeight: 'bold' }}>
      {footerText?.map((text, index) => (
        <p key={index} style={{ margin: 0 }}>{text}</p>
      ))}
      {/* <QRCode value={'https://appcrescieperdi.dfcom.com.br/'} size={90}/> */}
      <p>.</p>
    </div>
  )
}

export default PrintFooter;
