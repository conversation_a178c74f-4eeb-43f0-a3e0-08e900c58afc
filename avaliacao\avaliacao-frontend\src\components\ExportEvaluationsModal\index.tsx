import moment from 'moment'
import { useEffect, useState } from 'react'
import Modal from 'react-modal'
import Checkbox from '../Checkbox'
import Datepicker from '../Datepicker'

import './styles.css'

interface ExportEvaluationsModalProps {
  isOpen: boolean
  onClose: () => void
  onExportClick: (data: Object) => void
}

const customStyles = {
  overlay: {
    background: '#00000050'
  },
  content: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    margin: 'auto',
    background: 'transparent',
    border: 'none'
  }
}

const ExportEvaluationsModal: React.FC<ExportEvaluationsModalProps> = ({
  isOpen,
  onClose,
  onExportClick
}) => {
  const [selectedFilters, setSelectedFilters] = useState({
    dataInicio: false,
    dataFinal: false,
    tipo: false,
    status: false,
    motivo: false,
    valorTotal: false,
    nome: false,
    cpf: false,
    celular: false,
    rua: false,
    numero: false,
    bairro: false,
    cidade: false,
    estado: false,
    startDate: String(moment.utc().format('YYYY-MM-DD')),
    finalDate: String(moment.utc().format('YYYY-MM-DD')),
  })

  useEffect(() => {
    if (!isOpen) {
      setSelectedFilters({
        dataInicio: false,
        dataFinal: false,
        tipo: false,
        status: false,
        motivo: false,
        valorTotal: false,
        nome: false,
        cpf: false,
        celular: false,
        rua: false,
        numero: false,
        bairro: false,
        cidade: false,
        estado: false,
        startDate: String(moment.utc().format('YYYY-MM-DD')),
        finalDate: String(moment.utc().format('YYYY-MM-DD')),
      })
    }
  }, [isOpen])

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      style={customStyles}
    >
      <div className="export-evaluations-container">
        <div>
          <h2>Exportar avaliações</h2>
          <small>Selecione as informações que deseja exportar</small>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '2rem' }}>
          <Datepicker
            id='start-date'
            label='Data inicial'
            value={selectedFilters.startDate}
            onChange={(value: string) => setSelectedFilters({
              ...selectedFilters,
              startDate: value
            })}
          />
          <Datepicker
            id='final-date'
            label='Data final'
            value={selectedFilters.finalDate}
            onChange={(value: string) => setSelectedFilters({
              ...selectedFilters,
              finalDate: value
            })}
          />
        </div>
        <div className="export-content">
          <div className="export-section">
            <strong>Avaliações</strong>
            <div>
              <Checkbox
                id="data-inicial"
                label='Data inicial'
                checked={selectedFilters.dataInicio}
                onChange={(checked: boolean) => setSelectedFilters({
                  ...selectedFilters,
                  dataInicio: checked
                })}
              />
              <Checkbox
                id="data-final"
                label='Data final'
                checked={selectedFilters.dataFinal}
                onChange={(checked: boolean) => setSelectedFilters({
                  ...selectedFilters,
                  dataFinal: checked
                })}
              />
              <Checkbox
                id="tipo"
                label='Tipo'
                checked={selectedFilters.tipo}
                onChange={(checked: boolean) => setSelectedFilters({
                  ...selectedFilters,
                  tipo: checked
                })}
              />
              <Checkbox
                id="status"
                label='Status'
                checked={selectedFilters.status}
                onChange={(checked: boolean) => setSelectedFilters({
                  ...selectedFilters,
                  status: checked
                })}
              />
              <Checkbox
                id="motivo-cancelamento"
                label='Motivo de cancelamento'
                checked={selectedFilters.motivo}
                onChange={(checked: boolean) => setSelectedFilters({
                  ...selectedFilters,
                  motivo: checked
                })}
              />
              <Checkbox
                id="valor-total"
                label='Valor total'
                checked={selectedFilters.valorTotal}
                onChange={(checked: boolean) => setSelectedFilters({
                  ...selectedFilters,
                  valorTotal: checked
                })}
              />
            </div>
          </div>
          <div className="separator" />
          <div className="export-section">
            <strong>Fornecedor</strong>
            <div>
            <Checkbox
                id="nome"
                label='Nome'
                checked={selectedFilters.nome}
                onChange={(checked: boolean) => setSelectedFilters({
                  ...selectedFilters,
                  nome: checked
                })}
              />
              <Checkbox
                id="cpf"
                label='CPF'
                checked={selectedFilters.cpf}
                onChange={(checked: boolean) => setSelectedFilters({
                  ...selectedFilters,
                  cpf: checked
                })}
              />
              <Checkbox
                id="celular"
                label='Celular'
                checked={selectedFilters.celular}
                onChange={(checked: boolean) => setSelectedFilters({
                  ...selectedFilters,
                  celular: checked
                })}
              />
              <Checkbox
                id="rua"
                label='Rua'
                checked={selectedFilters.rua}
                onChange={(checked: boolean) => setSelectedFilters({
                  ...selectedFilters,
                  rua: checked
                })}
              />
              <Checkbox
                id="numero"
                label='Número'
                checked={selectedFilters.numero}
                onChange={(checked: boolean) => setSelectedFilters({
                  ...selectedFilters,
                  numero: checked
                })}
              />
              <Checkbox
                id="bairro"
                label='Bairro'
                checked={selectedFilters.bairro}
                onChange={(checked: boolean) => setSelectedFilters({
                  ...selectedFilters,
                  bairro: checked
                })}
              />
              <Checkbox
                id="cidade"
                label='Cidade'
                checked={selectedFilters.cidade}
                onChange={(checked: boolean) => setSelectedFilters({
                  ...selectedFilters,
                  cidade: checked
                })}
              />
              <Checkbox
                id="estado"
                label='Estado'
                checked={selectedFilters.estado}
                onChange={(checked: boolean) => setSelectedFilters({
                  ...selectedFilters,
                  estado: checked
                })}
              />
            </div>
          </div>
        </div>
        <div style={{ display: 'flex', alignSelf: 'flex-end', gap: '1rem' }}>
          <button
            className='btn cancel-btn'
            onClick={onClose}
          >
            Cancelar
          </button>
          <button
            className="btn export-btn"
            onClick={() => onExportClick(selectedFilters)}
          >
            Exportar
          </button>
        </div>
      </div>
    </Modal>
  )
}

export default ExportEvaluationsModal;
