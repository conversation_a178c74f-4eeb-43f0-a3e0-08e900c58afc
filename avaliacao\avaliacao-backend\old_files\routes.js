const express = require('express')
const router = express.Router()
require('dotenv/config')

const CodSituacaoOperacaoModel = require('../models/CodSituacaoOperacao')
const SituacaoTributacaoModel = require('../models/SituacaoTributariaICMS')
const OrigemMercadoriaModel = require('../models/OrigemMercadoria')
const SituacaoTributariaIpiModel = require('../models/SituacaoTributariaIPI')
const SituacaoTributariaPisModel = require('../models/SituacaoTributariaPIS')
const CodRegimeTributarioModel = require('../models/CodRegimeTributario')
const Nivel1 = require('../models/Nivel1')
const Nivel2 = require('../models/Nivel2')
const Nivel3 = require('../models/Nivel3')
const Nivel4 = require('../models/Nivel4')
const Avaliacao = require('../models/Avaliacao')
const Usuario = require('../models/Usuario')
const isAuth = require('../middlewares/isAuth')
const { SECRET_JWT, expiresIn } = require('../config/jwt')

const bcrypt = require('bcryptjs')
const randomstring = require('randomstring')
const moment = require('moment-timezone')
const passport = require('passport')
const jwt = require('jsonwebtoken')
const Cliente = require('../models/Cliente')
const Vale = require('../models/Vale')
const Contador = require('../models/Contador')
const Config = require('../models/Config')
const Pre_Avaliacao = require('../models/Pre_Avaliacao')
const Caixa = require('../models/Caixa')
const Produto = require('../models/Produto')
const Empresa = require('../models/Empresa')
const HttpException = require('../exception/HttpException')

const axios = require('axios')
const tx2 = require('tx2-helper')
const mongoose = require('mongoose')
const qs = require('qs')
const utils = require('../utils')
const FinalidadeNotaModel = require('../models/FinalidadeNota')
const IndicadorPresencaModel = require('../models/IndicadorPresenca')
const ObjectId = id => new mongoose.Types.ObjectId(id)
const tecnoSpeedPort = process.env.TECNOSPEED_PORT || '7071'
const amb = process.env.PRODUCTION === 'true' ? '' : 'hom'
const tecnoSpeedBaseUrl = process.env.TECNOSPEED_BASE_URL
const EvaluationExtensions = require('../controllers/products/extensions')
const inventory = require('../modules/inventory/services')
const { deleteTx2File } = require('../helpers/deleteTx2File')
const { uploadTx2File } = require('../helpers/uploadTx2File')
const { getLatLongByAddress } = require('../helpers/getLatLongByAddress')

const invoiceNotesRouters = require('./nota-entrada')
const PrintPipelineRouters = require('./print-pipeline')
const PrinterOriginsRouters = require('./print-origins')
const EvaluationsRouters = require('./evaluation')
const LevelsRouters = require('./levels')
const PreEvaluationRouters = require('./pre-evaluations')
const ProductsRouters = require('./products')
const AddressRouters = require('./address')
const { RoutingKeys, Payload } = require('../config/rabbitmq/payload')
const { Broker } = require('../config/rabbitmq/broker')

router.use('/invoice-notes', invoiceNotesRouters)
router.use('/printer-pipeline', PrintPipelineRouters)
router.use('/printer-origins', PrinterOriginsRouters)
router.use('/evaluations', EvaluationsRouters)
router.use('/product-levels', LevelsRouters)
router.use('/pre-evaluations', PreEvaluationRouters)
router.use('/products', ProductsRouters)
router.use('/address', AddressRouters)
// Routes

router.get('/getEmpresaByCnpj/:cnpj', async (req, res) => {
  try {
    const { cnpj } = req.params

    const _empresa = await Empresa.findOne({ cnpj: cnpj })

    if (!_empresa)
      return res.status(400).json({
        status: false,
        data: 'Empresa nao localizada',
      })

    res.status(200).json({
      status: true,
      empresa: _empresa,
    })
  } catch (err) {
    console.log('!! /gerarValePresentes !! \n Error' + err)
    recordingErrors(
      req,
      'Erro ao gerar os Vale Presentes',
      { type: 'gerarValePresentes', interface: '/gerarValePresentes' },
      'Error' + err
    )
    return res.status(400).json({
      status: false,
      data: 'Bad Resquest',
    })
  }
})

router.get('/checkInternet', async function (req, res, next) {
  var url = 'https://www.google.com'

  try {
    var resp = await axios({
      method: 'GET',
      url: url,
    })

    res.status(200).json({
      status: true,
      message: '',
    })
  } catch (e) {
    console.log('====checkInternet erro:' + e)

    res.status(200).json({
      status: false,
      message: 'não encontrado',
    })
  }
})

//indicador de presença
router.get('/indicadores-presenca', async (req, res) => {
  const codigos = await IndicadorPresencaModel.find({}, { _id: 0, __v: 0 })

  if (!codigos.length)
    return res.status(200).json({
      status: true,
      data: 'Nenhum Codigo de finalidade da nota foi localizado',
    })

  res.status(200).json({
    status: true,
    data: codigos,
  })
})

//Codigo de finalidade da nota
router.get('/finalidade-nota', async (req, res) => {
  const codigos = await FinalidadeNotaModel.find({}, { _id: 0, __v: 0 })

  if (!codigos.length)
    return res.status(200).json({
      status: true,
      data: 'Nenhum Codigo de finalidade da nota foi localizado',
    })

  res.status(200).json({
    status: true,
    data: codigos,
  })
})

//Codigo do regime tributario
router.get('/codigo-regime-tributario', async (req, res) => {
  const codigos = await CodRegimeTributarioModel.find({}, { _id: 0, __v: 0 })

  if (!codigos.length)
    return res.status(200).json({
      status: true,
      data: 'Nenhum Codigo do regime tributario foi localizado',
    })

  res.status(200).json({
    status: true,
    data: codigos,
  })
})

//Situação tributária do PIS
router.get('/situacao-tributaria-pis', async (req, res) => {
  const situacoes = await SituacaoTributariaPisModel.find({}, { _id: 0, __v: 0 })

  if (!situacoes.length)
    return res.status(200).json({
      status: true,
      data: 'Nenhuma origem da mercadoria foi localizado',
    })

  res.status(200).json({
    status: true,
    data: situacoes,
  })
})

//Situação tributária do IPI
router.get('/situacao-tributaria-ipi', async (req, res) => {
  const situacoes = await SituacaoTributariaIpiModel.find({}, { _id: 0, __v: 0 })

  if (!situacoes.length)
    return res.status(200).json({
      status: true,
      data: 'Nenhuma origem da mercadoria foi localizado',
    })

  res.status(200).json({
    status: true,
    data: situacoes,
  })
})

//Origem da Mercadoria
router.get('/origens-mercadoria', async (req, res) => {
  const origens = await OrigemMercadoriaModel.find({}, { _id: 0, __v: 0 })

  if (!origens.length)
    return res.status(200).json({
      status: true,
      data: 'Nenhuma origem da mercadoria foi localizado',
    })

  res.status(200).json({
    status: true,
    data: origens,
  })
})

//Situação tributária do ICMS,
router.get('/situacao-tributaria-icms', async (req, res) => {
  const situacoes = await SituacaoTributacaoModel.find({}, { _id: 0, __v: 0 })

  if (!situacoes.length)
    return res.status(200).json({
      status: true,
      data: 'Nenhuma Situação tributária do ICMS foi localizada',
    })

  res.status(200).json({
    status: true,
    data: situacoes,
  })
})

//Código de Situação da Operação – Simples Nacional (CSOSN), onde CRT = 1
router.get('/csosn', async (req, res) => {
  const codSituacaoOperacao = await CodSituacaoOperacaoModel.find({}, { _id: 0, __v: 0 })

  if (!codSituacaoOperacao.length)
    return res.status(200).json({
      status: true,
      data: 'Nenhum Código de Situação da Operação foi localizado.',
    })

  res.status(200).json({
    status: true,
    data: codSituacaoOperacao,
  })
})

router.post('/itens-estoque', isAuth, async (req, res) => {
  const { idAvaliacao, itens } = req.body

  try {
    const avaliacao = await Avaliacao.findOneAndUpdate(
      { _id: idAvaliacao },
      { $push: { itensEstoque: itens } },
      { upsert: true, new: true }
    )

    return res.status(200).json(avaliacao)
  } catch (error) {
    return res.status(400).json({
      error: `Falha ao atualizar itens de estoque: ${error}`,
    })
  }
})

router.put('/update-itens-estoque', async (req, res) => {
  const { idAvaliacao, newItem } = req.body
  try {
    let avaliationToUpdate = await Avaliacao.findById(idAvaliacao)

    const itenAlreadyPrecify = avaliationToUpdate.itensEstoque
      .filter(item => item.descricao === newItem.descricao && item.codBarras === newItem.codBarras)
      .shift()

    if (!itenAlreadyPrecify) {
      avaliationToUpdate.itensEstoque.filter(item => {
        if (item.description === newItem.description && !item.precificado) {
          if (item.quantidade === 1) {
            item.codBarras = newItem.codBarras
            item.precificado = true
            item.valorDef = newItem.valorDef
          } else {
            avaliationToUpdate.itensEstoque.push(newItem)
            item.quantidade = item.quantidade - 1
          }
        }
      })
    } else {
      const alreadyExists = avaliationToUpdate.itensEstoque.some(
        item =>
          item.description === newItem.description &&
          item.codBarras === newItem.codBarras &&
          item.precificado
      )
      if (alreadyExists) {
        avaliationToUpdate.itensEstoque.filter(item => {
          if (
            item.description === newItem.description &&
            item.codBarras === newItem.codBarras &&
            item.precificado
          ) {
            item.quantidade = item.quantidade + 1
            avaliationToUpdate.itensEstoque.filter(itemNotPrecify => {
              if (
                itemNotPrecify.description === newItem.description &&
                !itemNotPrecify.precificado &&
                itemNotPrecify.quantidade > 0
              ) {
                itemNotPrecify.quantidade = itemNotPrecify.quantidade - 1
              }
            })
          }
        })
      } else {
        avaliationToUpdate.itensEstoque.push(newItem)
        avaliationToUpdate.itensEstoque.filter(product => {
          if (product.description === newItem.description && !product.precificado) {
            product.quantidade = product.quantidade - 1
          }
        })
      }
    }

    avaliationToUpdate.itensEstoque.filter(item => item.quantidade > 0)

    const avaliationUpdated = await Avaliacao.findByIdAndUpdate(idAvaliacao, avaliationToUpdate, {
      upsert: true,
      new: true,
    })

    res.status(200).json(avaliationUpdated)
  } catch (error) {
    console.log(error)
    return res.status(400).json({
      error: `Failed to update item: ${error}`,
    })
  }
})

router.get('/busca', isAuth, (req, res) => {
  let data = []
  Nivel1.find({ $or: [{ deleted: { $exists: false } }, { deleted: false }] })
    .then(data1 => {
      data.push(data1)
      Nivel2.find({
        $or: [{ deleted: { $exists: false } }, { deleted: false }],
      })
        .then(data2 => {
          data.push(data2)
          Nivel3.find({
            $or: [{ deleted: { $exists: false } }, { deleted: false }],
          })
            .then(data3 => {
              data.push(data3)
              Nivel4.find({
                $or: [{ deleted: { $exists: false } }, { deleted: false }],
              })
                .then(data4 => {
                  data.push(data4)
                  res.json(data)
                })
                .catch(error => {
                  console.log('error: ', error)
                })
            })
            .catch(error => {
              console.log('error: ', error)
            })
        })
        .catch(error => {
          console.log('error: ', error)
        })
    })
    .catch(error => {
      console.log('error: ', error)
    })
})

router.get('/busca_avaliacoes', isAuth, async (req, res) => {
  let filter = req.query
  const { page, size } = req.query

  if (filter.startDate) {
    filter['dataInicio'] = {
      $gte: new Date(new Date(filter.startDate)),
    }
    delete filter.startDate
  }

  if (filter.finalDate) {
    filter['dataInicio'] = {
      ...filter['dataInicio'],
      $lte: new Date(new Date(filter.finalDate).setHours(23 - 3)),
    }
    delete filter.finalDate
  }

  delete filter.page
  delete filter.size

  try {
    let evaluations = await Avaliacao.find(filter)
      .limit(Number(size))
      .skip((page - 1) * size)
      .sort({ dataInicio: -1 })

    if (filter.cliente) {
      evaluations = evaluations.filter(evaluation =>
        String(evaluation?.cliente)?.toLowerCase()?.includes(filter.cliente?.toLowerCase())
      )
    }

    if (filter.cpf) {
      evaluations = evaluations.filter(evaluation =>
        String(evaluation?.cpf)?.toLowerCase()?.includes(filter.cpf)
      )
    }

    const totalEvaluation = await Avaliacao.count()

    res.json({
      page: Number(page),
      size: evaluations.length,
      totalEvaluation: totalEvaluation,
      avaliacoes: evaluations,
    })
  } catch (error) {
    console.log(error)
  }
})

router.post('/busca_avaliacoes', isAuth, (req, res) => {
  const cnpj = req.body.cnpj
  Avaliacao.find({ cnpj: cnpj })
    .then(data => {
      res.json(data)
    })
    .catch(error => {
      console.log('error: ', error)
    })
})

router.post('/busca_avaliacao/cliente', isAuth, async (req, res) => {
  const { cpf } = req.body || {}

  const findAvaliacao = await Avaliacao.aggregate([
    {
      $match: {
        cpf,
      },
    },
    {
      $lookup: {
        as: 'empresa',
        from: 'empresas',
        let: {
          cnpjCondition: '$cnpj',
        },
        pipeline: [
          {
            $match: {
              $expr: {
                $eq: ['$cnpj', '$$cnpjCondition'],
              },
            },
          },
          {
            $project: {
              _id: 0,
              nomeUnidade: 1,
            },
          },
        ],
      },
    },
    {
      $set: {
        empresa: { $first: '$empresa' },
      },
    },
  ])

  return res.json(findAvaliacao)
})

router.get('/avaliacao_aberta', isAuth, (req, res) => {
  const { cnpj, idUsuarioAvaliacao } = req.query

  Avaliacao.findOne({
    finalizado: false,
    cnpj: cnpj,
    idUsuarioAvaliacao: idUsuarioAvaliacao,
  })
    .then(data => {
      res.json(data)
    })
    .catch(error => {
      console.log('error: ', error)
    })
})

router.post('/pre_avaliacao_aberta', isAuth, (req, res) => {
  const cnpj = req.body.cnpj
  Pre_Avaliacao.findOne({ finalizado: false, cnpj: cnpj })
    .then(data => {
      res.json(data)
    })
    .catch(error => {
      console.log('error: ', error)
    })
})

async function geraCod() {
  let contador, codigo
  contador = await Produto.countDocuments()
  codigo = contador++
  // gera o codigo de barras essa parte
  let codigoBarra = 123000000000
  let um,
    dois,
    tres,
    quatro,
    cinco,
    seis,
    sete,
    oito,
    nove,
    dez,
    onze,
    doze,
    soma,
    multiplo,
    resultado
  codigoBarra += Number(codigo)
  codigoBarra = codigoBarra.toString()
  um = Number(codigoBarra.substring(0, 1))
  dois = Number(codigoBarra.substring(1, 2))
  tres = Number(codigoBarra.substring(2, 3))
  quatro = Number(codigoBarra.substring(3, 4))
  cinco = Number(codigoBarra.substring(4, 5))
  seis = Number(codigoBarra.substring(5, 6))
  sete = Number(codigoBarra.substring(6, 7))
  oito = Number(codigoBarra.substring(7, 8))
  nove = Number(codigoBarra.substring(8, 9))
  dez = Number(codigoBarra.substring(9, 10))
  onze = Number(codigoBarra.substring(10, 11))
  doze = Number(codigoBarra.substring(11, 12))
  soma =
    um * 1 +
    dois * 3 +
    tres * 1 +
    quatro * 3 +
    cinco * 1 +
    seis * 3 +
    sete * 1 +
    oito * 3 +
    nove * 1 +
    dez * 3 +
    onze * 1 +
    doze * 3
  soma = soma.toString()
  if (soma.slice(1) === '0') {
    codigoBarra += '0'
  } else {
    multiplo = Number(soma.slice(0, -1))
    multiplo = multiplo * 10 + 10
    resultado = multiplo - soma
    codigoBarra += resultado
  }
  return codigoBarra
}

router.post('/busca_amarrados', isAuth, async (req, res) => {
  const { nvl, id: chave } = req.body || {}

  if (!chave) {
    return res.status(400).json({
      status: false,
      error: 'Id/Chave Inválida!',
    })
  }

  const niveisOpts = {
    nivel1: Nivel1,
    nivel2: Nivel2,
    nivel3: Nivel3,
    nivel4: Nivel4,
  }

  const acceptedNiveis = Object.keys(niveisOpts)

  if (!acceptedNiveis.includes(nvl) || !niveisOpts?.[nvl]) {
    return res.status(400).json({
      status: false,
      error: 'Nível não encontrado!',
    })
  }

  try {
    const findInfosByNivel = await niveisOpts[nvl].aggregate([
      {
        $match: {
          chave,
          $or: [{ deleted: { $exists: false } }, { deleted: false }],
        },
      },
      {
        $set: {
          _id: {
            $toString: '$_id',
          },
        },
      },
      {
        $lookup: {
          from: 'produtos',
          localField: '_id',
          foreignField: 'nivel3',
          as: 'produto',
        },
      },
    ])

    const formatProductNivel = findInfosByNivel.map(info => {
      const produto = info['produto'] || {}
      const produtoAtivo = produto?.filter(prod => prod.ativo)?.[0] || {}

      if (produtoAtivo?.ativo) {
        info['valor_antigo'] = info['valor'] || 0
        info['valor'] = produtoAtivo?.['vlrCusto'] || info['valor'] || 0
      }

      delete info['produto']

      return info
    })

    res.status(200).json(formatProductNivel)
  } catch (error) {
    res.status(400).json({
      status: false,
      error,
    })
  }
})

router.post('/save_avaliacao', isAuth, (req, res) => {
  try {
    const {
      motivo,
      usuario,
      cnpj,
      dataInicio,
      dataFinal,
      totalValePadrao,
      totalValeEfetivado,
      totalDinheiroPadrao,
      totalDinheiroEfetivado,
      oferta,
      tipo,
      cpf,
      cliente,
      idPreAvaliacao,
      idUsuarioAvaliacao,
      caixa,
    } = req.body

    if (!cnpj)
      return res.status(400).json({
        status: false,
        errorMenssage: 'Informe o CNPJ corretamente.',
      })

    const avaliacao = {
      items: [],
      motivo: motivo,
      usuario: usuario,
      cnpj: cnpj,
      dataInicio: dataInicio,
      dataFinal: dataFinal,
      totalValePadrao: totalValePadrao,
      totalValeEfetivado: totalValeEfetivado,
      totalDinheiroPadrao: totalDinheiroPadrao,
      totalDinheiroEfetivado: totalDinheiroEfetivado,
      oferta: oferta,
      tipo: tipo,
      finalizado: false,
      cancelado: false,
      cpf: cpf,
      cliente: cliente,
      idPreAvaliacao: idPreAvaliacao,
      caixa: caixa,
      idUsuarioAvaliacao: idUsuarioAvaliacao,
    }
    new Avaliacao(avaliacao)
      .save()
      .then(obj => res.json(obj))
      .catch(erro => res.status(400).json(erro))
  } catch (error) {
    console.log(error)
    res.status(400).json({
      status: false,
      error: error,
    })
  }
})

router.patch('/cancela_avaliacao', isAuth, async (req, res) => {
  const evaluation = await Avaliacao.findOneAndUpdate(
    { _id: req.body.idAvaliacao },
    {
      items: req.body.items,
      motivo: req.body.motivo,
      dataFinal: req.body.dataFinal,
      totalDinheiroPadrao: req.body.totalDinheiroPadrao,
      totalDinheiroEfetivado: req.body.totalDinheiroEfetivado,
      totalValePadrao: req.body.totalValePadrao,
      totalValeEfetivado: req.body.totalValeEfetivado,
      tipo: req.body.tipo,
      codBarras: req.body.codBarras,
      finalizado: true,
      cancelado: true,
      oferta: req.body.oferta,
      caixa: req.body.idCaixa,
      rejected: false,
      reject_at: null,
    }
  )

  inventory.removeProduct({
    items: req.body.items,
    cnpj: req.body.cnpj,
  })

  // const payload = new Payload(RoutingKeys.EVALUATION_CANCELED, evaluation)
  // Broker.publish(payload)

  res.json(evaluation)
})

router.get('/get_avaliacao', (req, res) => {
  const avaliacao = process.env
  res.status(200).json({
    avaliacao,
  })
})

router.post('/buscar_por_nvs', isAuth, async (req, res) => {
  const { produtos } = req.body
  let prodEncontrado
  let prodGen
  let qtd
  let precoBruto
  let descPesquisa = null
  let ePesquisa = false
  let excluido = false

  const produtosEncontrados = []
  // percorre os produtos
  for (let i = 0; i < produtos.length; i++) {
    descPesquisa = null
    ePesquisa = false
    // vai verficar quantos niveis tem e buscar o produto
    if (produtos[i].fechamento.length === 1) {
      prodEncontrado = await Produto.findOne({
        nivel1: produtos[i].fechamento[0].item._id,
        ativo: true,
      })
      qtd = produtos[i].fechamento[0].qtd
      if (prodEncontrado && (prodEncontrado.pesquisa || prodEncontrado.pesquisa == 'sim')) {
        precoBruto = Number(produtos[i].fechamento[0].item.valor)
        descPesquisa = produtos[i].fechamento[0].item.descPesquisa
        ePesquisa = true
      }
      if (!prodEncontrado) {
        const req = {
          nivel: 'nivel1',
          pesquisa: produtos[i].fechamento[0].item.pesquisa,
          valor: produtos[i].fechamento[0].item.valor,
          valorMax: produtos[i].fechamento[0].item.valorMax,
          coeficiente: produtos[i].fechamento[0].item.coeficiente,
          descricao: produtos[i].fechamento[0].item.descricao,
        }
        prodGen = await EvaluationExtensions.geraProduto(req, produtos[i].fechamento[0].item)
        prodEncontrado = await new Produto(prodGen).save()
      }
    } else if (produtos[i].fechamento.length === 2) {
      prodEncontrado = await Produto.findOne({
        nivel1: produtos[i].fechamento[0].item._id,
        nivel2: produtos[i].fechamento[1].item._id,
        ativo: true,
      })
      qtd = produtos[i].fechamento[1].qtd
      if (prodEncontrado && (prodEncontrado.pesquisa || prodEncontrado.pesquisa == 'sim')) {
        precoBruto = Number(produtos[i].fechamento[1].item.valor)
        descPesquisa = produtos[i].fechamento[1].item.descPesquisa
        ePesquisa = true
      }
      if (!prodEncontrado) {
        const req = {
          nivel: 'nivel2',
          pesquisa: produtos[i].fechamento[0].item.pesquisa,
          valor: produtos[i].fechamento[0].item.valor,
          valorMax: produtos[i].fechamento[0].item.valorMax,
          coeficiente: produtos[i].fechamento[0].item.coeficiente,
          descricao: produtos[i].fechamento[0].item.descricao,
        }
        prodGen = await EvaluationExtensions.geraProduto(req, produtos[i].fechamento[0].item)
        prodEncontrado = await new Produto(prodGen).save()
      }
    } else if (produtos[i].fechamento.length === 3) {
      prodEncontrado = await Produto.findOne({
        nivel1: produtos[i].fechamento[0].item._id,
        nivel2: produtos[i].fechamento[1].item._id,
        nivel3: produtos[i].fechamento[2].item._id,
        ativo: true,
      })
      qtd = produtos[i].fechamento[2].qtd
      if (prodEncontrado && (prodEncontrado.pesquisa || prodEncontrado.pesquisa == 'sim')) {
        precoBruto = Number(produtos[i].fechamento[2].item.valor)
        descPesquisa = produtos[i].fechamento[2].item.descPesquisa
        ePesquisa = true
      }
      if (!prodEncontrado) {
        const req = {
          nivel: 'nivel3',
          pesquisa: produtos[i].fechamento[0].item.pesquisa,
          valor: produtos[i].fechamento[0].item.valor,
          valorMax: produtos[i].fechamento[0].item.valorMax,
          coeficiente: produtos[i].fechamento[0].item.coeficiente,
          descricao: produtos[i].fechamento[0].item.descricao,
        }
        prodEncontrado = await EvaluationExtensions.geraProduto(req, produtos[i].fechamento[0].item)
      }
    } else if (produtos[i].fechamento.length === 4) {
      prodEncontrado = await Produto.findOne({
        nivel1: produtos[i].fechamento[0].item._id,
        nivel2: produtos[i].fechamento[1].item._id,
        nivel3: produtos[i].fechamento[2].item._id,
        nivel4: produtos[i].fechamento[3].item._id,
        ativo: true,
      })
      qtd = produtos[i].fechamento[3].qtd

      if (prodEncontrado && (prodEncontrado.pesquisa || prodEncontrado.pesquisa == 'sim')) {
        precoBruto = Number(produtos[i].fechamento[3].item.valor)
        descPesquisa = produtos[i].fechamento[3].item.descPesquisa
        ePesquisa = true
      }

      if (!prodEncontrado) {
        const req = {
          nivel: 'nivel4',
          pesquisa: produtos[i].fechamento[0].item.pesquisa,
          valor: produtos[i].fechamento[0].item.valor,
          valorMax: produtos[i].fechamento[0].item.valorMax,
          coeficiente: produtos[i].fechamento[0].item.coeficiente,
          descricao: produtos[i].fechamento[0].item.descricao,
        }
        prodEncontrado = await EvaluationExtensions.geraProduto(req, produtos[i].fechamento[0].item)
      }
    }
    excluido = qtd === 0 ? true : false

    if (!prodEncontrado.pesquisa)
      produtosEncontrados.push({
        idProduto: prodEncontrado._id,
        qtd: qtd,
        excluido,
        descPesquisa,
        ePesquisa,
      })
    else
      produtosEncontrados.push({
        idProduto: prodEncontrado._id,
        qtd: qtd,
        coeficiente: prodEncontrado.coeficiente,
        precoBruto: precoBruto,
        excluido,
        descPesquisa,
        ePesquisa,
      })
  }
  return res.json(produtosEncontrados)
})

// requisicao para finalizar a pre avaliacao
router.put('/update_pre_avaliacao', isAuth, (req, res) => {
  Pre_Avaliacao.findByIdAndUpdate(
    { _id: req.body._id },
    {
      finalizado: true,
    }
  )
    .then(() => res.json('pre avaliação atualizada!!!!!!!'))
    .catch(erro => res.json(erro))
})

router.patch('/update_avaliacao/cliente', isAuth, (req, res) => {
  if (req.body.data !== null || typeof req.body.data !== 'undefined') {
    Avaliacao.findOneAndUpdate(
      { cnpj: req.body.cnpj, finalizado: false },
      {
        cpf: req.body.cpf,
        cliente: req.body.nome,
        data: req.body.data,
      }
    )
      .then(() => res.json('avaliação atualizada!!!!!!!'))
      .catch(erro => res.json(erro))
  } else {
    Avaliacao.findOneAndUpdate(
      { cnpj: req.body.cnpj, finalizado: false },
      {
        cpf: req.body.cpf,
        cliente: req.body.nome,
      }
    )
      .then(() => res.json('avaliação atualizada!!!!!!!'))
      .catch(erro => res.json(erro))
  }
})

router.post('/registra_usuario', (req, res) => {
  const usuario = {
    usuario: req.body.usuario,
    senha: req.body.senha,
    tipo: req.body.tipo,
    unidades: req.body.unidades,
  }
  bcrypt.genSalt(10, (erro, salt) => {
    bcrypt.hash(usuario.senha, salt, (erro, hash) => {
      if (erro) {
        console.log(erro)
      } else {
        usuario.senha = hash
        new Usuario(usuario)
          .save()
          .then(obj => res.send(obj))
          .catch(erro => res.status(400).json(erro))
      }
    })
  })
})

router.post('/login', (req, res, next) => {
  passport.authenticate('local', function (err, user, info) {
    if (err) return res.status(500).send(info.message)
    if (!user) return res.status(401).json(info.message)
    req.logIn(user, function (err) {
      if (err) return next(err)
      const token = jwt.sign(
        {
          _id: user._id,
        },
        SECRET_JWT,
        {
          expiresIn,
        }
      )

      let company
      Caixa.find({ dataFechamento: null })
        .then(async caixas => {
          const { cnpj } = user.unidades.find(unidade => unidade.cnpj)

          company = await Empresa.findOne({ cnpj: String(cnpj).replace(/\D/g, '') })
          const caixaAberto = caixas.find(caixa => caixa.idUsuario == user._id)

          return res.status(200).json({
            usuario: user,
            msg: info.message,
            token: token,
            caixa: caixaAberto,
            company,
          })
        })
        .catch(() => {
          return res.status(200).json({
            usuario: user,
            msg: info.message,
            token: token,
            caixa: null,
            company,
          })
        })
    })
  })(req, res, next)
})

router.get('/busca_usuario', isAuth, (req, res) => {
  Usuario.findById(req.usuario._id)
    .then(data => {
      res.send(data)
    })
    .catch(err => console.log(err))
})

router.get('/usuarios', isAuth, (req, res) => {
  Usuario.find()
    .then(data => {
      res.send(data)
    })
    .catch(err => console.log(err))
})

router.post('/busca_cpf', isAuth, (req, res) => {
  Cliente.findOne({ cpf: req.body.cpf })
    .then(data => res.send(data))
    .catch(err => console.log(err))
})

router.get('/busca_todos_clientes', isAuth, (req, res) => {
  Cliente.find({})
    .then(data => res.send(data))
    .catch(erro => console.log(erro))
})

router.post('/salva_cliente', isAuth, async (req, res) => {
  try {
    const cliente = {
      nome: req.body.nome,
      cpf: req.body.cpf,
      dtNascimento: req.body.dtNascimento,
      celular: req.body.celular,
      rua: req.body.rua,
      bairro: req.body.bairro,
      numero: req.body.numero,
      cep: req.body.cep,
      cod_cidade: req.body.cod_cidade,
      cod_uf: req.body.cod_uf,
      estado: req.body.estado,
      cidade: req.body.cidade,
      complemento: req.body.complemento,
    }

    const addressFormatted = [
      cliente.rua,
      cliente.numero,
      cliente.bairro,
      cliente.cidade,
      cliente.estado,
    ].join(', ')

    const geo = await getLatLongByAddress(addressFormatted)

    if (geo.latitude) {
      cliente.geo = {
        latitude: geo.latitude,
        longitude: geo.longitude,
      }
    }

    new Cliente(cliente)
      .save()
      .then(data => res.json(data))
      .catch(erro => res.json(erro))
  } catch (erro) {
    res.json(erro)
  }
})

router.put('/atualiza_cliente', isAuth, async (req, res) => {
  try {
    const {
      nome,
      cpf,
      dtNascimento,
      celular,
      rua,
      bairro,
      numero,
      cep,
      cod_uf,
      cod_cidade,
      estado,
      cidade,
      complemento,
    } = req.body || {}

    if (!cpf || cpf.length < 4) {
      return res.json({
        status: false,
        message: 'Cliente não encontrado!',
      })
    }

    const findCliente = await Cliente.findOne({ cpf }).lean()

    if (!findCliente) {
      return res.json({
        status: false,
        message: 'Usuário não encontrado!',
      })
    }

    const clienteRequest = {
      nome,
      cpf,
      dtNascimento,
      celular,
      rua,
      bairro,
      numero,
      cep,
      cod_uf,
      cod_cidade,
      estado,
      cidade,
      complemento,
    }

    // Se for a mesma rua ou número ou o usuário não tiver geo, consulta e atualiza
    if (
      clienteRequest.rua != findCliente.rua ||
      Number(clienteRequest.numero) !== findCliente.numero ||
      !findCliente?.['geo']?.['latitude']
    ) {
      const addressFormatted = [
        clienteRequest.rua,
        clienteRequest.numero,
        clienteRequest.bairro,
        clienteRequest.cidade,
        clienteRequest.estado,
      ].join(', ')

      const geo = await getLatLongByAddress(addressFormatted)

      if (geo?.latitude) {
        clienteRequest.geo = {
          latitude: geo.latitude,
          longitude: geo.longitude,
        }
      }
    }

    const update = await Cliente.findOneAndUpdate({ cpf }, clienteRequest, { new: true }).lean()
    res.json(update)
  } catch (erro) {
    res.json(erro)
  }
})

router.post('/gera_vale', isAuth, async (req, res) => {
  const {
    cpf,
    idAvaliacao,
    vlrAberto,
    vlrTotal,
    codBarras,
    nomeCliente,
    cnpj,
    codCidade,
    data,
    dataExpiracao,
  } = req.body || {}

  const existValeInAvaliacao = await Vale.findOne({
    idAvaliacao,
  })

  if (existValeInAvaliacao) {
    return res.status(400).json({
      status: false,
      errorMenssage: 'Já existe um vale criado para esta avaliação!',
    })
  }

  const newVale = {
    cpf,
    idAvaliacao,
    vlrAberto,
    vlrTotal,
    codBarras,
    nomeCliente,
    cnpj,
    codCidade,
    data,
    dataExpiracao,
  }

  new Vale(newVale)
    .save()
    .then(obj => res.json(obj))
    .catch(erro => res.status(400).json(erro))
})

router.post('/busca_vale', isAuth, (req, res) => {
  if (req.body.cpf !== null)
    Vale.find({ cpf: req.body.cpf })
      .then(data => res.send(data))
      .catch(err => console.log(err))
  else
    Vale.find({ codBarras: req.body.codBarras })
      .then(data => res.send(data))
      .catch(err => console.log(err))
})

router.post('/deleta_vale', isAuth, (req, res) => {
  Vale.findOneAndDelete({ codBarras: req.body.codBarras })
    .then(data => res.send(data))
    .catch(err => console.log(err))
})

router.get('/busca_rodape', isAuth, (req, res) => {
  Config.find({ cod: 1 })
    .then(data => res.send(data))
    .catch(err => console.log(err))
})

router.post('/cadastra_pre_avaliacao', isAuth, (req, res) => {
  try {
    const { usuario, cnpj, data, finalizado, idCliente, volume } = req.body

    if (!cnpj)
      return res.status(400).json({
        status: false,
        errorMenssage: 'Informe o cnpj corretamente.',
      })

    const newPre = {
      usuario: usuario,
      cnpj: cnpj,
      data: data,
      finalizado: finalizado,
      idCliente: idCliente,
      volume: volume,
    }
    new Pre_Avaliacao(newPre)
      .save()
      .then(obj => res.json(obj))
      .catch(erro => res.status(400).json(erro))
  } catch (error) {
    console.log(error)
    res.status(400).json({
      status: false,
      error: error,
    })
  }
})

router.post('/busca_pre_avaliacao', isAuth, (req, res) => {
  Pre_Avaliacao.aggregate([
    { $match: { cnpj: req.body.cnpj, finalizado: false } },
    { $addFields: { userObjectId: { $toObjectId: '$idCliente' } } },
    {
      $lookup: {
        localField: 'userObjectId',
        from: 'clientes',
        foreignField: '_id',
        as: 'cliente',
      },
    },
  ])
    .then(data => {
      res.send(data)
    })
    .catch(err => console.log(err))
})

router.get('/busca_pre_avaliacao', isAuth, (req, res) => {
  Pre_Avaliacao.aggregate([
    { $match: { finalizado: false } },
    { $addFields: { userObjectId: { $toObjectId: '$idCliente' } } },
    {
      $lookup: {
        localField: 'userObjectId',
        from: 'clientes',
        foreignField: '_id',
        as: 'cliente',
      },
    },
  ])
    .then(data => {
      res.send(data)
    })
    .catch(err => console.log(err))
})

router.get('/busca_pre/:idPre', isAuth, async (req, res) => {
  try {
    const { idPre } = req.params

    const pre_avaliation = await Pre_Avaliacao.findById(idPre)

    if (pre_avaliation.finalizado === true) {
      throw new Error('Pré Avaliação já finalizada')
    }

    return res.status(200).json(pre_avaliation)
  } catch (error) {
    return res.status(400).json({
      error: `Something went wrong: ${error}`,
    })
  }
})

router.post('/busca_cliente', isAuth, (req, res) => {
  if (typeof req.body.idCliente !== 'undefined') {
    Cliente.findOne({ _id: req.body.idCliente })
      .then(data => res.send(data))
      .catch(err => console.log(err))
  } else {
    Cliente.findOne({ cpf: req.body.cpf })
      .then(data => res.send(data))
      .catch(err => console.log(err))
  }
})

router.post('/abrir_caixa', isAuth, (req, res) => {
  const dataAbertura = new Date()
  dataAbertura.setHours(dataAbertura.getHours() - 3)
  const newCaixa = {
    // unidade: req.body.unidade,
    cnpj: req.body.cnpj,
    dataAbertura: dataAbertura,
    vlrAbertura: req.body.vlrAbertura,
    idUsuario: req.body.idUsuario,
    sangria: 0,
    reforco: 0,
  }
  new Caixa(newCaixa)
    .save()
    .then(obj => res.json(obj))
    .catch(erro => res.status(400).json(erro))
})

router.post('/fechar_caixa', isAuth, (req, res) => {
  const data = new Date()
  data.setHours(data.getHours() - 3)
  Caixa.findOneAndUpdate(
    { _id: req.body.idCaixa },
    {
      condPagamento: req.body.condPagamento,
      dataFechamento: data,
    }
  )
    .then(caixa => res.json(caixa))
    .catch(erro => res.status(400).json(erro))
})

router.post('/sangria', isAuth, (req, res) => {
  Caixa.findById(req.body.id).then(caixa => {
    Caixa.findOneAndUpdate(
      { _id: req.body.id },
      { sangria: Number(req.body.sangria) + caixa.sangria }
    )
      .then(() => res.send({ message: 'Sangria realizada' }))
      .catch(erro => res.status(400).json(erro))
  })
})

router.post('/reforco', isAuth, (req, res) => {
  Caixa.findById(req.body.id).then(caixa => {
    Caixa.findOneAndUpdate(
      { _id: req.body.id },
      { reforco: Number(req.body.reforco) + caixa.reforco }
    )
      .then(() => res.send({ message: 'Reforço realizado' }))
      .catch(erro => res.status(400).json(erro))
  })
})

// rota para retornar as condiçoes de pagamento de dado caixa
router.post('/condPagamento_caixa', isAuth, (req, res) => {
  const { idCaixa } = req.body
  let somaDinheiro = 0
  let somaVale = 0
  // encontra vendas amarradas ao id do caixa
  Avaliacao.find({ caixa: idCaixa })
    .then(avaliacoes => {
      // percorre as avaliacoes
      avaliacoes.forEach(avaliacao => {
        if (!avaliacao.cancelado && avaliacao.finalizado)
          if (avaliacao.tipo === 'Dinheiro') {
            if (!isNaN(Number(avaliacao.totalDinheiroEfetivado)))
              somaDinheiro += Number(avaliacao.totalDinheiroEfetivado)
          } else if (avaliacao.tipo === 'Vale') {
            if (!isNaN(Number(avaliacao.totalValeEfetivado)))
              somaVale += Number(avaliacao.totalValeEfetivado)
          }
      })
      res.send({ dinheiro: somaDinheiro, vale: somaVale })
    })
    .catch(erro => res.status(400).json(erro))
})

router.post('/buscar_caixa', isAuth, (req, res) => {
  Caixa.findOne({ _id: req.body.idCaixa })
    .then(caixa => res.json(caixa))
    .catch(erro => res.status(400).json(erro))
})

// buscar caixa aberto de dado usuario
router.get('/buscar_caixa/idUsuario/:id', isAuth, (req, res) => {
  Caixa.findOne({ idUsuario: req.params.id, dataFechamento: undefined })
    .then(caixa => res.json(caixa))
    .catch(erro => res.status(400).json(erro))
})

router.post('/busca_itens', isAuth, async (req, res) => {
  try {
    const itens = await Avaliacao.aggregate([
      { $unwind: '$items' },
      { $match: { _id: ObjectId(req.body.idAvaliacao) } },
      { $addFields: { userObjectId: { $toObjectId: '$items.idProduto' } } },
      {
        $lookup: {
          localField: 'userObjectId',
          from: 'produtos',
          foreignField: '_id',
          as: 'prod_item',
        },
      },
      {
        $unwind: {
          path: '$prod_item',
        },
      },
      {
        $project: {
          id: 1,
          items: 1,
          prod_item: 1,
        },
      },
    ])

    res.json(itens)
  } catch (e) {
    console.log(e)
    res.status(500).send(e)
  }
})

router.post('/buscar_caixa_por_usuario', isAuth, async (req, res) => {
  const { idUsuario } = req.body
  const caixas = await Caixa.find({ idUsuario: idUsuario }).sort({ _id: -1 }).limit(30)
  return res.send(caixas)
})

// função para gerar os produtos pelos niveis cadastrados
router.get('/gera_produtos', isAuth, async (req, res) => {
  let contador, codigo, produto, vlrCusto, vlrVenda, coeficiente, pesquisa, nvl1, nvl2, nvl3, nvl4
  contador = await Contador.findOne({ id: 'produtoid' })
  codigo = contador.sequencia
  let um,
    dois,
    tres,
    quatro,
    cinco,
    seis,
    sete,
    oito,
    nove,
    dez,
    onze,
    doze,
    soma,
    multiplo,
    resultado

  // percorre o nivel1 e monta os produtos
  Nivel1.find().then(async res => {
    for (let i = 0; i < res.length; i++) {
      if (res[i].ultimoNvl === 'sim') {
        if (!res[i].pesquisa || res[i].pesquisa === 'não') {
          pesquisa = false
          coeficiente = null
          vlrCusto = res[i].valor
          vlrVenda = res[i].valorMax
        } else {
          pesquisa = true
          coeficiente = res[i].coeficiente
          vlrCusto = null
          vlrVenda = null
        }
        // gera o codigo de barras essa parte
        let codigoBarra = 123000000000
        codigoBarra += Number(codigo)
        codigoBarra = codigoBarra.toString()
        um = Number(codigoBarra.substring(0, 1))
        dois = Number(codigoBarra.substring(1, 2))
        tres = Number(codigoBarra.substring(2, 3))
        quatro = Number(codigoBarra.substring(3, 4))
        cinco = Number(codigoBarra.substring(4, 5))
        seis = Number(codigoBarra.substring(5, 6))
        sete = Number(codigoBarra.substring(6, 7))
        oito = Number(codigoBarra.substring(7, 8))
        nove = Number(codigoBarra.substring(8, 9))
        dez = Number(codigoBarra.substring(9, 10))
        onze = Number(codigoBarra.substring(10, 11))
        doze = Number(codigoBarra.substring(11, 12))
        soma =
          um * 1 +
          dois * 3 +
          tres * 1 +
          quatro * 3 +
          cinco * 1 +
          seis * 3 +
          sete * 1 +
          oito * 3 +
          nove * 1 +
          dez * 3 +
          onze * 1 +
          doze * 3
        soma = soma.toString()
        if (soma.slice(1) === '0') {
          codigoBarra += '0'
        } else {
          multiplo = Number(soma.slice(0, -1))
          multiplo = multiplo * 10 + 10
          resultado = multiplo - soma
          codigoBarra += resultado
        }
        produto = {
          descricao: res[i].descricao,
          nivel1: res[i]._id,
          vlrCusto: vlrCusto,
          vlrVenda: vlrVenda,
          codigo: codigo,
          codBarras: codigoBarra,
          pesquisa: pesquisa,
          coeficiente: coeficiente,
          favorito: false,
        }
        codigo++
        await new Produto(produto).save()
      }
    }

    // percorre o nivel2 montando os produtos
    Nivel2.find().then(async res => {
      for (let i = 0; i < res.length; i++) {
        if (res[i].ultimoNvl === 'sim') {
          nvl1 = await Nivel1.findById(res[i].chave)

          if (!res[i].pesquisa || res[i].pesquisa === 'não') {
            pesquisa = false
            coeficiente = null
            vlrCusto = res[i].valor
            vlrVenda = res[i].valorMax
          } else {
            pesquisa = true
            coeficiente = res[i].coeficiente
            vlrCusto = null
            vlrVenda = null
          }

          // gera o codigo de barras essa parte
          let codigoBarra = 123000000000
          codigoBarra += Number(codigo)
          codigoBarra = codigoBarra.toString()
          um = Number(codigoBarra.substring(0, 1))
          dois = Number(codigoBarra.substring(1, 2))
          tres = Number(codigoBarra.substring(2, 3))
          quatro = Number(codigoBarra.substring(3, 4))
          cinco = Number(codigoBarra.substring(4, 5))
          seis = Number(codigoBarra.substring(5, 6))
          sete = Number(codigoBarra.substring(6, 7))
          oito = Number(codigoBarra.substring(7, 8))
          nove = Number(codigoBarra.substring(8, 9))
          dez = Number(codigoBarra.substring(9, 10))
          onze = Number(codigoBarra.substring(10, 11))
          doze = Number(codigoBarra.substring(11, 12))
          soma =
            um * 1 +
            dois * 3 +
            tres * 1 +
            quatro * 3 +
            cinco * 1 +
            seis * 3 +
            sete * 1 +
            oito * 3 +
            nove * 1 +
            dez * 3 +
            onze * 1 +
            doze * 3
          soma = soma.toString()
          if (soma.slice(1) === '0') {
            codigoBarra += '0'
          } else {
            multiplo = Number(soma.slice(0, -1))
            multiplo = multiplo * 10 + 10
            resultado = multiplo - soma
            codigoBarra += resultado
          }

          produto = {
            descricao: `${nvl1.descricao} - ${res[i].descricao}`,
            nivel1: nvl1._id,
            nivel2: res[i]._id,
            vlrCusto: vlrCusto,
            vlrVenda: vlrVenda,
            codigo: codigo,
            codBarras: codigoBarra,
            pesquisa: pesquisa,
            coeficiente: coeficiente,
            favorito: false,
          }
          codigo++
          await new Produto(produto).save()
        }
      }

      // percorre o nivel3 montando os produtos
      Nivel3.find().then(async res => {
        for (let i = 0; i < res.length; i++) {
          if (res[i].ultimoNvl === 'sim') {
            nvl2 = await Nivel2.findById(res[i].chave)
            nvl1 = await Nivel1.findById(nvl2.chave)
            if (!res[i].pesquisa || res[i].pesquisa === 'não') {
              pesquisa = false
              coeficiente = null
              vlrCusto = res[i].valor
              vlrVenda = res[i].valorMax
            } else {
              pesquisa = true
              coeficiente = res[i].coeficiente
              vlrCusto = null
              vlrVenda = null
            }

            // gera o codigo de barras essa parte
            let codigoBarra = 123000000000
            codigoBarra += Number(codigo)
            codigoBarra = codigoBarra.toString()
            um = Number(codigoBarra.substring(0, 1))
            dois = Number(codigoBarra.substring(1, 2))
            tres = Number(codigoBarra.substring(2, 3))
            quatro = Number(codigoBarra.substring(3, 4))
            cinco = Number(codigoBarra.substring(4, 5))
            seis = Number(codigoBarra.substring(5, 6))
            sete = Number(codigoBarra.substring(6, 7))
            oito = Number(codigoBarra.substring(7, 8))
            nove = Number(codigoBarra.substring(8, 9))
            dez = Number(codigoBarra.substring(9, 10))
            onze = Number(codigoBarra.substring(10, 11))
            doze = Number(codigoBarra.substring(11, 12))
            soma =
              um * 1 +
              dois * 3 +
              tres * 1 +
              quatro * 3 +
              cinco * 1 +
              seis * 3 +
              sete * 1 +
              oito * 3 +
              nove * 1 +
              dez * 3 +
              onze * 1 +
              doze * 3
            soma = soma.toString()
            if (soma.slice(1) === '0') {
              codigoBarra += '0'
            } else {
              multiplo = Number(soma.slice(0, -1))
              multiplo = multiplo * 10 + 10
              resultado = multiplo - soma
              codigoBarra += resultado
            }

            produto = {
              descricao: `${nvl1.descricao} - ${nvl2.descricao} - ${res[i].descricao}`,
              nivel1: nvl1._id,
              nivel2: nvl2._id,
              nivel3: res[i]._id,
              vlrCusto: vlrCusto,
              vlrVenda: vlrVenda,
              codigo: codigo,
              codBarras: codigoBarra,
              pesquisa: pesquisa,
              coeficiente: coeficiente,
              favorito: false,
            }
            codigo++
            await new Produto(produto).save()
          }
        }

        // percorre o nivel3 montando os produtos
        Nivel4.find().then(async res => {
          for (let i = 0; i < res.length; i++) {
            if (res[i].ultimoNvl === 'sim') {
              nvl3 = await Nivel3.findById(res[i].chave)
              if (nvl3 !== null) {
                nvl2 = await Nivel2.findById(nvl3.chave)
                nvl1 = await Nivel1.findById(nvl2.chave)
                if (!res[i].pesquisa || res[i].pesquisa === 'não') {
                  pesquisa = false
                  coeficiente = null
                  vlrCusto = res[i].valor
                  vlrVenda = res[i].valorMax
                } else {
                  pesquisa = true
                  coeficiente = res[i].coeficiente
                  vlrCusto = null
                  vlrVenda = null
                }

                // gera o codigo de barras essa parte
                let codigoBarra = 123000000000
                codigoBarra += Number(codigo)
                codigoBarra = codigoBarra.toString()
                um = Number(codigoBarra.substring(0, 1))
                dois = Number(codigoBarra.substring(1, 2))
                tres = Number(codigoBarra.substring(2, 3))
                quatro = Number(codigoBarra.substring(3, 4))
                cinco = Number(codigoBarra.substring(4, 5))
                seis = Number(codigoBarra.substring(5, 6))
                sete = Number(codigoBarra.substring(6, 7))
                oito = Number(codigoBarra.substring(7, 8))
                nove = Number(codigoBarra.substring(8, 9))
                dez = Number(codigoBarra.substring(9, 10))
                onze = Number(codigoBarra.substring(10, 11))
                doze = Number(codigoBarra.substring(11, 12))
                soma =
                  um * 1 +
                  dois * 3 +
                  tres * 1 +
                  quatro * 3 +
                  cinco * 1 +
                  seis * 3 +
                  sete * 1 +
                  oito * 3 +
                  nove * 1 +
                  dez * 3 +
                  onze * 1 +
                  doze * 3
                soma = soma.toString()
                if (soma.slice(1) === '0') {
                  codigoBarra += '0'
                } else {
                  multiplo = Number(soma.slice(0, -1))
                  multiplo = multiplo * 10 + 10
                  resultado = multiplo - soma
                  codigoBarra += resultado
                }

                produto = {
                  descricao: `${nvl1.descricao} - ${nvl2.descricao} - ${nvl3.descricao} -  ${res[i].descricao}`,
                  nivel1: nvl1._id,
                  nivel2: nvl2._id,
                  nivel3: nvl3._id,
                  nivel4: res[i]._id,
                  vlrCusto: vlrCusto,
                  vlrVenda: vlrVenda,
                  codigo: codigo,
                  codBarras: codigoBarra,
                  pesquisa: pesquisa,
                  coeficiente: coeficiente,
                  favorito: false,
                }
                codigo++
                await new Produto(produto).save()
              }
            }
          }
        })
      })
    })
  })

  res.send('ok')
})

// rota que remove todos os produtos que nao forem favoritos
router.get('/rm_produto', isAuth, async (req, res) => {
  await Produto.deleteMany({ favorito: false })
  res.send('ok')
})

router.post('/gerar_nfe', isAuth, async (req, res, next) => {
  try {
    const { idAvaliacao, dataEmissao } = req.body
    const avaliacao = await Avaliacao.findById(idAvaliacao)

    let evento = '' //

    let jaEmitiu = false

    for (let i = 0; i < avaliacao.eventosFiscais.length; i++) {
      if (typeof avaliacao.eventosFiscais[i].excecao === 'undefined') {
        if (avaliacao.eventosFiscais[i].status === '100') {
          evento = 'Nota já emitida'
          jaEmitiu = true
        }
      }
    }

    if (jaEmitiu) {
      return res.status(400).send(evento)
    }

    const counter = await Caixa.findById(avaliacao.caixa)
    let company = await Empresa.findOne({ cnpj: avaliacao.cnpj })
    company = JSON.stringify(company)
    company = JSON.parse(company)
    const { products, sum, sum_icms, discount, others } = await getItems(avaliacao, company)
    const tx2Payments = [
      {
        tPag_YA02: '01',
        vPag_YA03: 0,
      },
    ]

    const user = await Usuario.findById(counter.idUsuario)
    const client = await Cliente.findOne({ cpf: avaliacao.cpf })
    const tx2FileName =
      randomstring.generate({
        length: 12,
        charset: 'alphabetic',
      }) + '.tx2'

    const cnf_b03 = (await tx2.generatecNF_B03()).toString()
    const notaData = getNotaData(company, user, cnf_b03, 55, 3, dataEmissao)
    const issuer = getIssuer(company)
    const clientData = getClient(client)
    const totalizer = getTotalizer(sum, discount, others, company, sum_icms)
    const technician = getTechnician()
    const cnpjContador = company.cnpjContador ? company.cnpjContador : ''

    const tx2File = await tx2.generateNFeTx2(
      tx2FileName,
      notaData,
      issuer,
      clientData,
      products,
      tx2Payments,
      totalizer,
      technician,
      cnpjContador,
      '',
      {}
    )

    const response = await tx2.sendNFe(
      tx2File,
      avaliacao.cnpj,
      'CresciPerdi',
      'Basic YWRtaW46ZGZjb20xNDc=',
      tecnoSpeedPort,
      amb
    )

    const uploadData = await uploadTx2File(tx2File)

    deleteTx2File(tx2FileName)

    const info = response.split(',')
    let status = 200
    if (info[0] !== 'EXCEPTION') {
      evento = {
        serie: info[0],
        chave: info[1],
        status: info[2],
        motivo: info[3],
        numero: info[4],
        dataEmissao: info[5],
        situacao: info[6],
        codNf: cnf_b03,
        ...uploadData,
      }
    } else {
      evento = {
        resposta: info[0],
        tipoException: info[1],
        mensagem: info[2],
        codNf: cnf_b03,
        // serie: user.serie
        ...uploadData,
      }
      status = 400
    }

    let eventosFiscais = avaliacao.eventosFiscais
    eventosFiscais.push(evento)
    await Avaliacao.findByIdAndUpdate(idAvaliacao, {
      eventosFiscais: eventosFiscais,
    })
    res.status(status).send(evento)
  } catch (e) {
    return next(new HttpException(500, 'Algo deu errado. Tente novamente, por favor.', e))
  }
})

function getNotaData(company, user, cnf_b03, mod, tpImp, dataEmissao = '') {
  const dhEmi_B09 = dataEmissao
    ? moment(dataEmissao, 'YYYY-MM-DD')
        .set({ hours: 12 })
        .tz('America/Sao_Paulo')
        .format('YYYY-MM-DDTHH:mm:ssZ')
    : moment.tz('America/Sao_Paulo').format('YYYY-MM-DDTHH:mm:ssZ')

  const data = {
    versao_A02: '4.00',
    cUF_B02: company.codUf,
    cNF_B03: cnf_b03,
    natOp_B04: 'Compra para comercialização',
    mod_B06: mod,
    serie_B07: 1,
    nNF_B08: 200,
    dhEmi_B09,
    tpNF_B11: 0,
    idDest_B11a: 1,
    cMunFG_B12: parseInt(company.codCidade),
    tpImp_B21: tpImp,
    tpEmis_B22: 1,
    cDV_B23: 0,
    tpAmb_B24: process.env.PRODUCTION === 'true' ? 1 : 2,
    finNFe_B25: 1,
    indFinal_B25a: 1,
    indPres_B25b: 1,
    procEmi_B26: 0,
    verProc_B27: 'dfcomonline 1.0',
    CamposRetorno: 'serie, chave, cstat, motivo, numero, dtemissao, situacao',
  }

  const contingencyUFSVCRS = ['AM', 'BA', 'CE', 'GO', 'MA', 'MS', 'MT', 'PA', 'PE', 'PI', 'PR']

  // Fluxo Provisório Contingency ( TODO: Deve ser posteriormente automatizado )
  if (company?.contingencyNFEMode) {
    data['dhCont_B28'] = dhEmi_B09
    data['xJust_B29'] = 'Modo de Contingência'
    data['tpEmis_B22'] = 6
    // Estados de uso isolado
    if (contingencyUFSVCRS.includes(company.uf)) {
      data['tpEmis_B22'] = 7
    }
  }

  return data
}

function getIssuer(company) {
  return {
    CNPJ_C02: company.cnpj,
    xNome_C03: company?.razaoSocial || company.nomeReduzido,
    xFant_C04: company?.razaoSocial || company.nomeReduzido,
    xLgr_C06: company.endereco,
    nro_C07: parseInt(company.numero),
    xCpl_C08: '',
    xBairro_C09: company.bairro,
    cMun_C10: company.codCidade,
    xMun_C11: company.cidade,
    UF_C12: company.uf,
    CEP_C13: company.cep.replace(/\D/g, ''),
    cPais_C14: '1058',
    xPais_C15: 'BRASIL',
    indIEDest_E16a: 9,
    IE_C17: company.ie.replace(/\D/g, ''),
    IEST_C18: '',
    CRT_C21: company.crt,
  }
}

async function getItems(evaluation, company) {
  let products = []
  let sum = 0
  let sum_icms = 0
  let counter = 1
  let product
  let value
  let discount = 0
  let others = 0

  if (evaluation.manual) {
    if (evaluation.tipo === 'Vale') {
      products = [
        {
          nItem_H02: counter,
          cEAN_I03: 'SEM GTIN',
          cProd_I02: 0,
          xProd_I04:
            process.env.PRODUCTION === 'true'
              ? 'Produto Seminovo'
              : 'NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL',
          NCM_I05: '63090010',
          CEST_I05c: '0000000',
          CFOP_I08: '1102',
          uCom_I09: 'UND',
          qCom_I10: 1,
          vUnCom_I10a: Number(evaluation.totalValeEfetivado).toFixed(2),
          vProd_I11: Number(evaluation.totalValeEfetivado).toFixed(2),
          cEANTrib_I12: 'SEM GTIN',
          uTrib_I13: 'UND',
          qTrib_I14: 1.0,
          vUnTrib_I14a: Number(evaluation.totalValeEfetivado).toFixed(2),
          indTot_I17b: 1,
          orig_N11: 0,
          CST_Q06: '06',
          CST_S06: '06',
          CSOSN_N12a: '102', //simples
        },
      ]
      if (company.crt !== 1) {
        products[0].CST_N12 = '00'
        products[0].modBC_N13 = '0'
        products[0].vBC_N15 = Number(evaluation.totalValeEfetivado).toFixed(2)
        products[0].pICMS_N16 = '18'
        products[0].vICMS_N17 = Number(evaluation.totalValeEfetivado * 0.18).toFixed(2)
        sum_icms = Number(products[0].vICMS_N17)
        delete products[0].CSOSN_N12a
      }
      sum = Number(evaluation.totalValeEfetivado)
    } else {
      products = [
        {
          nItem_H02: counter,
          cEAN_I03: 'SEM GTIN',
          cProd_I02: 0,
          xProd_I04:
            process.env.PRODUCTION === 'true'
              ? 'Produto Seminovo'
              : 'NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL',
          NCM_I05: '63090010',
          CEST_I05c: '0000000',
          CFOP_I08: '1102',
          uCom_I09: 'UND',
          qCom_I10: 1,
          vUnCom_I10a: Number(evaluation.totalDinheiroEfetivado).toFixed(2),
          vProd_I11: Number(evaluation.totalDinheiroEfetivado).toFixed(2),
          cEANTrib_I12: 'SEM GTIN',
          uTrib_I13: 'UND',
          qTrib_I14: 1.0,
          vUnTrib_I14a: Number(evaluation.totalDinheiroEfetivado).toFixed(2),
          indTot_I17b: 1,
          orig_N11: 0,
          CST_Q06: '06',
          CST_S06: '06',
          CSOSN_N12a: '102',
        },
      ]
      if (company.crt !== 1) {
        products[0].CST_N12 = '00'
        products[0].modBC_N13 = '0'
        products[0].vBC_N15 = Number(evaluation.totalDinheiroEfetivado).toFixed(2)
        products[0].pICMS_N16 = '18'
        products[0].vICMS_N17 = Number(evaluation.totalDinheiroEfetivado * 0.18).toFixed(2)
        sum_icms = Number(products[0].vICMS_N17)
        delete products[0].CSOSN_N12a
      }
      sum = Number(evaluation.totalDinheiroEfetivado)
    }
  } else {
    let maiorValor = 0
    let indexDoMaior

    for (let i = 0; i < evaluation.items.length; i++) {
      product = await Produto.findById(evaluation.items[i].idProduto)

      if (typeof evaluation.items[i].precoBruto !== 'undefined') {
        if (evaluation.items[i].precoBruto > 2000) {
          value = evaluation.items[i].precoBruto / 6
        } else {
          value = evaluation.items[i].precoBruto / 5
        }
      } else {
        value = product.vlrCusto
      }

      sum += evaluation.items[i].qtd * value

      products.push({
        nItem_H02: counter,
        cEAN_I03: 'SEM GTIN',
        cProd_I02: product.codigo,
        xProd_I04: product.descricao,
        NCM_I05: '63090010',
        CEST_I05c: '0000000',
        CFOP_I08: '1102',
        uCom_I09: 'UND',
        qCom_I10: evaluation.items[i].qtd.toFixed(2),
        vUnCom_I10a: value.toFixed(2),
        vProd_I11: (evaluation.items[i].qtd * value).toFixed(2),
        cEANTrib_I12: 'SEM GTIN',
        uTrib_I13: 'UND',
        qTrib_I14: evaluation.items[i].qtd.toFixed(2),
        vUnTrib_I14a: value.toFixed(2),
        indTot_I17b: 1,
        orig_N11: 0,
        CST_Q06: '06',
        CST_S06: '06',
        CSOSN_N12a: '102',
      })

      counter += 1

      if (company.crt !== 1) {
        products[i].CST_N12 = '00'
        products[i].modBC_N13 = '0'
        products[i].vBC_N15 = (evaluation.items[i].qtd * value).toFixed(2)
        products[i].pICMS_N16 = '18'
        products[i].vICMS_N17 = Number(evaluation.items[i].qtd * value * 0.18).toFixed(2)
        sum_icms += Number(products[i].vICMS_N17)
        delete products[i].CSOSN_N12a
      }

      // encontra o maior valor entre os produtos
      if (Number(maiorValor) < Number(evaluation.items[i].qtd * value).toFixed(2)) {
        maiorValor = (evaluation.items[i].qtd * value).toFixed(2)
        indexDoMaior = i
      }
    }

    let diferenca
    if (evaluation.tipo === 'Vale') {
      diferenca = Number(evaluation.totalValeEfetivado) - sum
    } else {
      diferenca = Number(evaluation.totalDinheiroEfetivado) - sum
    }

    if (diferenca < 0) {
      discount = diferenca * -1
      products[indexDoMaior].vDesc_I17 = discount.toFixed(2)
    } else if (diferenca > 0) {
      others = diferenca
      products[indexDoMaior].vOutro_I17a = others.toFixed(2)
    }
  }

  return {
    sum: sum.toFixed(2),
    sum_icms,
    products: products,
    discount: discount,
    others: others,
  }
}

function getClient(client) {
  return {
    CPF_E03: client.cpf,
    idEstrangeiro_E03a: '',
    xNome_E04:
      process.env.PRODUCTION === 'true'
        ? client.nome
        : 'NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL',
    xLgr_E06: client.rua || 'SEM LOGRADOURO',
    nro_E07: client.numero || 1,
    xBairro_E09: client.bairro || 'SEM BAIRRO',
    cMun_E10: client.cod_cidade,
    xMun_E11: client.cidade || 'SEM CIDADE',
    UF_E12: client.estado,
  }
}

function getTechnician() {
  return {
    CNPJ_ZD02: '23212902000197',
    email_ZD05: '<EMAIL>',
    fone_ZD06: '1936637938',
    xContato_ZD04: 'Eduardo',
  }
}

function getTotalizer(sum, discount, others, company, sum_icms) {
  let w03, w04
  if (company.crt === 1) {
    w03 = 0
    w04 = 0
  } else {
    w03 = sum
    w04 = Number(sum_icms).toFixed(2)
  }
  return {
    vBC_W03: w03,
    vICMS_W04: w04,
    vICMSDeson_W04a: 0,
    vFCPUFDest_W04c: 0,
    VICMSUFDest_W04e: 0,
    vProd_W07: sum,
    vNF_W16: Number(sum - discount + others).toFixed(2),
    modFrete_X02: 9,
    VICMSUFRemet_W04g: 0,
    infCpl_Z03: 0,
    vBCST_W05: 0,
    vCOFINS_W14: 0,
    vDesc_W10: discount.toFixed(2),
    vFCPSTRet_W06b: 0,
    vFCPST_W06a: 0,
    vFCP_W04h: 0,
    vFrete_W08: 0,
    vII_W11: 0,
    vIPIDevol_W12a: 0,
    vIPI_W12: 0,
    vOutro_W15: others.toFixed(2),
    vPIS_W13: 0,
    vST_W06: 0,
    vSeg_W09: 0,
    vTotTrib_W16a: 0,
  }
}

router.post('/consultar_nfe', isAuth, async (req, res) => {
  const { cnpj, codNf } = req.body
  var config = {
    method: 'get',
    url: `${tecnoSpeedBaseUrl}/ManagerAPIWeb/nfe/consulta?grupo=CresciPerdi&cnpj=${cnpj}&Filtro=serie=1 and codnf=${codNf}&campos=situacao,chave`,
    headers: {
      Authorization: 'Basic YWRtaW46ZGZjb20xNDc=',
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  }
  axios(config)
    .then(function (response) {
      return res.status(200).send(response.data)
    })
    .catch(function (error) {
      return res.status(400).send(error)
    })
})

router.post('/cancelar_nfe', isAuth, async (req, res) => {
  const { cnpj, chavenota, justificativa } = req.body
  var data = qs.stringify({
    grupo: 'CresciPerdi',
    cnpj: cnpj,
    chavenota: chavenota,
    justificativa: justificativa,
  })
  var config = {
    method: 'post',
    url: `${tecnoSpeedBaseUrl}/ManagerAPIWeb/nfe/cancela`,
    headers: {
      Authorization: 'Basic YWRtaW46ZGZjb20xNDc=',
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  }

  axios(config)
    .then(function (response) {
      return res.status(200).send(response.data)
    })
    .catch(function (error) {
      return res.status(400).send(error)
    })
})

router.post('/imprimeNf', isAuth, async (req, res) => {
  const { cnpj, chavenota } = req.body
  var config = {
    method: 'get',
    url: `${tecnoSpeedBaseUrl}/ManagerAPIWeb/nfe/imprime?grupo=CresciPerdi&cnpj=${cnpj}&chavenota=${chavenota}&url=1`,
    headers: {
      Authorization: 'Basic YWRtaW46ZGZjb20xNDc=',
    },
  }

  axios(config)
    .then(function (response) {
      return res.status(200).send(response.data)
    })
    .catch(function (error) {
      return res.status(400).send(error)
    })
})

router.post('/status_sefaz', isAuth, async (req, res) => {
  const { cnpj } = req.body
  var config = {
    method: 'get',
    url: `${tecnoSpeedBaseUrl}/ManagerAPIWeb/nfe/status?grupo=CresciPerdi&cnpj=${cnpj}`,
    headers: {
      Authorization: 'Basic YWRtaW46ZGZjb20xNDc=',
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  }
  axios(config)
    .then(function (response) {
      return res.status(200).send(response.data)
    })
    .catch(function (error) {
      return res.status(400).send(error)
    })
})

router.post('/descartar_nfe', isAuth, async (req, res) => {
  const { cnpj, chavenota } = req.body
  var data = qs.stringify({
    grupo: 'CresciPerdi',
    cnpj: cnpj,
    chavenota: chavenota,
  })
  var config = {
    method: 'post',
    url: `${tecnoSpeedBaseUrl}/ManagerAPIWeb/nfe/descarta`,
    headers: {
      Authorization: 'Basic YWRtaW46ZGZjb20xNDc=',
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  }

  axios(config)
    .then(function (response) {
      return res.status(200).send(response.data)
    })
    .catch(function (error) {
      return res.status(400).send(error)
    })
})

router.patch('/update_eventosFiscais', isAuth, async (req, res) => {
  const { eventosFiscais } = req.body
  const update = {
    serie: eventosFiscais.serie,
    chave: eventosFiscais.chave,
    status: eventosFiscais.status,
    motivo: eventosFiscais.motivo,
    numero: eventosFiscais.numero,
    dataEmissao: eventosFiscais.dataEmissao,
    codNf: eventosFiscais.codNf,
    situacao: eventosFiscais.situacao,
  }
  await Avaliacao.findByIdAndUpdate(eventosFiscais.idAvaliacao, {
    $push: { eventosFiscais: update },
  })
  return res.send('Atualizado array de eventos fiscais')
})

// rota que busca todas as avaliações que possuem eventos fiscais
router.get('/buscar_nfes/:cnpj', isAuth, async (req, res) => {
  const { cnpj } = req.params
  const avaliacoes = await Avaliacao.find({
    'eventosFiscais.0': { $exists: true },
    cnpj: cnpj,
  }).sort({ dataInicio: -1 })
  return res.send(avaliacoes)
})

router.get('/importar_json_mongo', (req, res) => {
  const array = [
    {
      _id: ObjectId('603d3a1a7b820c5638adf4e3'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '29',
      ativo: true,
      razaoSocial: 'SUELLEN CRESCI E PERDI ALFENAS LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AV GOVERNADOR VALADARES',
      numero: '671',
      bairro: 'CENTRO',
      codUf: 31,
      uf: 'MG',
      codCidade: '3101607',
      cep: '37.130-215',
      cnpj: '40593869000101',
      ie: null,
      telefone: '(35) 3295-4993',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Alfenas',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4e4'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '10',
      ativo: true,
      razaoSocial: 'NATALIA CRISTINA PEDROSO',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA DOUTOR CANDIDO CRUZ',
      numero: '94',
      bairro: 'VILA REHDER',
      codUf: 35,
      uf: 'SP',
      codCidade: '3501608',
      cep: '13.465-350',
      cnpj: '33589489000138',
      ie: null,
      telefone: '(19) 8430-3130 / (19) 3608-6179 / (19) 3608-6179',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 3300,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: true,
      __v: 0,
      cidade: 'Americana',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4e5'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '81',
      ativo: true,
      razaoSocial: 'PRISCILA GABRIELA PIZZI LOPES LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA BERNARDINO DE CAMPOS',
      numero: '40',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '2500734',
      cep: '13.900-400',
      cnpj: '39668879000180',
      ie: null,
      telefone: '(19) 9170-2709',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Amparo',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4e7'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '90',
      ativo: true,
      razaoSocial: 'LUCIANA LEONE BARROS DIAS',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA AMERICO PEREIRA LIMA',
      numero: '50',
      bairro: 'JARDIM LAVINIA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3502804',
      cep: '13.736-260',
      cnpj: '12843114000182',
      ie: null,
      telefone: '(19) 3666-5757 / (19) 3665-3207',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Araçatuba',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4e8'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '22',
      ativo: true,
      razaoSocial: 'ROSEANE DOS SANTOS VIEIRA SOARES 09762071689',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA MATO GROSSO',
      numero: '1865',
      bairro: 'CENTRO',
      codUf: 31,
      uf: 'MG',
      codCidade: '3103504',
      cep: '38.440-046',
      cnpj: '34164371000120',
      ie: null,
      telefone: '(34) 8700-5008',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Araguari',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4e6'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '35',
      ativo: true,
      razaoSocial: 'BRAMBILLA & BRAMBILLA LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA RICARTI TEIXEIRA',
      numero: '61',
      bairro: 'CENTRO',
      codUf: 31,
      uf: 'MG',
      codCidade: '3102605',
      cep: '37.795-000',
      cnpj: '34643648000106',
      ie: null,
      telefone: '(35) 3551-5043',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Andradas',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4e9'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '1',
      ativo: true,
      razaoSocial: 'ARACELLI P. FRANCHI DE OLIVEIRA',
      nomeReduzido: 'CRESCI E PERDI ',
      endereco: 'AV MAUA',
      numero: '527',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3503208',
      cep: '14.801-190',
      cnpj: '33242250000197',
      ie: '***************',
      telefone: '(19) 3663-2090',
      celular: '(19) 98172-1231',
      email: '<EMAIL>',
      dtInauguracao: Date('2019-01-01T00:00:00.000Z'),
      vlrRoyalties: 2000,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Araraquara',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4ea'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '41',
      ativo: true,
      razaoSocial: 'RICHARD MATTHEWS BAPTISTA DE LIMA',
      nomeReduzido: null,
      endereco: 'AVENIDA PADRE ALARICO ZACHARIAS',
      numero: '1142',
      bairro: 'PARQUE INDUSTRIAL',
      codUf: 35,
      uf: 'SP',
      codCidade: '3503307',
      cep: '13.601-343',
      cnpj: '36170230000146',
      ie: null,
      telefone: '(19) 3813-2132',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Araras',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4eb'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '84',
      ativo: true,
      razaoSocial: 'RENATA NAIANA DA SILVA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AV JOAO MOREIRA SALES',
      numero: '94',
      bairro: 'ARASOL',
      codUf: 31,
      uf: 'MG',
      codCidade: '3104007',
      cep: '38.182-264',
      cnpj: '39824183000103',
      ie: null,
      telefone: '(34) 8804-6870',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Araxá',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4ed'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '66',
      ativo: true,
      razaoSocial: 'ERICA C.A.DE OLIVEIRA MOREIRA',
      nomeReduzido: 'CRESCI E PERDI ',
      endereco: 'ALAMEDA PROFESSOR LUCAS NOGUEIRA GARCEZ',
      numero: '2986',
      bairro: 'JARDIM DO LAGO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3504107',
      cep: '12.947-000',
      cnpj: '36670828000102',
      ie: null,
      telefone: '(19) 3663-1136',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Atibaia',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4ef'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '83',
      ativo: true,
      razaoSocial: 'TAMARA RAMIRES CARLOS',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R CAMPOS SALES',
      numero: '318',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3505708',
      cep: '06.401-000',
      cnpj: '28806139000146',
      ie: null,
      telefone: '(19) 3608-1396',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Barueri',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4ec'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '72',
      ativo: true,
      razaoSocial: 'MONISE FRANCISCO BRB LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA DOUTOR FERNANDO ARENS JUNIOR',
      numero: '878',
      bairro: 'VILA QUEIROZ',
      codUf: 35,
      uf: 'SP',
      codCidade: '3503802',
      cep: '13.163-004',
      cnpj: '38210906000103',
      ie: null,
      telefone: '(19) 3813-2132',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Artur Nogueira',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4ee'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '43',
      ativo: true,
      razaoSocial: 'FERNANDES & NEVES LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA 20',
      numero: '2480',
      bairro: 'AEROPORTO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3505500',
      cep: '14.783-242',
      cnpj: '36192890000128',
      ie: null,
      telefone: '(35) 9975-8715',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Barretos',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4f0'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '76',
      ativo: true,
      razaoSocial: 'LARA STORE LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R ANTONIO ALVES',
      numero: '22-30',
      bairro: 'VILA SANTA TEREZA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3506003',
      cep: '17.012-060',
      cnpj: '39496507000113',
      ie: null,
      telefone: '(19) 3867-0020',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Bauru',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4f1'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '62',
      ativo: true,
      razaoSocial: 'ST BAZAR E OUTLET INFANTIL LTDA',
      nomeReduzido: 'CRESCI E PERDI ',
      endereco: 'AV PREFEITO EDNE JOSE PIFFER',
      numero: '748',
      bairro: 'RESIDENCIAL DOUTOR HERCULES PEREIRA HORTAL',
      codUf: 35,
      uf: 'SP',
      codCidade: '3506102',
      cep: '14.711-622',
      cnpj: '38000053000185',
      ie: null,
      telefone: '(17) 9130-4997',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Bebedouro',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4f2'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '61',
      ativo: true,
      razaoSocial: 'LAIS LUCATELLI MAGALHAES',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R MAJOR MATHEUS',
      numero: '703',
      bairro: 'VILA DOS LAVRADORES',
      codUf: 35,
      uf: 'SP',
      codCidade: '3507506',
      cep: '18.609-083',
      cnpj: '38049338000100',
      ie: null,
      telefone: '(35) 9752-1279',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Botucatu',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4f3'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '50',
      ativo: true,
      razaoSocial: 'MONISE GONCALVES PEREIRA MENATTI COSTA',
      nomeReduzido: 'CRESCI E PERDI ',
      endereco: 'RUA CORONEL TEOFILO LEME',
      numero: '1008',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3507605',
      cep: '12.900-002',
      cnpj: '37133989000111',
      ie: null,
      telefone: '(11) 4032-8345',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Bragança Paulista',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4f4'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '26',
      ativo: true,
      razaoSocial: 'ARCO-IRIS COMERCIO DE ARTIGOS INFANTIS EIRELI',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R ALVARENGA',
      numero: '1088',
      bairro: 'BUTANTA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3550308',
      cep: '05.509-001',
      cnpj: '34296069000126',
      ie: null,
      telefone: '(11) 2910-3137',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'São Paulo',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4f5'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '11',
      ativo: true,
      razaoSocial: 'MARINA DA SILVA SOUZA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA OROSIMBO MAIA',
      numero: '1087',
      bairro: 'VILA ITAPURA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3509502',
      cep: '13.023-002',
      cnpj: '34040500000179',
      ie: null,
      telefone: '(19) 9561-4628 / (19) 3608-6179',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Campinas',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4f6'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '63',
      ativo: true,
      razaoSocial: 'MARINA DA SILVA SOUZA',
      nomeReduzido: 'CRESCI E PERDI ',
      endereco: 'RUA THOMAZ ORTALE',
      numero: '157',
      bairro: 'JARDIM PROENCA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3509502',
      cep: '13.026-290',
      cnpj: '34040500000250',
      ie: null,
      telefone: '(19) 3608-6179 / (19) 8722-1283 / (19) 3608-6179',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Campinas',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4f8'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '89',
      ativo: true,
      razaoSocial: 'CRISTIANO NUNES',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R DR CESAR CASTIGLIONI JUNIOR',
      numero: '62',
      bairro: 'CASA VERDE',
      codUf: 35,
      uf: 'SP',
      codCidade: '3550308',
      cep: '02.515-000',
      cnpj: '40463806000130',
      ie: null,
      telefone: '(11) 2658-6294',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'São Paulo',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4f9'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '49',
      ativo: true,
      razaoSocial: 'DANILO MOREIRA SOARES 05807002750',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA AV JOSE MARCELINO',
      numero: '790',
      bairro: 'NOSSA SENHORA DE FATIMA',
      codUf: 52,
      uf: 'GO',
      codCidade: '5205109',
      cep: '75.701-970',
      cnpj: '36106721000128',
      ie: null,
      telefone: '(34) 9106-4461',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Catalão',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4fc'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '25',
      ativo: true,
      razaoSocial: 'LUCAS FRANCHI FAYA',
      nomeReduzido: 'CRESCI E PERDI FRANCA',
      endereco: 'AVENIDA DOUTOR ISMAEL ALONSO Y. ALONSO',
      numero: '1606',
      bairro: 'SAO JOSE',
      codUf: 35,
      uf: 'SP',
      codCidade: '3516200',
      cep: '14.403-430',
      cnpj: '34197804000144',
      ie: null,
      telefone: '(19) 3663-1783',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Franca',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4fd'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '59',
      ativo: true,
      razaoSocial: 'GLAUCIA CRISTINA MARINOTO DE SOUZA LOJA INFANTIL',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AV SETE DE SETEMBRO',
      numero: '500',
      bairro: 'VILA MARTINHO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3516408',
      cep: '07.852-000',
      cnpj: '37980902000141',
      ie: null,
      telefone: '(11) 8765-7197',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Franco da Rocha',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4fe'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '88',
      ativo: true,
      razaoSocial: 'AGATA COMERCIO DE ARTIGOS INFANTIS LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AV REPUBLICA DO LIBANO',
      numero: '2540',
      bairro: 'SET OESTE',
      codUf: 52,
      uf: 'GO',
      codCidade: '5208707',
      cep: '74.115-030',
      cnpj: '40348228000191',
      ie: null,
      telefone: '(62) 9982-7714',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Goiânia',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4ff'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '28',
      ativo: true,
      razaoSocial: 'LEANDRO ALVES DE MELLO 21560389885',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R DR. ANTONIO DOS SANTOS CORAGEM',
      numero: '72',
      bairro: 'CENTRO',
      codUf: 31,
      uf: 'MG',
      codCidade: '3128709',
      cep: '37.800-000',
      cnpj: '29070324000188',
      ie: null,
      telefone: '(35) 3551-5043',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Guaxupé',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf500'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '17',
      ativo: true,
      razaoSocial: 'CARINA APARECIDA BATISTA SABADINI',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA APARICIO DA COSTA CAMARGO',
      numero: '604',
      bairro: 'LOTEAMENTO REMANSO CAMPINEIRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3519071',
      cep: '13.184-322',
      cnpj: '33794428000102',
      ie: null,
      telefone: '(19) 9131-1104 / (19) 3608-6179 / (19) 3608-6179',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Hortolândia',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf501'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '56',
      ativo: true,
      razaoSocial: 'CM BAZAR INFANTIL LTDA',
      nomeReduzido: 'CRESCI E PERDI ',
      endereco: 'AVENIDA ENGENHEIRO FABIO ROBERTO BARNABE',
      numero: '4554',
      bairro: 'JARDIM COLONIAL',
      codUf: 35,
      uf: 'SP',
      codCidade: '3520509',
      cep: '13.348-670',
      cnpj: '37782212000188',
      ie: null,
      telefone: '(19) 3813-2132',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Indaiatuba ',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf502'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '92',
      ativo: true,
      razaoSocial: 'JED COMERCIO VAREJISTA DE ARTIGOS USADOS LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AV NAZARE',
      numero: '605',
      bairro: 'IPIRANGA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3550308',
      cep: '04.263-000',
      cnpj: '40903341000191',
      ie: null,
      telefone: '(11) 5870-9397',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'São Paulo ',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf503'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '68',
      ativo: true,
      razaoSocial: 'CRESCI E PERDI BAZAR E OUTLET INFANTIL LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA DOUTOR VIRGILIO DE REZENDE',
      numero: '856',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3522307',
      cep: '18.200-046',
      cnpj: '38097709000120',
      ie: null,
      telefone: '(15) 3272-6329',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Itapetininga',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf504'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '18',
      ativo: true,
      razaoSocial: 'PABLO ARRUDA BATISTA DE LIMA JUNIOR',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA DOUTOR MARIO DA FONSECA',
      numero: '230',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3522604',
      cep: '13.970-275',
      cnpj: '31511583000149',
      ie: null,
      telefone: '(19) 8219-2042',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Itapira',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf505'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '77',
      ativo: true,
      razaoSocial: 'LUCIANO BALICO MODA INFANTIL EIRELI',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R BENJAMIN CONSTANT',
      numero: '193',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3523404',
      cep: '13.250-340',
      cnpj: '38479539000148',
      ie: null,
      telefone: '(11) 4524-9700',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Itatiba',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf506'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '42',
      ativo: true,
      razaoSocial: 'M&N BAZAR E OUTLET LTDA',
      nomeReduzido: 'CRESCI E PERDI ',
      endereco: 'RUA LEDA MARIA TOLEDO ASSUMPCAO MENABO',
      numero: '10',
      bairro: 'ITU NOVO CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3523909',
      cep: '13.303-521',
      cnpj: '36241348000118',
      ie: null,
      telefone: '(19) 3813-2132',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Itu',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf507'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '37',
      ativo: true,
      razaoSocial: 'BAZAR OLIVEIRA DE ALMEIDA LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R VINTE E SEIS',
      numero: '512',
      bairro: 'CENTRO',
      codUf: 31,
      uf: 'MG',
      codCidade: '3134202',
      cep: '38.300-080',
      cnpj: '34967180000105',
      ie: null,
      telefone: '(34) 9993-8853',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Ituiutaba',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf508'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '53',
      ativo: true,
      razaoSocial: 'RAFRAN COMERCIO DE VESTUARIO LTDA',
      nomeReduzido: 'CRESCI E PERDI ',
      endereco: 'RUA FLORIANO PEIXOTO',
      numero: '540',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3524303',
      cep: '14.870-370',
      cnpj: '37449849000157',
      ie: null,
      telefone: '(16) 9148-9322',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Jaboticabal ',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf509'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '13',
      ativo: true,
      razaoSocial: 'SERGIO TANAKA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA LAURO DE CARVALHO',
      numero: '1124',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3524709',
      cep: '13.910-025',
      cnpj: '33129320000103',
      ie: null,
      telefone: '(19) 8349-4947',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Jaguariúna',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf50a'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '73',
      ativo: true,
      razaoSocial: 'D L ACCESSOR NARDINI',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AV ZEZINHO MAGALHAES',
      numero: '605',
      bairro: 'JARDIM ESTADIO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3525300',
      cep: '17.203-380',
      cnpj: '38110205000100',
      ie: null,
      telefone: '(19) 3813-2132',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Jaú',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf50b'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '27',
      ativo: true,
      razaoSocial: 'RAISSA FRANCHI DE OLIVEIRA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R ATILIO VIANELLO',
      numero: '300',
      bairro: 'VILA VIANELO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3525904',
      cep: '13.207-130',
      cnpj: '34412242000104',
      ie: null,
      telefone: '(19) 3663-1148/ (19) 8181-3489',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Jundiaí',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf50c'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '71',
      ativo: true,
      razaoSocial: 'ANDREAS BETZ COMERCIO DE ARTIGOS INFANTIS LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA CERRO CORA',
      numero: '475',
      bairro: 'VILA ROMANA',
      codUf: 35,
      uf: 'SP',
      codCidade: '4113205',
      cep: '05.061-050',
      cnpj: '38483283000142',
      ie: null,
      telefone: '(11) 8279-3125',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Lapa',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf50d'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '54',
      ativo: true,
      razaoSocial: 'CRESCI E PERDI LAVRAS COMERCIO VAREJISTA LTDA',
      nomeReduzido: 'CRESCI E PERDI ',
      endereco: 'AV DOUTOR SILVIO MENICUCCI',
      numero: '342',
      bairro: 'CENTRO',
      codUf: 31,
      uf: 'MG',
      codCidade: '3138203',
      cep: '37.200-169',
      cnpj: '37722977000122',
      ie: null,
      telefone: '(35) 9947-0337',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Lavras',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf50e'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '23',
      ativo: true,
      razaoSocial: 'MILTON EDUARDO MANHARELLI',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AV 29 DE AGOSTO',
      numero: '1000',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3526704',
      cep: '13.610-210',
      cnpj: '34487403000129',
      ie: null,
      telefone: '(19) 9650-4800/ (19) 3608-6179',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Leme',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf50f'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '4',
      ativo: true,
      razaoSocial: 'MARIA APARECIDA MARTIN DA SILVA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R TREZE DE MAIO',
      numero: '257',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3526902',
      cep: '13.480-171',
      cnpj: '32787238000103',
      ie: null,
      telefone: '(19) 3453-9080',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 2700,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Limeira',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf510'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '87',
      ativo: true,
      razaoSocial: 'LUPE COMERCIO DE ARTIGOS INFANTIS LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AV MARINGA',
      numero: '1453',
      bairro: 'VITORIA',
      codUf: 41,
      uf: 'PR',
      codCidade: '4113700',
      cep: '86.060-000',
      cnpj: '40797895000151',
      ie: null,
      telefone: '(19) 9866-8843',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Londrina',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf512'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '91',
      ativo: true,
      razaoSocial: 'BENTO E FRANCHI LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R BAHIA',
      numero: '306',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3529005',
      cep: '17.501-080',
      cnpj: '40756759000113',
      ie: null,
      telefone: '(14) 3221-3025',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Marília',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4fa'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '12',
      ativo: true,
      razaoSocial: 'APARECIDA ESCOTON',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA DOUTOR CAMPOS SALES',
      numero: '554',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3512803',
      cep: '13.150-027',
      cnpj: '33565456000158',
      ie: null,
      telefone: '(19) 9228-5529 / (19) 3608-6179 / (19) 3608-6179',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Cosmópolis',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4fb'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '74',
      ativo: true,
      razaoSocial: 'MIRIAN SORIANO MORENO NORDON COMERCIO DE ROUPAS LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R DOS MANACAS',
      numero: '228',
      bairro: 'JARDIM DA GLORIA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3513009',
      cep: '06.711-500',
      cnpj: '39611008000120',
      ie: null,
      telefone: '(11) 3673-2255/ (11) 3673-2647',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Cotia',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf513'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '80',
      ativo: true,
      razaoSocial: 'IFMF CASTELLO CONFECCOES - EIRELI',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA BENTO MUNHOZ DA ROCHA NETTO',
      numero: '159',
      bairro: 'ZONA 07',
      codUf: 41,
      uf: 'PR',
      codCidade: '4115200',
      cep: '87.030-010',
      cnpj: '39757110000138',
      ie: null,
      telefone: '(44) 3223-0311',
      celular: null,
      email: '',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCe',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Maringá',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf4f7'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '3',
      ativo: true,
      razaoSocial: 'KELLY RIBEIRO PAZIANI',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA CAPITAO HORTA',
      numero: '442',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3510807',
      cep: '13.700-000',
      cnpj: '30987024000147',
      ie: null,
      telefone: '(19) 9597-7616',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 2300,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: true,
      __v: 0,
      cidade: 'Casa Branca',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf514'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '67',
      ativo: true,
      razaoSocial: 'RONALDE BAPTISTA DE LIMA JUNIOR',
      nomeReduzido: 'CRESCI E PERDI ',
      endereco: 'AVENIDA PRESIDENTE CASTELO BRANCO',
      numero: '504',
      bairro: 'JARDIM ZAIRA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3529401',
      cep: '09.320-590',
      cnpj: '38478312000188',
      ie: null,
      telefone: '(19) 3813-2132',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Mauá',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf511'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '57',
      ativo: true,
      razaoSocial: 'SUELLEN CRESCI E PERDI MACHADO LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R CORONEL JACINTO',
      numero: '217',
      bairro: 'CENTRO',
      codUf: 31,
      uf: 'MG',
      codCidade: '3139003',
      cep: '37.750-000',
      cnpj: '37879499000169',
      ie: null,
      telefone: '(35) 3295-4993',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Machado',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf515'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '20',
      ativo: true,
      razaoSocial: 'MATHEUS ALVES DE MELLO',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R QUINZE DE NOVEMBRO',
      numero: '192',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3530508',
      cep: '13.730-020',
      cnpj: '26224421000190',
      ie: null,
      telefone: '(19) 8918-6890/ (19) 3656-6211',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Mococa',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf517'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '7',
      ativo: true,
      razaoSocial: 'MATHEUS ALVES DE MELLO',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA SANTA JULIA',
      numero: '206',
      bairro: 'VILA JULIA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3530706',
      cep: '13.844-001',
      cnpj: '26224421000270',
      ie: null,
      telefone: '(19) 3818-8815',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 3250,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: true,
      __v: 0,
      cidade: 'Mogi Guaçu',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf516'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '70',
      ativo: true,
      razaoSocial: 'JRT ARTIGOS INFANTIS LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R SAO JOAO',
      numero: '619',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3530607',
      cep: '08.715-030',
      cnpj: '38826940000107',
      ie: null,
      telefone: '(11) 4726-4092',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Mogi das Cruzes',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf518'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '51',
      ativo: true,
      razaoSocial: 'LIMA JUNIOR COMERCIO DE ARTIGOS DO VESTUARIO LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA PADRE ROQUE',
      numero: '1869',
      bairro: 'JARDIM AUREA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3530805',
      cep: '13.800-207',
      cnpj: '37096471000155',
      ie: null,
      telefone: '(19) 3813-2132',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Mogi Mirim',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf519'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '55',
      ativo: true,
      razaoSocial: 'HMB COMERCIO DE ROUPAS E ARTIGOS INFANTIS LTDA',
      nomeReduzido: 'CRESCI E PERDI ',
      endereco: 'R BELO HORIZONTE',
      numero: '323',
      bairro: 'CENTRO',
      codUf: 31,
      uf: 'MG',
      codCidade: '3143302',
      cep: '39.400-054',
      cnpj: '37685400000198',
      ie: null,
      telefone: '(38) 9945-8415',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Montes Claros',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf51a'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '48',
      ativo: true,
      razaoSocial: 'MM REINALDI BAZAR E OUTLET EIRELI',
      nomeReduzido: 'CRESCI E PERDI ',
      endereco: 'AVENIDA DOUTOR EDDY DE FREITAS CRISSIUMA',
      numero: '814',
      bairro: 'JARDIM DONA MARIA AZENHA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3533403',
      cep: '13.380-492',
      cnpj: '36367588000163',
      ie: null,
      telefone: '(19) 3213-7907',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Nova Odessa',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf51e'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '24',
      ativo: true,
      razaoSocial: 'MAIARA HELENA MASSAROTTI',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AV DOIS CORREGOS',
      numero: '337',
      bairro: 'PIRACICAMIRIM',
      codUf: 35,
      uf: 'SP',
      codCidade: '3538709',
      cep: '13.420-610',
      cnpj: '34377454000106',
      ie: null,
      telefone: '(19) 3813-2132',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Piracicaba',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf51f'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '19',
      ativo: true,
      razaoSocial: 'LEANDRO ALVES DE MELLO 21560389885',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AV NEWTON PRADO',
      numero: '2.938',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3539301',
      cep: '13.631-045',
      cnpj: '29070324000269',
      ie: null,
      telefone: '(19) 3608-6179',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Pirassununga',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf51d'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '8',
      ativo: true,
      razaoSocial: 'FUJIWARA BAZAR E OUTLET EIRELI',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AV DOS EXPEDICIONARIOS',
      numero: '463',
      bairro: 'JARDIM DOS CALEGARIS',
      codUf: 35,
      uf: 'SP',
      codCidade: '3536505',
      cep: '13.140-111',
      cnpj: '33257720000196',
      ie: null,
      telefone: '(19) 3213-7907',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 3125.8,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: true,
      __v: 0,
      cidade: 'Paulínia',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf520'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '15',
      ativo: true,
      razaoSocial: 'ALESSANDRA APARECIDA BRAMBILLA DA COSTA BASSO 33506192825',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA JOAO PINHEIRO',
      numero: '117',
      bairro: 'CAMPO DA MOGIANA',
      codUf: 31,
      uf: 'MG',
      codCidade: '3151800',
      cep: '37.701-880',
      cnpj: '32164701000152',
      ie: null,
      telefone: '(35) 9888-3733',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Poços de Caldas',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf521'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '47',
      ativo: true,
      razaoSocial: 'LUIS OTAVIO DE AQUINO MORENO',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA RUDOLF STREIT',
      numero: '919',
      bairro: 'VILA SIBYLLA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3540705',
      cep: '13.664-050',
      cnpj: '36632350000118',
      ie: null,
      telefone: '(19) 9337-2911',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Porto Ferreira',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf522'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '30',
      ativo: true,
      razaoSocial: 'FLAVIA CAROLINA BASSO',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA JOAO BASILIO',
      numero: '58',
      bairro: 'CENTRO',
      codUf: 31,
      uf: 'MG',
      codCidade: '3152501',
      cep: '37.550-121',
      cnpj: '34801851000155',
      ie: null,
      telefone: '(35) 3551-5043 / (35) 8802-6272',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Pouso Alegre',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf51b'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '79',
      ativo: true,
      razaoSocial: 'BABALU COMERCIO DE ARTIGOS INFANTIS LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AV DOS AUTONOMISTAS',
      numero: '2628',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3534401',
      cep: '06.090-010',
      cnpj: '38831158000186',
      ie: null,
      telefone: '(11) 2910-3137',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Osasco',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf51c'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '14',
      ativo: true,
      razaoSocial: 'IRONETE APARECIDA DO COUTO NEVES',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA DEPUTADO LOURENCO DE ANDRADE',
      numero: '588',
      bairro: 'CENTRO',
      codUf: 31,
      uf: 'MG',
      codCidade: '3147907',
      cep: '37.900-093',
      cnpj: '32014513000148',
      ie: null,
      telefone: '(35) 9975-8715',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Passos',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf523'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '85',
      ativo: true,
      razaoSocial: 'AM BAZAR E OUTLET INFANTIL LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AV PRESIDENTE COSTA E SILVA',
      numero: '1122',
      bairro: 'BOQUEIRAO',
      codUf: 31,
      uf: 'MG',
      codCidade: '4213807',
      cep: '11.700-007',
      cnpj: '39959295000163',
      ie: null,
      telefone: '(13) 9999-9999',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Praia Grande',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf524'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '16',
      ativo: true,
      razaoSocial: 'AMANDA ALVES DE MELLO',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA ANTONIO DIEDERICHSEN',
      numero: '305',
      bairro: 'JARDIM AMERICA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3543402',
      cep: '14.020-240',
      cnpj: '24760219000238',
      ie: null,
      telefone: '(19) 9770-3935 / (19) 3608-6179 / (19) 3608-6179',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Ribeirão Preto',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf525'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '60',
      ativo: true,
      razaoSocial: 'AMANDA ALVES DE MELLO',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA GOIAS',
      numero: '1.688',
      bairro: 'CAMPOS ELISEOS',
      codUf: 35,
      uf: 'SP',
      codCidade: '3543402',
      cep: '14.085-460',
      cnpj: '24760219000408',
      ie: null,
      telefone: '(19) 9323-1358 / (19) 3608-6179 / (19) 3608-6179',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Ribeirão Preto',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf526'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '31',
      ativo: true,
      razaoSocial: 'ANA CAROLINA AULICINIO VALENTE',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R 4',
      numero: '1506',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3543907',
      cep: '13.500-171',
      cnpj: '35015480000149',
      ie: null,
      telefone: '(19) 7169-7181/ (19) 3608-6179',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Rio Claro',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf527'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '86',
      ativo: true,
      razaoSocial: 'NATALIA FERNANDES PEREIRA POMPONI LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AV PRESIDENTE VARGAS',
      numero: 'S/N',
      bairro: 'JARDIM MARCONAL',
      codUf: 52,
      uf: 'GO',
      codCidade: '5218805',
      cep: '75.901-551',
      cnpj: '39854552000100',
      ie: null,
      telefone: '(64) 3641-5609',
      celular: null,
      email: '',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Rio Verde',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf528'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '65',
      ativo: true,
      razaoSocial: 'MOISES E NARDINI LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA DOM PEDRO II',
      numero: '1458',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3545209',
      cep: '13.320-240',
      cnpj: '38420942000100',
      ie: null,
      telefone: '(19) 3813-2132',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Salto',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf529'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '38',
      ativo: true,
      razaoSocial: 'SALVADOR E FAVERO COMERCIO DE ROUPAS LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA RIACHUELO',
      numero: '387',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3545803',
      cep: '13.450-019',
      cnpj: '34814826000106',
      ie: null,
      telefone: '(19) 3454-1611',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: "Santa Bárbara d'Oeste",
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf52a'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '45',
      ativo: true,
      razaoSocial: 'FLAVIA REGINA METTI DE ABREU EIRELI',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA CATEQUESE',
      numero: '795',
      bairro: 'VILA GUIOMAR',
      codUf: 35,
      uf: 'SP',
      codCidade: '2513851',
      cep: '09.090-401',
      cnpj: '36214173000150',
      ie: null,
      telefone: '(11) 8802-5199',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Santo André',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf52b'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '75',
      ativo: true,
      razaoSocial: 'ROSSANA HELAL BAZAR E OUTLET EIRELI',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R RIO GRANDE DO NORTE',
      numero: '71',
      bairro: 'POMPEIA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3548500',
      cep: '11.065-460',
      cnpj: '38165986000122',
      ie: null,
      telefone: '(19) 3213-7907',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Santos ',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf52c'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '44',
      ativo: true,
      razaoSocial: 'MOMO BAZAR E OUTLET EIRELI',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA AMPARO',
      numero: '398',
      bairro: 'BAETA NEVES',
      codUf: 35,
      uf: 'SP',
      codCidade: '3548708',
      cep: '09.751-350',
      cnpj: '35851331000110',
      ie: null,
      telefone: '(19) 3213-7997',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'São Bernardo do Campo',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf52e'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '40',
      ativo: true,
      razaoSocial: 'SAO CARLOS',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA 1',
      numero: '1',
      bairro: 'CENTRO ',
      codUf: 35,
      uf: 'SP',
      codCidade: '3548906',
      cep: '13560-001',
      cnpj: '36356370000103',
      ie: '637.511.858.110',
      telefone: null,
      celular: null,
      email: null,
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'São Carlos',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf52f'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '5',
      ativo: true,
      razaoSocial: 'EVELISE DE OLIVEIRA FELTRAN BAPTISTELLA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA GETULIO VARGAS',
      numero: '539',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3549102',
      cep: '13.870-100',
      cnpj: '39329274000164',
      ie: null,
      telefone: '(19) 9734-4294 / (19) 3608-6179 / (19) 3608-6179',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 2900.6,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: true,
      __v: 0,
      cidade: 'São João da Boa Vista',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf530'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '2',
      ativo: true,
      razaoSocial: 'LUCAS FELTRAN BAPTISTELLA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R CAMPOS SALLES',
      numero: '820',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3549706',
      cep: '13.720-000',
      cnpj: '32906788000196',
      ie: null,
      telefone: '(19) 3608-6179',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 1000,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'São José do Rio Pardo',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf531'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '33',
      ativo: true,
      razaoSocial: 'CARLOS HENRIQUE DA SILVEIRA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA ANGELO CALAFIORI',
      numero: '144',
      bairro: 'CENTRO',
      codUf: 31,
      uf: 'MG',
      codCidade: '3164704',
      cep: '37.950-000',
      cnpj: '34984827000107',
      ie: null,
      telefone: '(35) 8802-6272',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'São Sebastião do Paraíso',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf532'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '52',
      ativo: true,
      razaoSocial: 'LOPES E CHAVES ARTIGOS INFANTIS LTDA',
      nomeReduzido: 'CRESCI PERDI',
      endereco: 'R FIORAVANTE SICCHIERI',
      numero: '90',
      bairro: 'JARDIM SAO JOSE',
      codUf: 35,
      uf: 'SP',
      codCidade: '2515930',
      cep: '14.170-690',
      cnpj: '36410964000155',
      ie: null,
      telefone: '(16) 3600-9896',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Sertãozinho',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf52d'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '46',
      ativo: true,
      razaoSocial: 'GISELI LEONE BARROS EIRELI',
      nomeReduzido: 'CRESCI E PERDI ',
      endereco: 'R ORIENTE',
      numero: '761',
      bairro: 'BARCELONA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3548807',
      cep: '09.551-010',
      cnpj: '36638439000191',
      ie: null,
      telefone: '(11) 2658-6294',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'São Caetano do Sul',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf533'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '39',
      ativo: true,
      razaoSocial: 'CARLOS ALEXANDRE BATISTA',
      nomeReduzido: null,
      endereco: 'AVENIDA DOUTOR AFONSO VERGUEIRO',
      numero: '2980',
      bairro: 'VILA AUGUSTA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3552205',
      cep: '18.040-000',
      cnpj: '36315007000140',
      ie: null,
      telefone: '(19) 3813-2132',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Sorocaba',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf534'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '32',
      ativo: true,
      razaoSocial: 'MEULOT BAZAR E OUTLET EIRELI',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA LUIS FRUTUOSO',
      numero: '534',
      bairro: 'VILA SANTANA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3552403',
      cep: '13.170-260',
      cnpj: '34577876000117',
      ie: null,
      telefone: '(19) 3213-7907',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Sumaré',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf536'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '64',
      ativo: true,
      razaoSocial: 'TALYTA CRISTINA DA SILVA GRANDCHAMP',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AV JUSCELINO KUBITSCHEK DE OLIVEIRA',
      numero: '75',
      bairro: 'JARDIM EULALIA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3554102',
      cep: '12.010-600',
      cnpj: '37898493000139',
      ie: null,
      telefone: '(12) 8814-3776',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Taubaté',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf535'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '69',
      ativo: true,
      razaoSocial: 'GENSKE COMERCIO VAREJISTA DE ARTIGOS USADOS LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AV DOUTOR JOSE MACIEL',
      numero: '773',
      bairro: 'JARDIM MARIA ROSA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3552809',
      cep: '06.764-005',
      cnpj: '38713650000157',
      ie: null,
      telefone: '(11) 5870-9397',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Taboão da Serra',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf537'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '36',
      ativo: true,
      razaoSocial: 'FABIANO GOMES ANDRIOLETTE 05695370612',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA EDILSON LAMARTINE MENDES',
      numero: '1013',
      bairro: 'PARQUE DAS AMERICAS',
      codUf: 31,
      uf: 'MG',
      codCidade: '3170107',
      cep: '38.045-000',
      cnpj: '34416667000191',
      ie: null,
      telefone: '(34) 9254-6080',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Uberaba',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf538'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '9',
      ativo: true,
      razaoSocial: 'DAMIANA DAS DORES DE OLIVEIRA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA GETULIO VARGAS',
      numero: '1543',
      bairro: 'TABAJARAS',
      codUf: 31,
      uf: 'MG',
      codCidade: '3170206',
      cep: '38.400-283',
      cnpj: '31970251000122',
      ie: null,
      telefone: '(34) 9214-6080',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 2845,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: true,
      __v: 0,
      cidade: 'Uberlândia',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf539'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '58',
      ativo: true,
      razaoSocial: 'JULIANO GOMES ANDRIOLETTE',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA RONDON PACHECO',
      numero: '3600',
      bairro: 'SARAIVA',
      codUf: 31,
      uf: 'MG',
      codCidade: '3170206',
      cep: '38.408-404',
      cnpj: '37829901000109',
      ie: null,
      telefone: '(34) 3306-5993',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'NFCE',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Uberlândia',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf53a'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '21',
      ativo: true,
      razaoSocial: 'SAMIRA NOGUEIRA AYEK DE MENEZES',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA INDEPENDENCIA',
      numero: '1072',
      bairro: 'VILA OLIVO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3556206',
      cep: '13.276-030',
      cnpj: '33825312000193',
      ie: null,
      telefone: '(19) 9729-0587',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Valinhos',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf53b'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '6',
      ativo: true,
      razaoSocial: 'VERONESI COMERCIO DE ROUPAS E ACESSORIOS EIRELI',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'RUA SANTANA',
      numero: '511',
      bairro: 'CENTRO',
      codUf: 35,
      uf: 'SP',
      codCidade: '3556404',
      cep: '13.880-000',
      cnpj: '37894229000127',
      ie: null,
      telefone: '(35) 3712-3185',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 3500,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: true,
      __v: 0,
      cidade: 'Vargem Grande do Sul',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf53d'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '34',
      ativo: true,
      razaoSocial: 'BFH COMERCIO VAREJISTA DE ARTIGOS USADOS LTDA.',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AVENIDA CONCEICAO',
      numero: '281',
      bairro: 'CARANDIRU',
      codUf: 35,
      uf: 'SP',
      codCidade: '3550308',
      cep: '02.072-000',
      cnpj: '35086566000162',
      ie: null,
      telefone: '(11) 7665-9669',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'São Paulo',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf53e'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '78',
      ativo: true,
      razaoSocial: 'J.R. ALMEIDA COMERCIO DE ROUPAS LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'AV ROGERIO CASSOLA',
      numero: '992',
      bairro: 'ITAPEVA',
      codUf: 35,
      uf: 'SP',
      codCidade: '3557006',
      cep: '18.116-709',
      cnpj: '38611401000150',
      ie: null,
      telefone: '(11) 4526-1266',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'Votorantim',
    },
    {
      _id: ObjectId('603d3a1a7b820c5638adf53c'),
      dataCriacao: Date('2021-03-01T16:01:12.178Z'),
      dataAlteracao: Date('2021-03-01T16:01:12.178Z'),
      codUnd: '82',
      ativo: true,
      razaoSocial: 'LRG COMERCIO VAREJISTA DE ARTIGOS USADOS LTDA',
      nomeReduzido: 'CRESCI E PERDI',
      endereco: 'R CATUTI',
      numero: '40',
      bairro: 'VILA ANDRADE',
      codUf: 35,
      uf: 'SP',
      codCidade: '3550308',
      cep: '05.729-120',
      cnpj: '39760124000100',
      ie: null,
      telefone: '(11) 5870-9397',
      celular: null,
      email: '<EMAIL>',
      dtInauguracao: null,
      vlrRoyalties: 0,
      tipoNota: 'SAT',
      visualizar: true,
      emitirBoleto: false,
      __v: 0,
      cidade: 'São Paulo',
    },
  ]

  array.forEach(item => {
    new Empresa(item)
      .save()
      .then(obj => res.json(obj))
      .catch(erro => res.status(400).json(erro))
  })
})

module.exports = router
