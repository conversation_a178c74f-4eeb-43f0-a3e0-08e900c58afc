require('dotenv/config')
const { getApp } = require('./app')

const { PORT } = process.env

const startApp = async () => {
  try {
    const port = PORT || 3001
    const app = await getApp()
    app.listen(port, () => console.log(`Listening on port ${port}`))
    app.keepAliveTimeout = 125000
  } catch (error) {
    console.error('Erro ao iniciar a aplicação:', error)
    process.exit(1)
  }
}

startApp()
