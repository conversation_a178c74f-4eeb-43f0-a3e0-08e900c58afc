const utils = require('../../utils/index')

const PreEvaluationModel = require('../../models/Pre_Avaliacao')
const UserModel = require('../../models/Usuario')
const { generatePasswordOfPreEvaluation } = require('../../utils/generate-pass-pre-evaluation')
const { Types } = require('mongoose')
const moment = require('moment')

class PreEvalution {
  constructor() {}

  async registerPreEvaluation(req, res) {
    try {
      const { usuario, cnpj, idCliente, volume } = req.body || {}

      if (!cnpj) {
        return res.status(400).json({
          status: false,
          errorMenssage: 'Informe o cnpj corretamente.',
        })
      }

      if (!idCliente) {
        return res.status(400).json({
          status: false,
          error: 'Não foi possível atualizar o cliente!',
        })
      }

      const user = await UserModel.findOne({
        usuario: usuario,
      })

      if (!user) {
        return res.status(400).json({
          status: false,
          error: 'Cliente não encontrado',
        })
      }

      let newPre = {
        usuario,
        cnpj,
        data: utils.getCurrentTime(cnpj),
        idCliente,
        volume,
      }

      newPre = {
        ...newPre,
        pre_avaliacao_senha: await generatePasswordOfPreEvaluation(cnpj),
      }

      const preEvaluation = await PreEvaluationModel.create(newPre)

      res.status(201).json({
        status: true,
        preEvaluation: preEvaluation,
      })
    } catch (error) {
      console.log(error)
      res.status(400).json({
        status: false,
        error,
      })
    }
  }

  async getPreEvaluations(req, res) {
    const { cnpj } = req.body || {}

    if (!cnpj) {
      return res.status(400).json({
        status: false,
        error: 'Informe o cnpj corretamente.',
      })
    }

    let pre_avaliacao = []
    try {
      pre_avaliacao = await PreEvaluationModel.aggregate([
        { $match: { cnpj, finalizado: false } },
        {
          $addFields: {
            userObjectId: {
              $convert: {
                input: '$idCliente',
                to: 'objectId',
                onError: '',
                onNull: '',
              },
            },
          },
        },
        {
          $lookup: {
            localField: 'userObjectId',
            from: 'clientes',
            foreignField: '_id',
            as: 'cliente',
          },
        },
      ])
    } catch (e) {}
    return res.json(pre_avaliacao)
  }

  async getPreEvaluation(req, res) {
    let pre_avaliacao = []

    // Rota provisoriamente suspensa

    // try {
    //   pre_avaliacao = await Pre_Avaliacao.aggregate([
    //     {
    //       $match: { finalizado: false },
    //     },
    //     {
    //       $addFields: {
    //         userObjectId: {
    //           $convert: {
    //             input: '$idCliente',
    //             to: 'objectId',
    //             onError: '',
    //             onNull: '',
    //           },
    //         },
    //       },
    //     },
    //     {
    //       $lookup: {
    //         localField: 'userObjectId',
    //         from: 'clientes',
    //         foreignField: '_id',
    //         as: 'cliente',
    //       },
    //     },
    //   ])
    // } catch (e) {}

    return res.json(pre_avaliacao)
  }

  async deletePreEvaluation(req, res) {
    const { id } = req.params
    const { reason } = req.body

    const verifyIfExist = await PreEvaluationModel?.findOne({ _id: new Types.ObjectId(id) }).lean()

    if (!verifyIfExist) {
      return res.status(404).json({
        status: false,
        error: 'Pré-avaliação não encontrada.',
      })
    }

    console.log(reason)

    const deletedPreEvaluation = await PreEvaluationModel?.findOneAndUpdate(
      { _id: new Types.ObjectId(id) },
      {
        deletedAt: moment.utc().subtract({ hours: 3 }).toDate(),
        deleteReason: reason,
      }
    )

    return res.status(200).json(deletedPreEvaluation)
  }
}

module.exports = PreEvalution
