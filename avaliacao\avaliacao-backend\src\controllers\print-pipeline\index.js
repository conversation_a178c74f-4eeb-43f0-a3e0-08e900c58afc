const PrinterPipelineModel = require('../../models/Printer-pipeline')
const Avaliacao = require('../../models/Avaliacao')
const VoucherModel = require('../../models/Vale');
const { Types } = require('mongoose')

class PrintPipelineClass {
  constructor() {}

  async createPrintPipeline(req, res) {
    try {
      const {
        ownerPrinterCnpj,
        typeId,
        dataId = '',
        data = {},
        copyValues = 1,
        boldValue,
        boldDescriptionValue,
      } = req.body

      if (dataId) {
        const findEvaluation = await Avaliacao.findOne({
          _id: new Types.ObjectId(dataId),
        }, {
          codBarras: 1,
          idPreAvaliacao: 1,
          idUsuarioAvaliacao: 1,
          tipo: 1,
        }).lean()

        if (findEvaluation) {
          if (findEvaluation?.tipo === 'Vale') {
            const giracreditVoucher = await VoucherModel.findOne({
              codBarras: findEvaluation?.codBarras
            }, {
              _id: 1,
              dataExpiracao: 1,
            }).lean()

            data['evaluation'] = {
              ...findEvaluation,
              giracredit: {
                ...giracreditVoucher
              }
            }
          } else {
            data['evaluation'] = findEvaluation
          }
        }
      }

      const _printPipeline = await PrinterPipelineModel.create({
        ownerPrinterCnpj,
        typeId,
        dataId,
        data,
        copyValues,
        boldValue,
        boldDescriptionValue,
      })

      if (!_printPipeline)
        return res.status(400).json({
          status: false,
          data: 'Not created print pipeline',
        })

      return res.status(200).json({
        status: true,
        data: 'Printed pipeline created.',
      })
    } catch (error) {
      console.error(error)
      res.status(400).json({
        status: error,
        error: 'Bad request, try letter',
      })
    }
  }
}

module.exports = PrintPipelineClass
