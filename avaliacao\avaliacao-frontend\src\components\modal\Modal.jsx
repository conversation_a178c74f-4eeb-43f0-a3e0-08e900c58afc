import React from 'react'
import Rodal from 'rodal'
import 'rodal/lib/rodal.css'
import './Modal.css'
import Input_valor from '../input_valor/Input_valor'

const Modal = props => {
  return (
    <Rodal visible={props.visible} onClose={props.closeModal} width={500} height={620}>
      <>
        <div className="header">Edição</div>
        <div className="body">
          <Input_valor
            value="Simples"
            handleChange={props.handleChange}
            arg={1}
            custoModal={props.custoModal1}
            minModal={props.minModal1}
            maxModal={props.maxModal1}
            pesoModal={props.pesoModal1}
          />
          <Input_valor
            value="Simples Novo"
            handleChange={props.handleChange}
            arg={2}
            custoModal={props.custoModal2}
            minModal={props.minModal2}
            maxModal={props.maxModal2}
            pesoModal={props.pesoModal2}
          />
          <Input_valor
            value="Diferenciado"
            handleChange={props.handleChange}
            arg={3}
            custoModal={props.custoModal3}
            minModal={props.minModal3}
            maxModal={props.maxModal3}
            pesoModal={props.pesoModal3}
          />
          <Input_valor
            value="Grife"
            handleChange={props.handleChange}
            arg={4}
            custoModal={props.custoModal4}
            minModal={props.minModal4}
            maxModal={props.maxModal4}
            pesoModal={props.pesoModal4}
          />
        </div>
        <div className="footer">
          <button id="btn-modal-cd-nvs" onClick={props.adicionarDoModal}>
            Adicionar
          </button>
        </div>
      </>
    </Rodal>
  )
}

export default Modal
