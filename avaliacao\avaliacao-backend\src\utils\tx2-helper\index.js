const { URLSearchParams } = require('url');
const axios = require('axios');

const createHeader = (buffer) => {
  buffer += "formato=tx2\nnumlote=0";
  return buffer;
};

const createDadosNota = (buffer, dadosNota) => {
  buffer += "\nINCLUIR";
  const keys = Object.keys(dadosNota);
  keys.forEach((key) => {
    buffer += `\n${key}=${dadosNota[key]}`;
  });
  return buffer;
};

const createDadosEmitente = (buffer, dadosEmitente) => {
  return new Promise<string>((resolve) => {
    const keys = Object.keys(dadosEmitente);
    keys.forEach((key) => {
      buffer += `\n${key}=${dadosEmitente[key]}`;
    });
    resolve(buffer);
  });
};

const createDadosDestinatario = (buffer, dadosDestinatario) => {
  return new Promise<string>((resolve) => {
    const keys = Object.keys(dadosDestinatario);
    keys.forEach((key) => {
      buffer += `\n${key}=${dadosDestinatario[key]}`;
    });
    resolve(buffer);
  });
};

const createDadosItens = (buffer, itens) => {
  return new Promise<string>((resolve) => {
    itens.forEach((item) => {
      buffer += "\nINCLUIRITEM";
      const keys = Object.keys(item);
      keys.forEach((key) => {
        buffer += `\n${key}=${item[key]}`;
      });
      buffer += "\nSALVARITEM";
    });
    resolve(buffer);
  });
};

const createPagamentos = (
  buffer,
  pagamentos,
  type
) => {
  return new Promise<string>((resolve) => {
    pagamentos.forEach((pagamento) => {
      buffer += `\nINCLUIRPARTE=${type}`;
      const keys = Object.keys(pagamento);
      keys.forEach((key) => {
        buffer += `\n${key}=${pagamento[key]}`;
      });
      buffer += `\nSALVARPARTE=${type}`;
    });
    resolve(buffer);
  });
};

const createTotalizadores = (buffer, totalizadores) => {
  return new Promise<string>((resolve) => {
    const keys = Object.keys(totalizadores);
    keys.forEach((key) => {
      buffer += `\n${key}=${totalizadores[key]}`;
    });
    resolve(buffer);
  });
};

const createAuthGetXml = (buffer, cnpj = "", cpf = "") => {
  return new Promise<string>((resolve) => {
    if (cnpj || cpf) {
      buffer += `\nINCLUIRPARTE=AUTXML\n${cnpj && `CNPJ_GA02=${cnpj}\n`}${
        cpf && `CPF_GA03=${cpf}\n`
      }SALVARPARTE=AUTXML`;
    }
    resolve(buffer);
  });
};

const createTecnico = (buffer, tecnico) => {
  return new Promise<string>((resolve) => {
    const keys = Object.keys(tecnico);
    keys.forEach((key) => {
      buffer += `\n${key}=${tecnico[key]}`;
    });
    resolve(buffer);
  });
};

const createObservacoes = (buffer, obs) => {
  return new Promise<string>((resolve) => {
    const keys = Object.keys(obs);
    keys.forEach((key) => {
      buffer += `\n${key}=${obs[key]}`;
    });
    resolve(buffer);
  });
};

const closeFile = (buffer) => {
  return new Promise<string>((resolve) => {
    buffer += "\n\nSALVAR";
    resolve(buffer);
  });
};

/**
 * Envia o arquivo tx2 para a api da tecnospeed e retorna a resposta.
 * @param tx2Path o caminho para o arquivo tx2
 * @param cnpj o cnpj da empresa emitente
 * @param grupo o nome do grupo
 * @param authorization a string de autorização para acessar a api da tecnospeed.
 */
const sendNFCe = (
  tx2Content,
  cnpj,
  grupo,
  authorization,
  port,
  amb
) => {
  return new Promise((resolve, reject) => {
    const form = {
      CNPJ: cnpj,
      Grupo: grupo,
      Arquivo: tx2Content, // Agora utilizamos o conteúdo fornecido diretamente
    };
    const formData = new URLSearchParams(form).toString();
    const contentLength = formData.length;

    axios
      .post(
        `https://managersaas${amb}.tecnospeed.com.br:${port}/ManagerAPIWeb/nfce/envia`,
        formData,
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            "Content-Length": contentLength,
            Authorization: authorization,
          },
        }
      )
      .then((response) => {
        resolve(response.data);
      })
      .catch((error) => {
        reject(error);
      });
  });
};

/**
 * Envia o arquivo tx2 para a api da tecnospeed e retorna a resposta.
 * @param tx2Path o caminho para o arquivo tx2
 * @param cnpj o cnpj da empresa emitente
 * @param grupo o nome do grupo
 * @param authorization a string de autorização para acessar a api da tecnospeed.
 */
const sendNFe = (
  tx2Content,
  cnpj,
  grupo,
  authorization,
  port,
  amb
) => {
  return new Promise((resolve, reject) => {
    const form = {
      CNPJ: cnpj,
      Grupo: grupo,
      Arquivo: tx2Content, // Agora utilizamos o conteúdo fornecido diretamente
    };
    const formData = new URLSearchParams(form).toString();
    const contentLength = formData.length;

    axios
      .post(
        `https://managersaas${amb}.tecnospeed.com.br:${port}/ManagerAPIWeb/nfe/envia`,
        formData,
        {
          headers: {
            "Content-Type": "application/x-www-form-urlencoded",
            "Content-Length": contentLength,
            Authorization: authorization,
          },
        }
      )
      .then((response) => {
        resolve(response.data);
      })
      .catch((error) => {
        reject(error);
      });
  });
};

/**
 * Gera o conteúdo para impressão da nota fiscal.
 * @param authorization a string de autorização para acessar a api da tecnospeed
 * @param key a chave da nota
 * @param url 0 = conteúdo binário de pdf, 1 = url para download do pdf.
 * @param group nome do grupo
 * @param cnpj cnpj da empresa emitente.
 */
const print = async (
  authorization,
  key,
  url,
  group,
  cnpj,
  port,
  amb
) => {
  return new Promise((resolve, reject) => {
    const form = {
      ChaveNota: key,
      CNPJ: cnpj,
      Grupo: group,
      Url: url,
    };
    axios
      .get(
        `https://managersaas${amb}.tecnospeed.com.br:${port}/ManagerAPIWeb/nfce/imprime`,
        {
          headers: {
            Authorization: authorization,
          },
          params: form,
        }
      )
      .then((response) => {
        resolve(response.data);
      })
      .catch((error) => {
        reject(error);
      });
  });
};

/**
 * Generates a random string to complement the cNF_B03 value.
 */
const generatecNF_B03 = () => {
  let result = "";
  const characters = "0123456789";
  const charactersLength = characters.length;
  for (let i = 0; i < 8; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }

  return result;
};

/**
 * Gera o arquivo tx2 (para NFCe) no caminho especificado.
 * @param caminhoTx2 o caminho onde o tx2 será gerado (um arquivo com o mesmo nome não pode existir)
 * @param dadosNota um objeto contendo os dados iniciais da nota.
 * @param dadosEmitente um objeto contento os dados do emitente.
 * @param itens um array contendo objetos com os dados dos itens.
 * @param pagamentos um array contendo as informações das formas de pagamento utilizadas.
 * @param totalizadores um objeto contendo os dados dos totalizadores.
 * @param tecnico um objeto contendo as informações do responsável técnico.
 * @param cnpjAutorizado Autorização para obter XML
 * @param observacoes Observações
 * @return retorna uma string do caminho onde o arquivo foi gerado
 */
const generateNFCeTx2 = async (
  DadosNota,
  dadosEmitente,
  itens,
  pagamentos,
  totalizadores,
  tecnico,
  cnpjAutorizado,
  cpfAutorizado,
  observacoes
) => {
  let buffer = ""; // Inicializa um buffer (string) vazio

  try {
    buffer = await createHeader(buffer);
    buffer = await createDadosNota(buffer, dadosNota);
    buffer = await createDadosEmitente(buffer, dadosEmitente);
    buffer = await createDadosItens(buffer, itens);
    buffer = await createAuthGetXml(buffer, cnpjAutorizado, cpfAutorizado);
    buffer = await createPagamentos(buffer, pagamentos, "YA");
    buffer = await createTotalizadores(buffer, totalizadores);
    buffer = await createTecnico(buffer, tecnico);
    buffer = await createObservacoes(buffer, observacoes);
    buffer = await closeFile(buffer);

    return buffer; // Retorna o conteúdo como string ou buffer
  } catch (error) {
    throw new Error(`Erro ao gerar o arquivo TX2: ${error}`);
  }
};

/**
 * Cancela uma NFCe.
 * @param authorization the header authrization string (base 64).
 * @param group the group name
 * @param cnpj the company cnpj
 * @param nfceKey the nfce key
 * @param justify a string justifying the cancel reason
 * @returns
 */
const cancelNFCe = (
  authorization,
  group,
  cnpj,
  nfceKey,
  justify,
  port,
  amb
) => {
  return new Promise((resolve, reject) => {
    try {
      const form = {
        ChaveNota: nfceKey,
        CNPJ: cnpj,
        Grupo: group,
        Justificativa: justify,
      };

      axios
        .post(
          `https://managersaas${amb}.tecnospeed.com.br:${port}/ManagerAPIWeb/nfce/cancela`,
          null,
          {
            headers: {
              Authorization: authorization,
            },
            params: form,
          }
        )
        .then((response) => {
          resolve(response.data);
        })
        .catch((error) => {
          reject(error);
        });
    } catch (e) {
      reject(e);
    }
  });
};

/**
 * Gera o arquivo tx2 (para NFe) no caminho especificado.
 * @param caminhoTx2 o caminho onde o tx2 será gerado (um arquivo com o mesmo nome não pode existir)
 * @param dadosNota um objeto contendo os dados iniciais da nota.
 * @param dadosEmitente um objeto contento os dados do emitente.
 * @param dadosDestinatario um objeto contendo os dados do destinatário.
 * @param itens um array contendo objetos com os dados dos itens.
 * @param pagamentos um array contendo as informações das formas de pagamento utilizadas.
 * @param totalizadores um objeto contendo os dados dos totalizadores.
 * @param tecnico um objeto contendo as informações do responsável técnico.
 * @param cnpjAutorizado Autorização para obter XML
 * @param cpfAutorizado Autorização para obter XML
 * @param observacoes Observações
 * @return retorna uma string do caminho onde o arquivo foi gerado
 */
const generateNFeTx2 = async (
  dadosNota,
  dadosEmitente,
  dadosDestinatario,
  itens,
  pagamentos,
  totalizadores,
  tecnico,
  cnpjAutorizado,
  cpfAutorizado,
  observacoes
) => {
  let buffer = ""; // Inicializa um buffer (string) vazio

  try {
    buffer = await createHeader(buffer);
    buffer = await createDadosNota(buffer, dadosNota);
    buffer = await createDadosEmitente(buffer, dadosEmitente);
    buffer = await createDadosDestinatario(buffer, dadosDestinatario);
    buffer = await createAuthGetXml(buffer, cnpjAutorizado, cpfAutorizado);
    buffer = await createDadosItens(buffer, itens);
    buffer = await createPagamentos(buffer, pagamentos, "YA");
    buffer = await createTotalizadores(buffer, totalizadores);
    buffer = await createTecnico(buffer, tecnico);
    buffer = await createObservacoes(buffer, observacoes);
    buffer = await closeFile(buffer);

    return buffer; // Retorna o conteúdo como string ou buffer
  } catch (error) {
    throw new Error(`Erro ao gerar o arquivo TX2: ${error}`);
  }
};

/**
 * Gera o arquivo tx2 (para NFe) no caminho especificado.
 * @param caminhoTx2 o caminho onde o tx2 será gerado (um arquivo com o mesmo nome não pode existir)
 * @param dadosNota um objeto contendo os dados iniciais da nota.
 * @param itens um array contendo objetos com os dados dos itens.
 * @param pagamentos um array contendo as informações das formas de pagamento utilizadas.
 * @return retorna uma string do caminho onde o arquivo foi gerado
 */
const generateMFeTx2 = async (
  dadosNota,
  itens,
  pagamentos,
  tecnico
) => {
  let buffer = ""; // Inicializa um buffer (string) vazio

  try {
    buffer = await createHeader(buffer);
    buffer = await createDadosNota(buffer, dadosNota);
    buffer = await createDadosItens(buffer, itens);
    buffer = await createPagamentos(buffer, pagamentos, "PAGAMENTO");
    buffer = await createTecnico(buffer, tecnico);
    buffer = await closeFile(buffer);

    return buffer; // Retorna o conteúdo como string ou buffer
  } catch (error) {
    throw `Erro ao gerar o arquivo TX2: ${error}`;
  }
};

/**
 * Gera o arquivo tx2 (para NFe) no caminho especificado.
 * @param caminhoTx2 o caminho onde o tx2 será gerado (um arquivo com o mesmo nome não pode existir)
 * @param posDataInformation Informações para emitir o tx2
 * @return retorna uma string do caminho onde o arquivo foi gerado
 */
const sendPaymentMfeTx2 = (
  posDataInformation
) => {
  return new Promise((resolve, reject) => {
    let buffer = ""; // Inicializa um buffer (string) vazio

    try {
      // Monta o conteúdo do TX2 diretamente no buffer
      buffer += `Formato=TX2\nInterface=EnviarPagamento\n`;
      buffer += `NumeroDocumento=${posDataInformation.docNumber}\n`;
      buffer += `ChaveAcessoValidador=${posDataInformation.company?.validatorAccessKey}\n`;
      buffer += `ChaveRequisicao=${posDataInformation.requestKey}\n`;
      buffer += `Estabelecimento=${posDataInformation.establishment}\n`;
      buffer += `CNPJ=${posDataInformation.company?.cnpj}\n`;
      buffer += `IcmsBase=${posDataInformation.sale?.value}\n`;
      buffer += `ValorTotalVenda=${posDataInformation.sale?.value}\n`;
      buffer += `HabilitarMultiplosPagamentos=${posDataInformation.sale?.isMultiplePayments}\n`;
      buffer += `HabilitarControleAntiFraude=false\n`;
      buffer += `CodigoMoeda=BRL\n`;
      buffer += `EmitirCupomNFCE=false\n`;
      buffer += `OrigemPagamento=1\n`;
      buffer += `SerialPOS=${posDataInformation.serialPOS}\n`;

      resolve(buffer); // Retorna o buffer contendo o TX2 gerado
    } catch (error) {
      reject(`Erro ao gerar o arquivo TX2: ${error}`);
    }
  });
};

/**
 * Gera o arquivo tx2 (para NFe) no caminho especificado.
 * @param caminhoTx2 o caminho onde o tx2 será gerado (um arquivo com o mesmo nome não pode existir)
 * @param posDataInformation Informações para emitir o tx2
 * @return retorna uma string do caminho onde o arquivo foi gerado
 */
const verifyStatusMfeTx2 = (
  posDataInformation
) => {
  return new Promise((resolve, reject) => {
    try {
      // Inicializa o buffer como uma string vazia
      let buffer = "";

      // Concatena as informações do TX2 diretamente no buffer
      buffer += `Formato=TX2\n`;
      buffer += `numeroDocumento=${posDataInformation.docNumber}\n`;
      buffer += `Interface=VerificarStatusValidador\n`;
      buffer += `ChaveAcessoValidador=${posDataInformation.company?.validatorAccessKey}\n`;
      buffer += `IdFila=${posDataInformation.idFila}\n`;
      buffer += `CNPJ=${posDataInformation.company?.cnpj}\n`;

      resolve(buffer); // Retorna o conteúdo gerado do TX2
    } catch (error) {
      reject(`Erro ao gerar o arquivo TX2: ${error}`);
    }
  });
};

/**
 * Gera o arquivo tx2 para a resposta da fatura MFe.
 * @param caminhoTx2 o caminho onde o tx2 será gerado (um arquivo com o mesmo nome não pode existir)
 * @param posDataInformation Informações para emitir o tx2
 * @return retorna uma string do caminho onde o arquivo foi gerado
 */
const invoiceResponseMfeTx2 = (
  posDataInformation
) => {
  return new Promise((resolve, reject) => {
    try {
      // Inicializa o buffer como uma string vazia
      let buffer = "";

      // Concatena as informações do TX2 diretamente no buffer
      buffer += `formato=tx2\n`;
      buffer += `NumeroDocumento=${posDataInformation.docNumber}\n`;
      buffer += `Interface=RespostaFiscal\n`;
      buffer += `ChaveAcessoValidador=${posDataInformation.company?.validatorAccessKey}\n`;
      buffer += `IdFila=${posDataInformation.idFila}\n`;
      buffer += `ChaveAcesso=${posDataInformation.requestKey}\n`;
      buffer += `NumeroAprovacao=${posDataInformation.approveCodeNumber}\n`;
      buffer += `Bandeira=${posDataInformation.creditCard?.brand}\n`;
      buffer += `Adquirente=${posDataInformation.creditCard?.companyName}\n`;
      buffer += `CNPJ=${posDataInformation.company?.cnpj}\n`;
      buffer += `ImpressaoFiscal=<![CDATA[ TANCA\n${posDataInformation.printerStringify}\n]]>\n`;

      resolve(buffer); // Retorna o conteúdo gerado do TX2 como string
    } catch (error) {
      reject(`Erro ao gerar o arquivo TX2: ${error}`);
    }
  });
};

module.exports = {
  sendNFCe,
  sendNFe,
  print,
  generatecNF_B03,
  generateNFCeTx2,
  generateNFeTx2,
  cancelNFCe,
  generateMFeTx2,
  sendPaymentMfeTx2,
  verifyStatusMfeTx2,
  invoiceResponseMfeTx2
};