const KitModel = require('../../models/Kit');
const ProductModel = require('../../models/Produto');
const { addProduct } = require('../../modules/inventory/config/api');
const { addProducts } = require('../../modules/inventory/services/addProducts');
const { getProductOnInventory } = require('../../modules/inventory/services/getProduct');
const { removeProduct } = require('../../modules/inventory/services/removeProducts');
const { getCurrentTime } = require('../../utils');
const { geraCod } = require('../products/extensions');
const { Types } = require('mongoose');

class HigienizationClass {
  constructor() {};

  async create(req, res) {
    const { name, qtd, products } = req.body;

    const verifyIfExistKit = await KitModel.findOne({ name });

    if (verifyIfExistKit) {
      return res.status(400).json({
        status: false,
        error: 'Já existe um kit com o nome digitado.',
      });
    }

    let newProductSaleValue = 0;
    let newProductPurchaseValue = 0;
    let newProductMinValue = 0;
    let productsToRemoveFromStock = [];

    products?.forEach(product => {
      newProductSaleValue += product?.vlrVenda * (product?.qtd || 0)
      newProductPurchaseValue += product?.vlrCusto * (product?.qtd || 0)
      newProductMinValue += product?.vlrMin * (product?.qtd || 0)

      productsToRemoveFromStock.push({
        ...product,
        qtd: product?.qtd * qtd,
        idProduto: product?._id,
      })
    })

    const barCode = await geraCod()
    const lastProduct = await ProductModel.find().limit(1).sort({ codigo: -1 })

    const productCode = Number(lastProduct?.[0]?.codigo) + 1;

    const productData = {
      ncm: '63090010',
      kit: true,
      descricao: String(name).toUpperCase(),
      favorito: false,
      deleted: false,
      deleted_at: null,
      pesquisa: false,
      ativo: true,
      vlrVenda: Number(newProductSaleValue),
      vlrCusto: Number(newProductPurchaseValue),
      vlrMin: Number(newProductMinValue),
      codBarras: barCode,
      codigo: productCode,
      coeficiente: null,
      cnpj: req?.usuario?.enterprise?.cnpj,
    }

    try {
      const createdProduct = await ProductModel.create(productData);
      const createdKit = await KitModel.create({
        name: String(name).toUpperCase(),
        qtd,
        products,
        productId: createdProduct?._id,
        enterpriseCnpj: req?.usuario?.enterprise?.cnpj,
      })

      if (createdKit) {
        await removeProduct({ items: productsToRemoveFromStock, cnpj: req?.usuario?.enterprise?.cnpj });
        await addProducts({ items: [{ ...productData, qtd, idProduto: createdProduct?._id }], cnpj: req?.usuario?.enterprise?.cnpj });

        return res.status(200).json(createdProduct);
      }
    } catch (error) {
      console.log(error)
      return res.status(500).json({
        status: false,
        error: 'Ocorreu um erro ao realizar o cadastro do kit',
      });
    }
  };

  async findAll(req, res) {
    const allKits = await KitModel.find({ enterpriseCnpj: req?.usuario?.enterprise?.cnpj, deleted: false }).lean();

    let kitList = [];

    for (const kit of allKits) {
      const kitOnStock = await getProductOnInventory(req?.usuario?.enterprise?.cnpj, kit?.productId);

      kitList.push({
        ...kit,
        qtd: kitOnStock?.product?.quantity
      })
    };

    return res.status(200).json(kitList)
  }

  async delete(req, res) {
    const { id } = req.params;
    const { qtd } = req.query;
    const currentTime = getCurrentTime()

    const kitToDelete = await KitModel.findOne({ _id: new Types.ObjectId(id) });
    const product = await ProductModel.findOne({ _id: new Types.ObjectId(kitToDelete?.productId) });

    if (!product || !kitToDelete) {
      return res.status(404).json({
        status: false,
        error: 'Produto não encontrado',
      });
    };

    const productsToAddOnStock = [];

    for (const prod of kitToDelete?.products) {
      productsToAddOnStock.push({
        ...prod,
        qtd: Number(prod?.qtd) * qtd,
        idProduto: prod?._id
      })
    };

    try {
      await addProducts({ items: productsToAddOnStock, cnpj: req?.usuario?.enterprise?.cnpj });
      await removeProduct({ items: [{ ...product, qtd, idProduto: product?._id }], cnpj: req?.usuario?.enterprise?.cnpj });

      await ProductModel.findOneAndUpdate({ _id: new Types.ObjectId(kitToDelete?.productId)}, { deleted: true, deleted_at: currentTime });
      await KitModel.findOneAndUpdate({ _id: new Types.ObjectId(kitToDelete?._id)}, { deleted: true, deleted_at: currentTime, qtd: kitToDelete?.qtd - qtd });
      return res.status(200).json();
    } catch (error) {
      console.log(error);
      return res.status(400).json({ status: false, error: 'Erro ao deletar o kit.'});
    }
  };

  async getItemFromKit(req, res) {
    const { id } = req.params;

    const product = await ProductModel.findOne({ _id: Types.ObjectId(id)}).lean();

    if (!product) {
      return res.status(404).json({
        status: false,
        error: "Não existe nenhum produto com este ID."
      })
    };

    return res.status(200).json(product);
  };

  async addStockToKit(req, res) {
    const { id } = req?.params;
    const { qtd } = req?.body;

    const product = await ProductModel.findOne({ _id: Types.ObjectId(id) }).lean();
    const kit = await KitModel.findOne({ productId: id }).lean();

    if (!product) {
      return res.status(404).json({
        status: false,
        error: "Não existe nenhum produto com este ID."
      });
    }

    if (!kit) {
      return res.status(404).json({
        status: false,
        error: "Não existe nenhum kit cadastrado com este ID."
      });
    }

    let productsToRemoveFromStock = [];

    kit?.products?.forEach(product => {
      productsToRemoveFromStock.push({
        ...product,
        qtd: product?.qtd * qtd,
        idProduto: product?._id,
      })
    })

    try {
      await removeProduct({ items: productsToRemoveFromStock, cnpj: req?.usuario?.enterprise?.cnpj });
      await addProducts({ items: [{ ...product, qtd, idProduto: id }], cnpj: req?.usuario?.enterprise?.cnpj });
      return res.status(200).json(product);
    } catch (error) {
      return res.status(400).json({
        status: false,
        error
      });
    }
  };
}

module.exports = HigienizationClass
