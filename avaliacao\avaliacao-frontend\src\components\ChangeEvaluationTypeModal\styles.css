.change-type-modal-container {
  display: flex;
  flex-flow: column;
  gap: 1.5rem;
  padding: 2rem;
  background-color: #FFF;
  width: 20%;
  border-radius: 5px;
}

.change-type-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.type-options {
  display: flex;
  align-items: center;
  gap: 30px;
}

.type-options section {
  display: flex;
  align-items: center;
  gap: .5rem;
}

.change-type-buttons-container {
  width: 100%;
  display: flex;
  gap: 20px;
  align-items: center;
}

.change-type-buttons-container button {
  padding: .5rem;
  width: 100%;
  border: none;
  outline: none;
  border-radius: .4rem;
  color: #FFF;
}

.change-type-buttons-container .btn-confirm {
  background-color: peru;
}

.change-type-buttons-container .btn-confirm:disabled {
  background-color: #CCC;
  cursor: not-allowed;
}

.change-type-buttons-container .btn-cancel {
  background-color: #555;
}

button {
  cursor: pointer;
  transition: all 0.2s;
}

button:hover {
  opacity: 0.8;
}

@media (max-width: 1281px) {
  .change-type-modal-container {
    width: 50%;
  }
}

@media (max-width: 980px) {
  .change-type-modal-container {
    width: 90%;
  }

  .type-options {
    display: flex;
    flex-flow: column;
    align-items: flex-start;
    gap: 10px;
  }
}


