const { Router } = require('express')
const isAuth = require('../../../middlewares/isAuth')
const PointsRules = require('../../../controllers/pointsRules')

const PointsRulesRoutes = Router()
const PointsRulesController = new PointsRules()

PointsRulesRoutes.get('/getRules', isAuth, PointsRulesController.getRules)
PointsRulesRoutes.post('/create', isAuth, PointsRulesController.createRule)

module.exports = PointsRulesRoutes
