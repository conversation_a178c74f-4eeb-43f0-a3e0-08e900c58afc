const { isValidObjectId } = require('mongoose')
const moment = require('moment-timezone')
const utils = require('./../../utils')
const { calculateValuePriceItemSearch } = require('./helpers/calculateValuePriceItemSearch')
const inventory = require('../../modules/inventory/services/index')
const Caixa = require('../../models/Caixa')

const EnterpriseModel = require('./../../models/Empresa')
const EvaluationModel = require('./../../models/Avaliacao')
const ProductModel = require('./../../models/Produto')
const PointsModel = require('../../models/Points')
const PointsRulesModel = require('../../models/PointsRules')
const VouchersModel = require('../../models/Vale')
const HigienizationModel = require('../../models/Higienization')
const UserModel = require('../../models/Usuario')

const VouchersClass = require('../vouchers')
const { generateBarCodeTime } = require('../../utils/barCode')
const { encrypt, decrypt } = require('../../utils/crypto')
const { uploadToS3FromBuffer } = require('../../modules/S3')

const fsExtra = require('fs-extra')

class EvaluationClass {
  constructor() {}

  async getEvaluationById(req, res) {
    const { id } = decrypt(req.body.data, process.env.PRIVATE_KEY)

    if (!isValidObjectId(id))
      return res.status(400).json({
        status: false,
        error: 'Invalid ID',
      })

    const evaluation = await EvaluationModel.findById(id).lean()

    res.status(200).json({
      data: encrypt(
        {
          status: true,
          evaluation,
        },
        process.env.PRIVATE_KEY
      ).toString(),
    })
  }

  async getEvaluations(req, res) {
    const evaluations = await EvaluationModel.find({}).limit(500)

    res.status(200).json({
      status: true,
      evaluations: evaluations,
    })
  }

  async getProductsOfEvaluation(req, res) {
    const { evaluationId } = req.params

    if (!isValidObjectId(evaluationId)) {
      return res.status(400).json({
        status: false,
        error: 'Invalid evaluation id',
      })
    }

    const evaluation = await EvaluationModel.findOne({ _id: evaluationId }).lean()

    if (!evaluation) {
      return res.status(404).json({
        status: false,
        error: 'Evaluation not found',
      })
    }

    const productsList = evaluation?.items?.map(async item => {
      if (!item.idProduto) return

      const product = await ProductModel.findById(item.idProduto).lean()

      if (!product) return

      const valorDef =
        evaluation.itensEstoque &&
        evaluation.itensEstoque.length &&
        evaluation.itensEstoque.some(is => is.codBarras === product.codBarras)
          ? evaluation.itensEstoque.find(is => is.codBarras === product.codBarras).valorDef
          : 0

      return {
        ...item,
        ...product,
        description:
          (item.ePesquisa && `${product.descricao} - ${item.descPesquisa}`) || product.descricao,
        quantidade: item.quantidade || item?.qtd,
        valorMin: calculateValuePriceItemSearch(item, 2) || product.vlrMin,
        valorMax: calculateValuePriceItemSearch(item, 1.5) || product.vlrVenda,
        valorDef: valorDef,
        codBarras: product.codBarras,
        precificado: product.precificado || false,
        impresso: product.impresso || true,
        vlrVenda: item?.vlrVenda,
        qtd: item?.qtd - (item?.qtdRm || 0),
      }
    })

    const listProducts = await Promise.all(productsList)

    res.status(200).json({
      status: true,
      products: [
        ...(listProducts || []),
        ...(evaluation?.higienizationItems || []),
        ...(evaluation?.discardedItems || []),
      ],
      type: evaluation?.pricingType,
    })
  }

  async updateEvaluation(req, res) {
    const { evaluation, voucher } = decrypt(req.body?.data, process.env.PRIVATE_KEY) || {}

    if (!isValidObjectId(evaluation.id)) {
      return res.status(400).json({
        status: false,
        error: 'Invalid ID of evaluation',
      })
    }

    const existEvaluation = await EvaluationModel.findOne({ _id: evaluation?.id || evaluation?._id });

    if (!existEvaluation) {
      return res.status(400).json({
        status: false,
        error: 'Not found any evaluation',
      })
    }

    const [findEvaluation] = await findEvaluations({ _id: evaluation.id })

    if (findEvaluation?.finalizado) {
      return res.status(400).json({
        status: false,
        error: 'This evaluation is already completed.',
      })
    }

    if (req?.usuario?.enterprise?.cnpj !== evaluation?.cnpj) {
      return res.status(400).json({
        status: false,
        error: 'Invalid CNPJ for this user',
      })
    }

    if (!evaluation.idCaixa) {
      return res.status(400).json({
        status: false,
        error: 'Invalid ID Caixa of evaluation',
      })
    }

    const cashier = await Caixa.findOne({
      _id: evaluation.idCaixa,
    }).lean()

    if (cashier?.dataFechamento) {
      return res.status(400).json({
        status: false,
        error: 'This cashier is closed',
      })
    }

    if (evaluation.ePesquisa) {
      evaluation.precoBruto = !evaluation.precoBruto > 5000 ? evaluation.precoBruto : 5000
    }

    if (evaluation?.manual === false || evaluation?.cancelado === false) {
      const props = ['idProduto', 'qtd', 'excluido', 'ePesquisa', 'descPesquisa']
      const checkIfItemsHaveAllProperties = evaluation?.items?.map(item =>
        props.every(prop => item.hasOwnProperty(prop))
      )
      const verifyIfAllItemsHaveProperties = checkIfItemsHaveAllProperties.every(
        bool => bool === true
      )
      if (!verifyIfAllItemsHaveProperties) {
        return res.status(409).json({
          status: false,
          error: 'Conflict in itens of evaluation',
        })
      }
    }

    if (evaluation.finalizado) {
      evaluation.dataFinal = evaluation.dataFinal || moment.utc().subtract({ hours: 3 }).toDate()
    }

    let codBarras = existEvaluation?.codBarras

    if (!codBarras) {
      codBarras = generateBarCodeTime(
        String(req.usuario.enterprise.codUnd).padStart(4, '0'),
        evaluation?.cpf
      )
    }

    let items = []

    for (const item of evaluation?.items) {
      const productId = item?._id || item?.idProduto
      const currentProduct = await ProductModel?.findOne({ _id: productId }).lean()

      for (let i = 0; i < item?.qtd; i++) {
        items?.push({
          ...currentProduct,
          ...item,
        })
      }
    }

    const batchValue = items?.reduce((acc, item) => acc + Number(item?.vlrVenda), 0)

    try {
      const evaluationUpdated = await EvaluationModel.findOneAndUpdate(
        { _id: evaluation.id || evaluation?._id },
        {
          items: evaluation.items,
          motivo: evaluation.motivo,
          dataFinal: evaluation.dataFinal,
          totalPixPadrao: evaluation?.totalPixPadrao || '0',
          totalPixEfetivado: evaluation?.totalPixEfetivado || '0',
          totalDinheiroPadrao: evaluation?.totalDinheiroPadrao || '0',
          totalDinheiroEfetivado: evaluation?.totalDinheiroEfetivado || '0',
          totalValePadrao: evaluation?.totalValePadrao || '0',
          totalValeEfetivado: evaluation?.totalValeEfetivado || '0',
          totalValorizacaoManual: evaluation?.totalValorizacaoManual || '0',
          tipo: evaluation.tipo,
          finalizado: true,
          cancelado: evaluation.cancelado,
          oferta: evaluation.oferta,
          caixa: evaluation.idCaixa,
          manual: evaluation.manual,
          manualReason: evaluation?.manualReason,
          uf: evaluation?.uf,
          offline: evaluation?.offline,
          codBarras,
          cpf: evaluation?.cpf,
          cnpj: evaluation?.cnpj,
          clientExpectation: evaluation?.clientExpectation || 0,
          pricingType: evaluation?.pricingType,
          evaluationValueDiference: evaluation?.evaluationValueDiference || 0,
          ...(evaluation.rejected === true && {
            rejected: evaluation.rejected,
            reject_at: utils.getCurrentTime(),
          }),
          ip: req.ip,
          batchValue,
        },
        { new: true }
      )

      if (!evaluation?.blockStock) {
        inventory.addProducts(evaluationUpdated)
      }

      let createdVoucher = null

      const evaluationVoucher = await VouchersModel.findOne({
        idAvaliacao: voucher?.idAvaliacao,
      }).lean()

      if (evaluation.tipo === 'Vale' && voucher && evaluationVoucher === null) {
        if (voucher?.vlrTotal != evaluation?.totalValeEfetivado) {
          return res.status(400).json({
            status: false,
            error: 'The value of the voucher is different from the value made in the assessment',
          })
        }

        let invalidCnpj = false

        if (
          voucher?.cnpj !== evaluation?.cnpj ||
          voucher?.cnpj !== req?.usuario?.enterprise?.cnpj
        ) {
          invalidCnpj = true
        }

        if (invalidCnpj) {
          return res.status(400).json({
            status: false,
            error: 'Invalid cnpj on evaluation or voucher.',
          })
        }

        createdVoucher = await VouchersModel.create({
          ...voucher,
          codBarras,
          data: moment.utc(voucher?.data).toDate(),
          dataExpiracao: moment.utc(voucher?.dataExpiracao).toDate(),
          idAvaliacao: evaluation?.id,
        })
      } else {
        createdVoucher = evaluationVoucher
      }

      if (createdVoucher?.status === false) {
        return res.status(400).json(createdVoucher)
      }

      res.status(200).json({
        status: true,
        evaluation: encrypt(
          {
            evaluationUpdated,
            ...(evaluation?.tipo === 'Vale' && {
              voucher: createdVoucher,
            }),
          },
          process.env.PRIVATE_KEY
        ).toString(),
      })
    } catch (error) {
      console.log(error)
    }
  }

  async rejectedEvaluation(req, res) {
    const { evaluationId } = req.body

    if (!isValidObjectId(evaluationId))
      return res.status(400).json({
        status: false,
        error: 'Invalid ID',
      })

    const _evaluationRejected = await EvaluationModel.findOneAndUpdate(
      { _id: evaluationId },
      {
        rejected: true,
        reject_at: moment.utc().subtract({ hours: 3 }).toDate(),
      },
      { new: true }
    )

    if (_evaluationRejected) {
      await inventory.removeProduct(_evaluationRejected)
    }

    return res.status(200).json({
      status: true,
      evaluationRejected: _evaluationRejected,
    })
  }

  async updateItemsOfStock(req, res) {
    const { evaluationId } = req.params
    const { items } = req.body

    if (!isValidObjectId(evaluationId))
      return res.status(400).json({
        status: false,
        error: 'Invalid ID',
      })

    const evaluation = await EvaluationModel.findOneAndUpdate(
      { _id: evaluationId },
      {
        itensEstoque: items,
      },
      { new: true }
    )

    return res.status(200).json({
      status: true,
      evaluation,
    })
  }

  async getOpenEvaluations(req, res) {
    const { cnpj, idEvaluationUser } = req.body

    if (!cnpj) {
      return res.status(400).json({
        status: false,
        error: 'CNPJ Inválido!',
      })
    }

    if (!idEvaluationUser) {
      return res.status(400).json({
        status: false,
        error: 'Usuário inválido!',
      })
    }

    const openEvaluation = await EvaluationModel.findOne({
      finalizado: false,
      cnpj,
      idUsuarioAvaliacao: idEvaluationUser,
    }).lean()

    return res.status(200).json(openEvaluation)
  }

  async createEvaluation(req, res) {
    const {
      motivo,
      usuario,
      cnpj,
      totalValePadrao,
      totalValeEfetivado,
      totalDinheiroPadrao,
      totalDinheiroEfetivado,
      oferta,
      tipo,
      cpf,
      cliente,
      telefone,
      idPreAvaliacao,
      idUsuarioAvaliacao,
      caixa,
      dataInicio,
      dataPreAvaliacao,
      items,
      canceledAt,
      cancelado,
    } = decrypt(req.body?.data, process.env.PRIVATE_KEY) || {}

    try {
      if (!cnpj) {
        return res.status(400).json({
          status: false,
          errorMenssage: 'Informe o CNPJ corretamente.',
        })
      }

      if (idPreAvaliacao) {
        const existEvaluation = await EvaluationModel.findOne({
          idPreAvaliacao,
        }).lean()

        if (existEvaluation) {
          return res.status(200).json(encrypt(existEvaluation, process.env.PRIVATE_KEY).toString())
        }
      }

      const cashier = await Caixa.findOne({ _id: caixa })

      if (cashier?.dataFechamento) {
        return res.status(400).json({
          status: false,
          error: 'This cashier is already closed.',
        })
      }

      if (idUsuarioAvaliacao !== req?.usuario?._id) {
        return res.status(400).json({
          status: false,
          error: 'Invalid user provided.',
        })
      }

      if (cnpj !== req?.usuario?.enterprise?.cnpj) {
        return res.status(400).json({
          status: false,
          error: 'Invalid user cnpj provided.',
        })
      }

      const existUser = await UserModel.findOne({ _id: idUsuarioAvaliacao })

      if (!existUser) {
        return res.status(404).json({
          status: false,
          errorMessage: 'Usuário não existe!',
        })
      }

      const avaliacao = {
        items: [],
        motivo,
        usuario,
        cnpj,
        dataInicio: dataInicio || moment.utc().subtract({ hours: 3 }).toDate(),
        dataFinal: null,
        totalValePadrao,
        totalValeEfetivado,
        totalDinheiroPadrao,
        totalDinheiroEfetivado: totalDinheiroEfetivado || '0',
        oferta,
        tipo,
        finalizado: false,
        cancelado: false,
        cpf,
        cliente,
        telefone,
        idPreAvaliacao,
        caixa,
        idUsuarioAvaliacao,
        dataPreAvaliacao: dataPreAvaliacao || null,
        items,
        cancelado: cancelado || false,
        canceledAt: canceledAt || null,
        ip: req.ip,
      }

      const newEvaluation = await EvaluationModel.create(avaliacao)
      return res.status(200).json(encrypt(newEvaluation, process.env.PRIVATE_KEY).toString())
    } catch (error) {
      console.log(error)
      return res.status(400).json({
        status: false,
        error,
      })
    }
  }

  async checkOperations(req, res) {
    const { cashierId } = req.body

    const cashier = await Caixa.findOne({ _id: cashierId }).lean()

    if (!cashier) {
      return res.status(200).json({
        status: false,
        cashier: {},
      })
    }

    const evaluations = await EvaluationModel.find({
      finalizado: true,
      caixa: cashierId,
      dataFinal: { $gte: cashier?.dataAbertura },
    }).lean()

    const paymentConditions = {
      money: 0,
      voucher: 0,
      pix: 0,
    }

    evaluations.forEach(evaluation => {
      if (!evaluation.cancelado && evaluation.finalizado)
        if (evaluation.tipo === 'Dinheiro') {
          if (!isNaN(Number(evaluation.totalDinheiroEfetivado)))
            paymentConditions.money += Number(evaluation.totalDinheiroEfetivado)
        } else if (evaluation.tipo === 'Vale') {
          if (!isNaN(Number(evaluation.totalValeEfetivado)))
            paymentConditions.voucher += Number(evaluation.totalValeEfetivado)
        } else if (evaluation.tipo === 'Pix') {
          if (!isNaN(Number(evaluation.totalValeEfetivado)))
            paymentConditions.pix += Number(evaluation.totalPixEfetivado)
        }
    })

    return res.status(200).json({
      cashier,
      evaluations,
      paymentConditions,
    })
  }

  async cancelEvaluation(req, res) {
    const evaluation = await EvaluationModel.findOneAndUpdate(
      { _id: req.body.idAvaliacao },
      {
        items: req.body.items,
        motivo: req.body.motivo,
        dataFinal: req.body.dataFinal,
        totalDinheiroPadrao: req.body.totalDinheiroPadrao || 0,
        totalDinheiroEfetivado: req.body.totalDinheiroEfetivado || 0,
        totalPixPadrao: req.body.totalPixPadrao || 0,
        totalPixEfetivado: req.body.totalPixEfetivado || 0,
        totalValePadrao: req.body.totalValePadrao || 0,
        totalValeEfetivado: req.body.totalValeEfetivado || 0,
        tipo: req.body.tipo,
        codBarras: req.body.codBarras,
        finalizado: true,
        cancelado: true,
        oferta: req.body.oferta,
        caixa: req.body.idCaixa,
        rejected: false,
        canceledAt: req.body.canceledAt || null,
        higienizationItems: null,
        manual: false,
        ip: req.ip,
      }
    )

    inventory.removeProduct({
      items: req.body.items,
      cnpj: req.body.cnpj,
    })

    const existsProductsInHigienization = await HigienizationModel.findOne({
      evaluationId: req.body.idAvaliacao,
    }).lean()

    if (existsProductsInHigienization) {
      await HigienizationModel.deleteOne({ evaluationId: req.body.idAvaliacao })
    }

    const company = await EnterpriseModel.findOne({ cnpj: req.body.cnpj }).lean()
    const isPointsProgram = company?.settings?.pointsProgram || false
    if (isPointsProgram) {
      const existPointsForEvaluation = await PointsModel.findOne({
        referenceId: req.body.idAvaliacao,
        isCanceled: false,
      })

      if (existPointsForEvaluation) {
        await PointsModel.findOneAndUpdate(
          { _id: existPointsForEvaluation?._id },
          { isCanceled: true, updatedAt: moment.utc().subtract({ hours: 3 }).toDate() }
        )
      }
    }

    // const payload = new Payload(RoutingKeys.EVALUATION_CANCELED, evaluation)
    // Broker.publish(payload)

    res.json(evaluation)
  }

  async changeEvaluationType(req, res) {
    const { id } = req.params
    const { type } = decrypt(req.body?.data, process.env.PRIVATE_KEY)

    const evaluation = await EvaluationModel.findOne({ _id: id }).lean()
    const pointsRules = await PointsRulesModel.findOne({ app: 'evaluation' })

    if (!evaluation) {
      return res.status(404).json({
        status: false,
        error: 'Avaliação não encontrada!',
      })
    }

    let voucherData = null
    let newPoints = 0

    if (type === 'Vale') {
      const voucher = {
        cpf: evaluation?.cpf,
        vlrAberto: Number(evaluation?.totalValeEfetivado),
        vlrTotal: Number(evaluation?.totalValeEfetivado),
        nomeCliente: evaluation?.cliente,
        idAvaliacao: evaluation?._id,
        codCidade: req?.usuario?.enterprise?.codCidade,
        data: moment.utc(evaluation?.dataFinal).subtract({ hours: 3 }).toDate(),
        cnpj: evaluation?.cnpj,
        dataExpiracao: moment
          .utc(evaluation?.dataFinal)
          .add({ year: 1 })
          .subtract({ hours: 3 })
          .toDate(),
        codBarras: evaluation?.codBarras,
      }

      voucherData = await VouchersModel.create(voucher)
      newPoints =
        Number(evaluation?.totalDinheiroEfetivado) *
        pointsRules?.paymentConditionsValue?.['giracredit']
    } else {
      if (evaluation?.tipo === 'Vale') {
        const voucher = await VouchersModel.findOne({ idAvaliacao: evaluation?._id })

        if (voucher?.vlrAberto !== voucher?.vlrTotal) {
          return res.status(400).json({
            status: false,
            error: 'O tipo não pode ser alterado, vale já utilizado.',
          })
        }

        await VouchersModel.deleteOne({ idAvaliacao: evaluation?._id })
      }

      if (type === 'Dinheiro') {
        newPoints =
          Number(evaluation?.totalDinheiroEfetivado) *
          pointsRules?.paymentConditionsValue?.['money']
      } else if (type === 'Pix') {
        newPoints =
          Number(evaluation?.totalDinheiroEfetivado) * pointsRules?.paymentConditionsValue?.['pix']
      }
    }

    const updatedEvaluation = await EvaluationModel.findOneAndUpdate(
      { _id: id },
      { $set: { tipo: type } },
      { new: true }
    )

    const company = await EnterpriseModel.findOne({ cnpj: req.body.cnpj }).lean()
    const isPointsProgram = company?.settings?.pointsProgram || false
    if (isPointsProgram) {
      const pointsFromEvaluation = await PointsModel.findOne({
        referenceId: evaluation?._id,
      }).lean()

      if (pointsFromEvaluation) {
        await PointsModel.findOneAndUpdate(
          { referenceId: evaluation?._id },
          { points: Number(newPoints) }
        )
      }
    }

    return res.status(200).json(
      encrypt(
        {
          evaluation: updatedEvaluation,
          ...(voucherData && {
            voucher: voucherData,
          }),
        },
        process.env.PRIVATE_KEY
      ).toString()
    )
  }

  async addBatchImages(req, res) {
    const { id } = req.params
    const files = req.files

    let images = []

    for (const img of files) {
      const uploadedImage = await uploadToS3FromBuffer(img, 'images/lotes')
      images.push({
        key: uploadedImage?.Key,
        url: uploadedImage?.Location,
      })
    }

    const updatedEvaluation = await EvaluationModel.findOneAndUpdate(
      { _id: id },
      {
        images,
      }
    )

    fsExtra.emptyDir('../../../tmp')
    return res.status(200).json(updatedEvaluation)
  }

  async updatePricingType(req, res) {
    const { evaluationId } = req.params
    const { items, type } = req.body

    const existEvaluation = await EvaluationModel.findOne({ _id: evaluationId })

    if (!existEvaluation) {
      return res.status(404).json({
        status: false,
        error: 'Evaluation not found.',
      })
    }

    try {
      await EvaluationModel.findOneAndUpdate(
        { _id: evaluationId },
        {
          items,
          pricingType: type,
        }
      )

      return res.status(200).json()
    } catch (error) {
      console.log(error)

      return res.status(500).json({
        status: false,
        error,
      })
    }
  }
}

const findEvaluations = async (filter = {}) => {
  return await EvaluationModel.find(filter)
}

module.exports = EvaluationClass
