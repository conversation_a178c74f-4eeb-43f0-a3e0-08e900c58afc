name: Deploy to EKS - master

on:
  push:
    branches: [master]

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: prod

    env:
      NAMESPACE: production
      CLUSTER_NAME: cluster-producao
      APPLICATION_NAME: avaliacao-backend
      ECR_REPOSITORY: dfcom-avaliacao-prod
      DOCKERFILE: Dockerfile.eks

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v1
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_EKS }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_EKS }}
        aws-region: ${{ secrets.AWS_REGION }}

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v1

    - name: Build, tag and push image to Amazon ECR
      id: build-image
      env:
        ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
        IMAGE_TAG: ${{ github.sha }}
      run: |
        docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -f $DOCKERFILE .
        docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
        echo "::set-output name=image::$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG"

    - name: Setup kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBECONFIG }}
        context: ${{ env.CLUSTER_NAME }}

    - name: Install and configure Helm
      uses: azure/setup-helm@v3
      with:
        version: v3.11.1

    - name: Deploy to EKS
      run: |
        helm upgrade --install $APPLICATION_NAME ./helm -f ./helm/$NAMESPACE-values.yaml --namespace $NAMESPACE --set image.repository=${{ steps.build-image.outputs.image }}
    
    - name: Discord notification
      env:
        DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
      uses: Ilshidur/action-discord@master
      with:
        args: 'Deploy efetuado (avaliacao-backend / prod)'
