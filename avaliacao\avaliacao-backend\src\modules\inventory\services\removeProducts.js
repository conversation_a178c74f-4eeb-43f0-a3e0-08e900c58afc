const inventoryApi = require('../config/api')
const { createInventory } = require('./createInventory')
const { getProduct } = require('./getProduct')

const removeProduct = async evaluation => {
  const { items, cnpj } = evaluation || {}

  if (!items || !items?.length) return

  const inventory = await inventoryApi.getInventory(cnpj)

  if (!Object.keys(inventory?.company || {}).length) {
    await createInventory(cnpj)
  }

  const productsPromises = items?.map(async item => {
    const product = await getProduct(item.idProduto)

    return {
      _id: product?._id || item?.idProduto,
      code: product.codigo,
      description: product.descricao,
      codeUnity: product.unidade || 'UN',
      quantity: -Math.abs(item.qtd),
      salePrice: product.vlrVenda,
      costPrice: product.vlrCusto,
      barCode: product.codBarras,
      condicao: product.condicao || 'NAO INFORMADO',
      isActive: product.ativo,
    }
  })

  if (productsPromises?.length) {
    await inventoryApi.addProduct(cnpj, await Promise.all(productsPromises))
  }
}

module.exports = { removeProduct }
