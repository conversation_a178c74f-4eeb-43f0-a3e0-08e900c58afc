const bcrypt = require('bcryptjs')
const jwt = require('jsonwebtoken')
const jwtConfig = require('../../config/jwt')
const { Types } = require('mongoose')
const moment = require('moment')

const User = require('../../models/Usuario')
const PdvUser = require('../../models/UsuarioPDV')
const Caixa = require('../../models/Caixa')
const SettingsModel = require('../../models/Settings')
const RefreshTokenModel = require('../../models/RefreshToken')

const { encrypt, decrypt } = require('../../utils/crypto')

const ObjectId = id => new Types.ObjectId(id)

class UsersClass {
  constructor() {}

  async list(req, res) {
    const filter = req.query
    const users = await PdvUser.find(
      {
        $or: [{ ...filter }, { unidade: 'Todas' }],
      },
      {
        senha: 0,
      }
    )

    return res.status(200).json({
      data: encrypt(users, process.env.PRIVATE_KEY).toString(),
    })
  }

  async login(req, res) {
    const { username, password } = decrypt(req.body.data || {}, process.env.PRIVATE_KEY)
    const user = await User.findOne({
      usuario: username,
      tipo: {
        $in: ['avaliador', 'administrador'],
      },
    })
    if (!user) {
      return res.status(401).send({
        message: 'Usuário ou senha inválidos',
      })
    }

    let verifyPassword = false

    try {
      verifyPassword = await bcrypt.compare(password, user.get('senha'))
    } catch (e) {}

    if (!verifyPassword) {
      return res.status(401).send({
        message: 'Usuário ou senha inválidos',
      })
    }

    const { enterprise } = await getUserInfo(user?._id)

    if (enterprise?.validateIP && enterprise?.publicIP && enterprise?.publicIP !== '*') {
      if (enterprise?.publicIP !== (req.headers['x-forwarded-for'] || req.socket.remoteAddress)) {
        return res.status(403).json({
          status: false,
          message: enterprise?.blockMessage || 'Unidade bloqueada, contate o suporte.',
        })
      }
    }

    const requestUser = await getUserInfo(user?._id)
    const settings = await SettingsModel?.find({
      $or: [{ app: 'avaliacao', active: true }, { type: 'points.program' }],
    })?.lean()
    const token = jwt.sign(requestUser, jwtConfig.SECRET_JWT, { expiresIn: jwtConfig.expiresIn })

    // salvar o token token
    try {
      const resp = await RefreshTokenModel.findOneAndUpdate(
        { id_user: requestUser?._id },
        {
          accessToken: [token],
          refreshToken: token,
          expiredAt: moment.utc().add({ hours: 12 }).toDate(),
          createdAt: moment.utc().toDate(),
          updatedAt: moment.utc().toDate(),
        },
        { upsert: true }
      )

      const caixa = await Caixa.findOne({ dataFechamento: null, idUsuario: requestUser._id }).lean()

      return res.status(200).json({
        data: encrypt(
          {
            usuario: {
              ...requestUser,
              settings,
            },
            token,
            caixa,
          },
          process.env.PRIVATE_KEY
        ).toString(),
      })
    } catch (err) {
      console.log(err)
    }
  }

  async logout(req, res) {
    const { _id } = req.usuario

    const refreshToken = await RefreshTokenModel.findOne({ id_user: _id })

    if (!refreshToken) {
      return res.status(404).json({
        status: false,
        error: 'Unkown refresh token for this user',
      })
    }

    try {
      await RefreshTokenModel.findOneAndDelete({ _id: refreshToken?._id })
      return res.status(200).json()
    } catch (error) {
      console.log(error)
      return res.status(500).json({
        status: false,
        error,
      })
    }
  }

  async fetch(req, res) {
    const { usuario: user } = req
    let findUser = {}

    try {
      findUser = await getUserInfo(user._id)
    } catch (error) {
      console.log(error)
    }

    const { enterprise } = findUser

    // if (enterprise?.validateIP && enterprise?.publicIP && enterprise?.publicIP !== '*') {
    //   if (enterprise?.publicIP !== (req.headers['x-forwarded-for'] || req.socket.remoteAddress)) {
    //     return res.status(403)
    //       .json({
    //         status: false,
    //         message: enterprise?.blockMessage || 'Unidade bloqueada, contate o suporte.',
    //       })
    //   }
    // }

    const caixa = await Caixa.findOne({ dataFechamento: null, idUsuario: findUser._id }).lean()
    const settings = await SettingsModel?.find({ app: 'avaliacao' }).lean()

    return res.status(200).json({
      usuario: {
        ...findUser,
        settings,
      },
      caixa,
    })
  }

  async checkIsManager(req, res) {
    const { user, password } = req.body
    const { usuario: authUser } = req

    if (authUser?.tipo === 'Administrador') {
      return res.status(400).json({
        status: false,
        error: 'Você não possui permissão!',
      })
    }

    const condition = {
      codigo: user,
      tipo: {
        $in: ['Administrador', 'Gerente', 'Etiqueta', 'avaliacao_manual', 'Eventos'],
      },
    }

    const findUser = await PdvUser.findOne(condition).lean()

    if (!findUser) {
      return res.status(400).send({
        status: false,
        error: 'Usuário ou senha inválidos',
      })
    }

    let verifyPassword = false

    try {
      verifyPassword = await bcrypt.compare(password, findUser?.['senha'])

      if (!verifyPassword) {
        return res.status(400).send({
          status: false,
          error: 'Usuário ou senha inválidos',
        })
      }

      return res.sendStatus(200)
    } catch (e) {
      return res.status(400).send({
        status: false,
        error: 'Usuário ou senha inválidos',
      })
    }
  }

  async createUser(req, res) {
    const { usuario, senha, tipo, unidades } = req.body

    const request = {
      usuario,
      senha,
      tipo,
      unidades,
    }

    bcrypt.genSalt(10, (erro, salt) => {
      bcrypt.hash(request.senha, salt, (erro, hash) => {
        if (erro) {
          console.log(erro)
        } else {
          request.senha = hash
          new User(request)
            .save()
            .then(obj => res.send(obj))
            .catch(erro => res.status(400).json(erro))
        }
      })
    })
  }

  async totemLogin(req, res) {
    const { username, password } = req?.body

    const user = await User.findOne({ usuario: username, tipo: 'avaliacaoTotem' }).lean()

    if (!user || user === null) {
      return res.status(404).json({
        status: false,
        error: 'Usuário ou senha inválidos.',
      })
    }

    const verifyPassword = await bcrypt.compare(password, user?.senha)

    if (!verifyPassword) {
      return res.status(404).json({
        status: false,
        error: 'Usuário ou senha inválidos.',
      })
    }

    const token = jwt.sign(user, jwtConfig.SECRET_JWT, { expiresIn: '30d' })

    await RefreshTokenModel.findOneAndUpdate(
      { id_user: user?._id },
      {
        accessToken: [token],
        refreshToken: token,
        expiredAt: moment.utc().add({ days: 30 }).toDate(),
        createdAt: moment.utc().toDate(),
        updatedAt: moment.utc().toDate(),
      },
      { upsert: true }
    )

    return res.status(200).json({
      ...user,
      token,
    })
  }
}

async function getUserInfo(id) {
  id = ObjectId(id)

  const user = await User.aggregate([
    {
      $match: {
        _id: id,
      },
    },
    {
      $unwind: {
        path: '$unidades',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $unwind: {
        path: '$permission',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        localField: 'unidades.cnpj',
        from: 'empresas',
        foreignField: 'cnpj',
        as: 'enterprise',
      },
    },
    {
      $unwind: {
        path: '$enterprise',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $project: {
        usuario: 1,
        senha: 1,
        tipo: 1,
        senha: 1,
        permission: 1,
        enterprise: {
          nomeUnidade: 1,
          cnpj: 1,
          codUnd: 1,
          ativo: 1,
          endereco: 1,
          numero: 1,
          bairro: 1,
          uf: 1,
          cep: 1,
          cnpj: 1,
          telefone: 1,
          celular: 1,
          cidade: 1,
          nomeReduzido: 1,
          codCidade: 1,
          plus: 1,
          beta: 1,
          settings: 1,
          blocked: 1,
          blockMessage: 1,
          publicIP: 1,
          validateIP: 1,
        },
      },
    },
  ])

  return user?.[0] || {}
}

module.exports = UsersClass
