import moment from "moment";
import { IPrintReturnTerm } from "../../types";
import "./styles.css";

interface ReturnTermProps {
  data: IPrintReturnTerm;
}

const ReturnTerm: React.FC<ReturnTermProps> = ({
  data: { reason, canceledAt, client },
}) => {
  return (
    <div id="termo" style={{ display: "block" }}>
      <div className="companyReturn">
        <h1 style={{ fontSize: "16px" }}>
          TERMO DE DEVOLUÇÃO DE MERCADORIAS ENTREGUES PARA AVALIAÇÃO
        </h1>
      </div>
      <div className="information-term">
        <p style={{ fontSize: "12px", textAlign: "justify", margin: 0 }}>
          <strong>Data de cancelamento: </strong>
          {moment.utc(canceledAt).format("DD/MM/YYYY HH:mm:ss")}
        </p>
        <p style={{ fontSize: "12px", textAlign: "justify", margin: 0 }}>
          <strong>Motivo do cancelamento: </strong>
          {reason || ""}
        </p>
      </div>
      <div className="declaration">
        <p style={{ fontSize: "12px", textAlign: "justify", margin: 0 }}>
          Eu {client?.name}, CPF/MF nº {client?.cpf}, declaro que retirei os
          produtos que por mim foram entregues na Cresci e Perdi, procedendo à
          conferência de todos os itens, estando eles no mesmo estado quando
          entregues ao estabelecimento, nada mais tenho a retirar ou receber da
          empresa.
        </p>
      </div>
      <div
        className="signature"
        style={{ fontSize: "12px", textAlign: "center", margin: 0 }}
      >
        <p>_______________________</p>
        <p>{client?.name}</p>
      </div>
    </div>
  );
};

export default ReturnTerm;
