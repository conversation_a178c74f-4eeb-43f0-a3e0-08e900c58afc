const { Router } = require('express')
const router = Router()

const isAuth = require('../../../middlewares/isAuth')

const PreEvaluationClass = require('../../../controllers/pre-evalutions')
const PreEvaluationController = new PreEvaluationClass()

router.post('/get_pre_evaluations', isAuth, PreEvaluationController.getPreEvaluations)
router.get('/get_pre_evaluation', isAuth, PreEvaluationController.getPreEvaluation)
router.post('/delete/:id', isAuth, PreEvaluationController.deletePreEvaluation)

module.exports = router
