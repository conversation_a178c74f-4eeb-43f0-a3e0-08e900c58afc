import React, { Component } from 'react'

export class ShowItensAvaliation extends Component {
  constructor(props) {
    super(props)
  }

  render() {
    const { itens } = this.props

    return (
      <div>
        {itens !== null ? (
          itens?.map((item, index) => {
            let pesquisa = false

            if (item.items.ePesquisa) pesquisa = true
            return (
              <div className="linha-detalhe" key={index}>
                <div className="item2-row" style={{ width: '450px' }}>
                  <div style={{ margin: '.5rem 0' }}>
                    <p>{item.prod_item?.descricao} {item?.items?.ePesquisa ? ` - ${item?.items?.descPesquisa} - ${item?.items?.condProd === 0 ? 'Novinho' : item?.items?.condProd === 1 ? 'Com Detalhes' : item?.items?.condProd === 2 ? 'Muitos detalhes' : 'Novinho'}` : ''}</p>
                  </div>
                </div>
                {pesquisa ? (
                  <>
                    <div className="item2-row" style={{ width: '190px' }}>
                      <div style={{ margin: '.5rem 0' }}>
                        <p>
                          Preço Bruto:{' '}
                          {item?.items?.precoBruto?.toLocaleString('pt-BT', {
                            style: 'currency',
                            currency: 'BRL',
                          })}
                        </p>
                      </div>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="item2-row" style={{ width: '190px' }}></div>
                  </>
                )}
                <div className="item2-row">
                  <div style={{ margin: '.5rem 0' }}>
                    Quantidade: {item.items.qtde || item?.items?.qtd || 0}
                  </div>
                </div>
              </div>
            )
          })
        ) : (
          <div style={{ textAlign: 'center' }}>Sem itens</div>
        )}
      </div>
    )
  }
}
