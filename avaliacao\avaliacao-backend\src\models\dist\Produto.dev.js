"use strict";

var _require = require('mongoose'),
    Schema = _require.Schema,
    model = _require.model;

var _require2 = require("../utils"),
    getCurrentTime = _require2.getCurrentTime;

var currentTime = getCurrentTime(); // Definindo Schema

var ProdutoSchema = Schema({
  vlrVenda: {
    type: Number,
    "default": 0
  },
  nivel1: {
    type: String,
    "default": null
  },
  nivel2: {
    type: String,
    "default": null
  },
  nivel3: {
    type: String,
    "default": null
  },
  nivel4: {
    type: String,
    "default": null
  },
  descricao: {
    type: String,
    "default": null
  },
  vlrCusto: {
    type: Number,
    "default": 0
  },
  vlrMin: {
    type: Number,
    "default": 0
  },
  codBarras: {
    type: String,
    "default": null
  },
  pesquisa: {
    type: Boolean,
    "default": null
  },
  coeficiente: {
    type: Number,
    "default": null
  },
  favorito: {
    type: <PERSON><PERSON>an,
    "default": false
  },
  codigo: {
    type: Number,
    "default": null
  },
  ativo: {
    type: Boolean,
    "default": true
  },
  create_at: {
    type: Date,
    "default": currentTime
  },
  update_at: {
    type: Date,
    "default": null
  },
  deleted: {
    type: Boolean,
    "default": false
  },
  deleted_at: {
    type: Date,
    "default": null
  },
  peso: {
    type: Number,
    "default": null
  },
  ncm: {
    type: String,
    "default": '63090010'
  },
  precificacao_automatica: {
    type: Object,
    "default": {
      padrao: {
        vlrCusto: 0,
        vlrVenda: 0,
        giracredito: 0
      },
      oferta1: {
        vlrCusto: 0,
        vlrVenda: 0,
        giracredito: 0
      },
      oferta2: {
        vlrCusto: 0,
        vlrVenda: 0,
        giracredito: 0
      }
    }
  },
  indexingTime: {
    type: Date,
    "default": null
  },
  acumulaPontos: {
    type: Boolean,
    "default": true
  },
  tipoFixo: {
    type: Boolean,
    "default": false
  },
  valorPontosFixos: {
    type: Number,
    "default": null
  },
  limitValue: {
    type: Number
  },
  cnpj: {
    type: String,
    "default": null
  },
  novo: {
    type: Boolean,
    "default": true
  },
  visualizar: {
    type: Boolean,
    "default": true
  },
  unused: {
    type: Boolean,
    "default": false
  },
  allowLocation: {
    type: Boolean,
    "default": false
  },
  locInfos: {
    type: Object,
    "default": {}
  },
  locProductId: {
    type: String,
    "default": null
  },
  version: {
    type: String,
    require: false
  }
});
{}
/* GOOGLE LENS */
//   pesquisaFoto: {
//     type: Boolean,
//     default: false,
//   }
// })
// Definindo collection

var Produto = model('produto', ProdutoSchema);
module.exports = Produto;