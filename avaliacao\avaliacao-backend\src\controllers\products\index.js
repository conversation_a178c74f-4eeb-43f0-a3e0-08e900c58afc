const { geraProduto } = require('../products/extensions')
const { Types } = require('mongoose')
const moment = require('moment')

const Produto = require('../../models/Produto')
const Nivel1 = require('../../models/Nivel1')
const Nivel2 = require('../../models/Nivel2')
const Nivel3 = require('../../models/Nivel3')
const Nivel4 = require('../../models/Nivel4')
const ProductStock = require('../../models/ProductStock')
const PrinterOrigin = require('../../models/Printer-origin')
const PrinterPipeline = require('../../models/Printer-pipeline')
const { createProduct } = require('../../services/loc/routes')
const { getCurrentTime } = require('../../utils')
const ProductStockProduct = require('../../models/ProductStockProduct')

class ProductsClass {
  constructor() {}

  async registerProductFromEvaluation(req, res) {
    const {
      nivel,
      descricao,
      valor,
      valorMin,
      valorMax,
      peso,
      pesquisa,
      coeficiente,
      isVisible = null,
      chave,
      vlrMin = null,
      precificacao_automatica,
      acumulaPontos,
      tipoFixo,
      valorPontosFixos,
      limitValue,
      pesquisaFoto,
      novo,
      visualizar,
      locInfos,
      token,
    } = req.body || {}

    let { ultimoNvl } = req.body || {}

    ultimoNvl = String(ultimoNvl).toLowerCase()

    const data = {
      descricao,
      ultimoNvl,
      valor,
      valorMin,
      valorMax,
      peso: Number(peso),
      pesquisa: pesquisa || 'não',
      coeficiente,
      isVisible,
      precificacao_automatica,
      limitValue,
      novo,
      visualizar,
      allowLocation: !!locInfos,
      ...(locInfos && {
        locInfos,
      }),
      // pesquisaFoto: pesquisaFoto || false,
    }

    // Format Data by Nivel
    if (nivel !== 'nivel1') data['chave'] = chave
    if (nivel === 'nivel4') data['vlrMin'] = vlrMin

    const NiveisModel = {
      nivel1: Nivel1,
      nivel2: Nivel2,
      nivel3: Nivel3,
      nivel4: Nivel4,
    }

    const acceptedNiveis = Object.keys(NiveisModel)

    if (!acceptedNiveis.includes(nivel)) {
      return res.status(400).json({
        status: true,
        error: 'Nivel não existe',
      })
    }

    let saveNivel = null

    // let existDescriptionInNivel = false

    // try {
    //   existDescriptionInNivel = !!(await NiveisModel[nivel]
    //     .findOne({
    //       descricao,
    //     })
    //     .lean())
    // } catch (e) {}

    // if (existDescriptionInNivel) {
    //   return res.status(400).json({
    //     status: true,
    //     error: 'Já existe uma descrição semelhante neste nivel',
    //   })
    // }

    try {
      saveNivel = await new NiveisModel[nivel](data).save()
    } catch (e) {}

    if (!saveNivel) {
      return res.status(400).json({
        status: true,
        error: 'Falha ao atualizar nivel!',
      })
    }

    const response = {
      nivel: saveNivel,
      produto: {},
    }

    if (valorPontosFixos) req.body.valorPontosFixos = Number(valorPontosFixos)

    if (ultimoNvl === 'sim' || pesquisaFoto) {
      try {
        const saveProduct = await geraProduto(req.body, saveNivel)

        let createdLocProduct = {}

        if (locInfos && token) {
          const locProduct = {
            name: locInfos?.name,
            brand: locInfos?.brand,
            model: locInfos?.model,
            cost: Number(locInfos?.costValue),
            barCode: saveProduct?.codBarras,
            dynamicPrice: {
              15: locInfos?.values[14],
              30: locInfos?.values[28],
            },
            stockManagement: true,
          }

          const createProductResponse = await createProduct(locProduct, token)

          // const stockData = {
          //   product: createProductResponse,
          //   cnpj: req?.usuario?.enterprise?.cnpj,
          //   stock: {

          //   }
          // }

          // const createProductStockResponse = await createProductLocation(stockData, token);
          createdLocProduct = createProductResponse
        }

        response['produto'] = await new Produto({
          ...saveProduct,
          ...(createdLocProduct && {
            locProductId: createdLocProduct?._id,
          }),
        }).save()
      } catch (e) {
        console.log(e)
      }
    }

    res.status(200).json(response)
  }

  async search(req, res) {
    let filter = req.query || {}
    const limit = req.query.limit || 100
    const page = req?.query?.page || 1

    filter.limit && delete filter.limit
    filter.page && delete filter.page
    filter.ativo = 'true'

    if (filter?.all === 'false') {
      filter.$or = [
        { create_at: { $gte: moment().startOf('day').toDate() } },
        { update_at: { $gte: moment().startOf('day').toDate() } },
      ]
    }

    filter['$or'] = [
      ...(filter?.$or || []),
      {
        deleted: { $ne: true },
        unused: false,
      },
      {
        deleted: { $exists: false },
        unused: { $exists: false },
      },
      {
        deleted: { $ne: true },
        unused: { $exists: false },
      },
    ]

    delete filter?.all
    filter.version = { $exists: false }

    let products = await Produto.find(filter)
      .skip((Number(page) - 1) * limit)
      .limit(Number(limit))
      .lean()

    if (filter?.descricao) {
      products = products?.filter(product =>
        String(product?.descricao).toLowerCase().includes(filter?.descricao?.toLowerCase())
      )
    }

    const array = []
    products.forEach(produto => {
      if (produto.codBarras === '1230000000000') {
        array.push(produto)
      }
    })

    products
      .map(product => {
        Number(String(product?.vlrVenda).split('.')[0])
        return product
      })
      .sort((a, b) => {
        if (a.valorVenda > b.valorVenda) {
          return 1
        }
        if (a.valorVenda < b.valorVenda) {
          return -1
        }

        return 0
      })
      .map(elem => array.push(elem))

    return res.status(200).json(array)
  }

  async searchById(req, res) {
    const { id } = req.params

    const products = await Produto.findById(id).lean()
    return res.status(200).json(products)
  }

  async getFavoriteProducts(req, res) {
    const products = await Produto.find({ favorito: true, version: { $exists: false } }).lean()

    const array = []
    products.forEach(product => {
      if (product.codBarras === '1230000000000') {
        array.push(product)
      }
    })

    products
      .map(product => {
        Number(String(product?.vlrVenda).split('.')[0])
        return product
      })
      .sort((a, b) => {
        if (a.valorVenda > b.valorVenda) {
          return 1
        }
        if (a.valorVenda < b.valorVenda) {
          return -1
        }

        return 0
      })
      .map(elem => array.push(elem))

    return res.json({
      status: true,
      products: array,
    })
  }

  async getIndexingProductsTime(req, res) {
    const indexingProducts = await Produto.find({
      indexingTime: { $ne: null },
    }).lean()

    let products = []

    indexingProducts.forEach(product => {
      if (product?.nivel4) {
        products.push({
          _id: product?.nivel4,
          indexingTime: product?.indexingTime,
        })
        return
      }

      if (product?.nivel3) {
        products.push({
          _id: product?.nivel3,
          indexingTime: product?.indexingTime,
        })
        return
      }

      if (product?.nivel2) {
        products.push({
          _id: product?.nivel2,
          indexingTime: product?.indexingTime,
        })
        return
      }

      if (product?.nivel1) {
        products.push({
          _id: product?.nivel1,
          indexingTime: product?.indexingTime,
        })
        return
      }
    })

    return res.status(200).json(products)
  }

  async printLocationProducts(req, res) {
    const { codunidade, Itens } = req.body

    const {
      enterprise: { cnpj },
    } = req.usuario

    let productsToPrint = []

    const products = {}

    for (const product of Itens) {
      const productExistOnStock = await ProductStock.findOne({
        productLocation: new Types.ObjectId(product?.locProductId),
        cnpj,
      }).lean()
      const productToPrint = await Produto?.findOne({ locProductId: product?.locProductId }).lean()

      try {
        if (productExistOnStock) {
          const createdStock = await ProductStock.findOneAndUpdate(
            {
              _id: productExistOnStock?._id,
            },
            {
              current: productExistOnStock?.current + product?.qtd,
              total:
                productExistOnStock?.total < productExistOnStock?.current + product?.qtd
                  ? productExistOnStock?.current + product?.qtd
                  : productExistOnStock?.total,
              updatedAt: getCurrentTime(),
            }
          )

          const createdProductInStock = await ProductStockProduct.create({
            stock: new Types.ObjectId(createdStock?._id),
            productLocation: new Types.ObjectId(product?.locProductId),
            cnpj,
            barcode: productToPrint?.codBarras,
            status: 'available',
            images: [],
          })

          if (!(createdStock?._id in products)) {
            products[createdStock?._id] = []
          }

          products[createdStock?._id].push(createdProductInStock?._id)
        } else {
          const newId = new Types.ObjectId(product?.locProductId)

          const createdStock = await ProductStock.create({
            current: product?.qtd,
            total: product?.qtd,
            cnpj,
            productLocation: newId,
          })

          const createdProductInStock = await ProductStockProduct.create({
            stock: new Types.ObjectId(createdStock?._id),
            productLocation: newId,
            cnpj,
            barcode: productToPrint?.codBarras,
            status: 'available',
            images: [],
          })

          if (!(createdStock?._id in products)) {
            products[createdStock?._id] = []
          }

          products[createdStock?._id].push(createdProductInStock?._id)
        }
      } catch (error) {
        console.log(error)
        return res.status(400).json({
          status: false,
          error: 'Erro ao operar estoque do produto',
        })
      }

      productsToPrint.push({
        ...productToPrint,
        qtd: product?.qtd,
      })
    }

    for (const [key, value] of Object.entries(products)) {
      console.log('entrou')

      const a = await ProductStock.findOneAndUpdate(
        {
          _id: key,
        },
        {
          $set: {
            items: value,
          },
        }
      )

      console.log(a)
    }

    const origin = await PrinterOrigin.findOne({ description: 'ALUGUEL' })

    await PrinterPipeline.create({
      codunidade,
      data: {
        codunidade,
        Itens: productsToPrint?.map(product => ({
          descricao: product?.descricao,
          qtd: product?.qtd,
          codbarras: product?.codBarras,
          valor1: product?.locInfos?.values?.[14],
          valor2: product?.locInfos?.values?.[28],
          novo: true,
        })),
      },
      ownerPrinterCnpj: cnpj,
      typeId: origin?._id,
    })

    return res.status(200).json()
  }
}

module.exports = ProductsClass
