/* eslint-disable camelcase */
const { Schema, model } = require('mongoose')

// Definindo Schema
const PermissionSchema = Schema({})

// Definindo Schema
const UsuarioPDVSchema = Schema({
  usuario: {
    type: String,
  },
  senha: {
    type: String,
    required: true,
  },
  tipo: {
    type: String,
    required: true,
  },
  unidade: {
    type: String,
    required: true,
  },
  cnpj: {
    type: String,
    required: true,
  },
  codigo: {
    type: String,
    required: true,
  },
  serie: {
    type: Number,
  },
  sequencia: {
    type: Number,
  },
  // definir depois
  codTipo: {
    type: Number,
  },
  num_caixa: {
    type: Number,
  },
  serialPOS: {
    type: String,
    default: null,
  },
  fiscal: {
    type: Boolean,
    default: false,
  },
  permission: {
    type: [PermissionSchema],
    default: [],
  },
})

const UsuarioPDV = model('usuarioPDV', UsuarioPDVSchema)
module.exports = UsuarioPDV
