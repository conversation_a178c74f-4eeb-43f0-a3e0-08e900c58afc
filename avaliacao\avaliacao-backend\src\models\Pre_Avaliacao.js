const { Schema, model } = require('mongoose')

// Definindo Schema
const Pre_AvaliacaoSchema = Schema({
  usuario: {
    type: String,
    require: true,
  },
  cnpj: {
    type: String,
    require: true,
  },
  data: {
    type: Date,
    require: true,
  },
  finalizado: {
    type: Boolean,
    default: false,
  },
  idCliente: {
    type: String,
    require: true,
  },
  volume: {
    type: Number,
    require: true,
  },
  pre_avaliacao_senha: {
    type: String,
    default: '',
  },
  deletedAt: {
    type: Date,
    default: null,
  },
  deleteReason: {
    type: String,
    default: null,
  }
})

// Definindo collection
const Pre_Avaliacao = model('pre_avaliacao', Pre_AvaliacaoSchema)
module.exports = Pre_Avaliacao
