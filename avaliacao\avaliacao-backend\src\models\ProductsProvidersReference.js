const { model, Schema } = require('mongoose')

// Definindo Schema
const ProductsProvidersReferences = Schema({
  productCode: String,
  providerCnpj: String,
  enterpriseCnpj: String,
  internalProductCode: String,
  createdAt: {
    type: Date,
    default: Date.now,
  },
  createdAt: {
    type: Date,
  },
  deletedAt: {
    type: Date,
    required: false,
    default: null
  }
})

// Definindo collection
const ProductsProvidersReference = model('products_providers_references', ProductsProvidersReferences)
module.exports = ProductsProvidersReference
