const PointsRulesModel = require('../../models/PointsRules')

class PointsRules {
  constructor() {}

  async getRules(req, res) {
    try {
      const rules = await PointsRulesModel.findOne({
        app: 'evaluation'
      })

      return res.status(200).json(rules)
    } catch (error) {
      console.log(error)
      return res.status(400).json({
        status: false,
        error: 'Error on getting points rules'
      })
    }
  }

  async createRule(req, res) {
    const {
      app,
      paymentConditionsValue,
      valuePoints,
      porcentage,
    } = req.body;

    try {
      const rule = await PointsRulesModel.create({
        app,
        paymentConditionsValue,
        valuePoints,
        porcentage
      })
      return res.status(201).json(rule);
    } catch (error) {
      console.log(error)
      return res.status(400).json({
        status: false,
        error: 'Error on getting points rules'
      })
    }
  }
}

module.exports = PointsRules;
