const mongoose = require('mongoose')
const db = require('../config/db')

let isConnected = false
let connectionId = null

const connectToDatabase = async () => {
  if (isConnected) {
    console.log('Utilizando conexão existente com o MongoDB')
    return
  }

  console.log(`MongoDB está na URI: ${db.mongoUri}`)

  await mongoose.connect(db.mongoUri, {
    maxPoolSize: 100,
    minPoolSize: 10,
    maxIdleTimeMS: 300000, // 5 minutos
    connectTimeoutMS: 10000, // 10 segundos
    socketTimeoutMS: 45000, // 45 segundos
  })

  isConnected = mongoose.connection.readyState
  connectionId = mongoose.connection.id
  console.log('MongoDB conectado:', isConnected)
  console.log('connectionId', connectionId)
}

connectToDatabase()
