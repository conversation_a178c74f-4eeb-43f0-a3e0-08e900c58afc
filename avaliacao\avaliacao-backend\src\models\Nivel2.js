const { Schema, model } = require('mongoose')
const { getCurrentTime } = require('../utils')

const currentTime = getCurrentTime()

// Definindo Schema
const Nivel2Schema = Schema({
  descricao: {
    type: String,
    require: true,
  },
  chave: {
    type: String,
    require: true,
  },
  ultimoNvl: {
    type: String,
    require: true,
  },
  valor: {
    type: Number,
    require: true,
  },
  valorMin: {
    type: Number,
    require: true,
  },
  valorMax: {
    type: Number,
    require: true,
  },
  peso: {
    type: Number,
    require: true,
  },
  pesquisa: {
    type: String,
    require: true,
  },
  coeficiente: {
    type: String,
    require: true,
  },
  create_at: {
    type: Date,
    default: currentTime,
  },
  update_at: {
    type: Date,
    default: null,
  },
  deleted: {
    type: Boolean,
    default: false,
  },
  deleted_at: {
    type: Date,
    default: null,
  },
  precificacao_automatica: {
    type: Object,
    default: {
      padrao: {
        vlrCusto: 0,
        vlrVenda: 0,
        giracredito: 0
      },
      oferta1: {
        vlrCusto: 0,
        vlrVenda: 0,
        giracredito: 0
      },
      oferta2: {
        vlrCusto: 0,
        vlrVenda: 0,
        giracredito: 0
      },
    }
  },
  limitValue: {
    type: Number,
    default: null,
  },
  novo: {
    type: Boolean,
    default: true,
  },
  visualizar: {
    type: Boolean,
    default: true,
  },
  unused: {
    type: Boolean,
    default: false,
  },
  allowLocation: {
    type: Boolean,
    default: false,
  },
  locInfos: {
    type: Object,
    default: {},
  }
})

{/* GOOGLE LENS */ }
//   pesquisaFoto: {
//     type: Boolean,
//     default: false,
//   }
// })

// Definindo collection
const Nivel2 = model('nivel2', Nivel2Schema)
module.exports = Nivel2
