import moment from "moment";
import { useAuth } from "../../../../context/auth";
import { IPrintCashierClosure } from "../../types";
import "./styles.css";

interface CashierClosureProps {
  data: IPrintCashierClosure;
}

const CashierClosure: React.FC<CashierClosureProps> = ({
  data: {
    openingDate,
    closeDate,
    amount,
    cashierMoney,
    cashierPix,
    cashierReinforcement,
    cashierSangria,
    cashierVoucher,
    notAcceptValueEvaluations,
    notProfileStoreEvaluations,
    customerDidNotWait = 0,
    test = 0,
    sistemError = 0,
    operatorError = 0,
    openingValue,
    othersEvaluations,
    othersEvaluationsReasons,
    qtyEvaluations,
    totalEvaluation,
    totalEvaluationManual,
    totalEvaluationManualValorization,
    totalEvaluationSystem,
    totalPriceManualValorization,
    username,
  },
}) => {
  return (
    <div id="ticket" style={{ display: "block" }}>
      <header
        style={{
          marginBottom: "2rem",
          marginTop: "2rem",
          textAlign: "center",
        }}
      >
        <div>
          <div className="company"></div>
          <div className="stateInformation">
            <p>Abertura: {openingDate}</p>
            <p>
              Fechamento:{" "}
              {closeDate ||
                moment
                  .utc()
                  .subtract({ hours: 3 })
                  .format("DD/MM/YYYY HH:mm:ss")}
            </p>
            <p>Operador: {username}</p>
          </div>
        </div>
      </header>
      <br />
      <div id="tablePrint">
        <table style={{ fontSize: "14px" }}>
          <thead>
            <tr>
              <th colSpan={2}>Fechamento de Caixa - Avaliação</th>
            </tr>
          </thead>

          <tbody
            style={{
              borderBottom: "2px solid #000",
              textAlign: "left",
              fontSize: "10px",
            }}
          >
            <tr style={{ border: "none" }}>
              <th>Inicio Caixa</th>
              <td>
                {Intl.NumberFormat("pt-BR", {
                  style: "currency",
                  currency: "BRL",
                }).format(Number(openingValue))}
              </td>
            </tr>

            <tr style={{ border: "none" }}>
              <th>Dinheiro</th>
              <td>
                {Intl.NumberFormat("pt-BR", {
                  style: "currency",
                  currency: "BRL",
                }).format(Number(cashierMoney))}
              </td>
            </tr>

            <tr style={{ border: "none" }}>
              <th>Pix</th>
              <td>
                {Intl.NumberFormat("pt-BR", {
                  style: "currency",
                  currency: "BRL",
                }).format(Number(cashierPix))}
              </td>
            </tr>

            <tr style={{ border: "none" }}>
              <th>{import.meta.env.VITE_GIRACREDITO}</th>
              <td>
                {Intl.NumberFormat("pt-BR", {
                  style: "currency",
                  currency: "BRL",
                }).format(Number(cashierVoucher))}
              </td>
            </tr>

            <tr style={{ border: "none" }}>
              <th>Reforço</th>
              <td>
                {Intl.NumberFormat("pt-BR", {
                  style: "currency",
                  currency: "BRL",
                }).format(Number(cashierReinforcement))}
              </td>
            </tr>
          </tbody>
        </table>
        <div className="line">
          <p>--------------------------------</p>
        </div>
        <table>
          <tbody
            className="expected"
            style={{ borderTop: "2px solid #000", marginTop: 15 }}
          >
            <tr></tr>
            <tr style={{ border: "none" }}>
              <th>Valor Previsto:</th>
              <td>
                {Intl.NumberFormat("pt-BR", {
                  style: "currency",
                  currency: "BRL",
                }).format(
                  Number(
                    Number(cashierReinforcement) +
                      Number(openingValue) -
                      Number(cashierMoney) -
                      Number(cashierSangria)
                  )
                )}
              </td>
            </tr>

            <tr style={{ border: "none" }}>
              <th>Total Avaliações:</th>
              <td>
                {Intl.NumberFormat("pt-BR", {
                  style: "currency",
                  currency: "BRL",
                }).format(
                  Number(cashierMoney) +
                    Number(cashierVoucher) +
                    Number(cashierPix)
                )}
              </td>
            </tr>
          </tbody>
        </table>
        <>
          <table style={{ marginTop: "-12px !important" }}>
            <h4 className="expected">Avaliações Recusadas</h4>
            <tbody
              className="expected"
              style={{ marginBottom: "10px !important" }}
            >
              <tr style={{ border: "none" }}>
                <h5>Não aceitou o valor: {notAcceptValueEvaluations}</h5>
                <h5>
                  Não enquadrou no perfil da loja: {notProfileStoreEvaluations}
                </h5>
                <h5>Fornecedor não quis esperar: {customerDidNotWait}</h5>
                <h5>Teste: {test}</h5>
                <h5>Erro sistema: {sistemError}</h5>
                <h5>Erro operador: {operatorError}</h5>
              </tr>
            </tbody>
          </table>
          <table>
            <h4 className="expected">Avaliações Canceladas</h4>
            <tbody
              className="expected"
              style={{ marginBottom: "10px !important" }}
            >
              <tr style={{ border: "none" }}>
                <h5>Qtd: {qtyEvaluations}</h5>
                <h5>
                  Valor Total:{" "}
                  {amount
                    ? Intl.NumberFormat("pt-BR", {
                        style: "currency",
                        currency: "BRL",
                      }).format(Number(amount))
                    : "R$ 0,00"}
                </h5>
              </tr>
            </tbody>
          </table>
        </>
        <div className="line">
          <p>--------------------------------</p>
        </div>
        <table style={{ marginTop: "1rem" }}>
          <tbody
            style={{
              borderBottom: "2px solid #000",
              textAlign: "left",
              fontSize: "10px",
            }}
          >
            <tr style={{ border: "none" }}>
              <td>Qtd Avaliações:</td>
              <td>{totalEvaluation}</td>
            </tr>
            <tr style={{ border: "none" }}>
              <td>Qtd Via Sistema: </td>
              <td>{totalEvaluationSystem}</td>
            </tr>
            <tr style={{ border: "none" }}>
              <td>Qtd Via Manual</td>
              <td>{totalEvaluationManual}</td>
            </tr>
            <tr>
              <td>Qtd Valorização Manual</td>
              <td>{totalEvaluationManualValorization}</td>
            </tr>
            <tr style={{ border: "none" }}>
              <td>Total Valorizado</td>
              <td>
                {Intl.NumberFormat("pt-BR", {
                  style: "currency",
                  currency: "BRL",
                }).format(Number(totalPriceManualValorization))}
              </td>
            </tr>
          </tbody>
        </table>
        <p>.</p>
      </div>
      <br />
    </div>
  );
};

export default CashierClosure;
