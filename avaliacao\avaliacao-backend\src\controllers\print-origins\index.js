const PrintOriginsModel = require('../../models/Printer-origin')

class PrintOriginsClass {
  constructor() {}

  async getPrintOrigins(req, res) {
    const filter = req.query
    const _printOrigins = await PrintOriginsModel.find(filter)

    return res.status(200).json({
      status: true,
      printOrigins: _printOrigins,
    })
  }

  async findPrinterOrigin(req, res) {
    const { origin } = req.query

    if (!origin) {
      return res.status(400).json({
        status: false,
        error: 'É necessário informar ORIGIN.',
      })
    }

    const printerOrigin = await PrintOriginsModel.findOne({ description: origin }).lean()

    if (!printerOrigin) {
      return res.status(404).json({
        status: false,
        error: 'Print origin não encontrado.',
      })
    }

    return res.status(200).json({
      status: true,
      origin: printerOrigin,
    })
  }
}

module.exports = PrintOriginsClass
