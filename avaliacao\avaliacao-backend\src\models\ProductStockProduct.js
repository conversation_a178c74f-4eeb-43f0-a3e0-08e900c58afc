const { Schema, model, Types } = require('mongoose')
const { getCurrentTime } = require('../utils')

const currentTime = getCurrentTime()

// Definindo Schema
const ProductStockProductSchema = Schema({
  stock: Types.ObjectId,
  productLocation: Types.ObjectId,
  barcode: {
    type: String,
    required: false,
  },
  cnpj: String,
  status: String,
  createdAt: {
    type: Date,
    default: currentTime,
  },
  updatedAt: {
    type: Date,
    default: null,
  },
})

// Definindo collection
const ProductStockProduct = model('products_stock_product', ProductStockProductSchema, 'products_stock_product')
module.exports = ProductStockProduct
