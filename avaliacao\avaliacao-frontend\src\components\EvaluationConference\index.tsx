import { useAuth } from "../../context/auth";

interface EvaluationConferenceProps {
  contentToPrint: any
}

const EvaluationConference: React.FC<EvaluationConferenceProps> = ({
  contentToPrint
}) => {
  const { cashier, user } = useAuth()

  return (
    <div id="ticket">
      <header style={{
        margin: '2rem 0',
        textAlign: 'center'
      }}>
        <div className="company">
          <h1>{user?.enterprise?.nomeReduzido}</h1>
          <p>CNPJ: {user?.enterprise?.cnpj}</p>
          <p>{user?.enterprise?.endereco},</p>
          <p>{user?.enterprise?.numero}</p>
          <p>{user?.enterprise?.bairro}</p>
          <p>TEL: {user?.enterprise?.telefone}</p>
          <p>------------------------------------------</p>
          <p>Data: {new Date().toLocaleString()}</p>
          <p>ID Caixa: {cashier?._id}</p>
          <p>------------------------------------------</p>
        </div>
      </header>
        {contentToPrint}
      <br />
    </div>
  )
}

export default EvaluationConference;
