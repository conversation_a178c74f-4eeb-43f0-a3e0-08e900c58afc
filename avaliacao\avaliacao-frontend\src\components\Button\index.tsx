import { ReactNode } from "react";
import "./styles.css";

interface ButtonProps {
  label: string | ReactNode;
  onClick: () => void;
  color: "primary" | "secondary" | "black";
  disabled?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  label,
  onClick,
  color,
  disabled = false,
}) => {
  return (
    <button
      className={`btn-component btn-${color}`}
      onClick={onClick}
      disabled={disabled}
    >
      {label}
    </button>
  );
};

export default Button;
