import Modal from "react-modal";
import * as BsIcons from "react-icons/bs";

import "./styles.css";
import Input from "../../Input";
import Select from "../../Select";
import Button from "../../Button";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";

interface GroupItemsModalProps {
  isOpen: boolean;
  onClose: () => void;
  products: {
    label: string;
    value: string;
    qtd: number;
  }[];
  onConcatClick: (product: string, qtd: number) => void;
}

const customStyles = {
  overlay: {
    background: "#00000050",
    zIndex: 999,
  },
  content: {
    background: "transparent",
    border: "none",
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
  },
};

const GroupItemsModal: React.FC<GroupItemsModalProps> = ({
  isOpen,
  onClose,
  products,
  onConcatClick,
}) => {
  const [selectedProduct, setSelectedProduct] = useState<string | undefined>(
    undefined
  );
  const [qtd, setQtd] = useState<number>(0);

  useEffect(() => {
    if (!isOpen) {
      setSelectedProduct(undefined);
      setQtd(0);
    }
  }, [isOpen]);

  return (
    <Modal isOpen={isOpen} onRequestClose={onClose} style={customStyles}>
      <div className="concat-items-modal">
        <div className="concat-items-modal-header">
          <h3>Agrupar itens</h3>
          <BsIcons.BsX color="#444" size={20} onClick={onClose} />
        </div>
        <div className="concat-items-modal-content">
          <Select
            id="concat-product"
            label="Produto"
            value={selectedProduct}
            onChange={(value: string) => setSelectedProduct(value)}
            options={products?.filter((product) => product?.qtd > 1)}
          />
          <div className="concat-items-modal-row">
            <Input
              id="concat-number"
              label="Quantidade"
              type="number"
              value={String(qtd)}
              onChange={(value: string) => setQtd(Number(value))}
              min={1}
              max={
                products?.find((p) => p?.value === selectedProduct)?.qtd || 1
              }
            />
            <Button
              label="Todos"
              color="primary"
              onClick={() => {
                const product = products?.find(
                  (p) => p?.value === selectedProduct
                );
                setQtd(product?.qtd || 0);
              }}
            />
          </div>
        </div>
        <div className="concat-items-modal-footer">
          <Button label="Cancelar" onClick={onClose} color="secondary" />
          <Button
            label="Agrupar"
            onClick={() => {
              if (selectedProduct === undefined) {
                return toast.error("Você precisa selecionar um produto.");
              }

              if (qtd === 0) {
                return toast.error("Quantidade não pode ser 0.");
              }

              if (qtd < 0) {
                return toast.error("Quantidade não pode ser negativa.");
              }

              onConcatClick?.(selectedProduct, qtd);
            }}
            color="primary"
          />
        </div>
      </div>
    </Modal>
  );
};

export default GroupItemsModal;
