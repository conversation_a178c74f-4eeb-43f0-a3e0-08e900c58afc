"use strict";var _require = require('express'),Router = _require.Router;
var router = Router();

var isAuth = require('../../../middlewares/isAuth');

var MessagesClass = require('../../../controllers/messages');
var MessagesController = new MessagesClass();

router.get('/show/:cnpj', isAuth, MessagesController.showMessages);
router.put('/edit/:id', isAuth, MessagesController.confirmReadMessage);

module.exports = router;