import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useAuth } from "../../context/auth";
import ApiService from "../../services/ApiService";
import Loading from "../Loading";
import "./styles.css";
import { decrypt } from "../../utils/crypto";

interface ManagerAuthProps {
  isOpen: boolean;
  onClose: () => void;
  onVerifyManager: () => void;
  plusManagers?: boolean;
  tagManagers?: boolean;
  manualEvaluationManagers?: boolean;
  eventManagers?: boolean;
}

const ManagerAuth: React.FC<ManagerAuthProps> = ({
  isOpen,
  onClose,
  onVerifyManager,
  plusManagers = false,
  tagManagers = false,
  manualEvaluationManagers = false,
  eventManagers
}) => {
  const { user } = useAuth();

  const [isLoading, setIsLoading] = useState(false);
  const [managers, setManagers] = useState([]);
  const [selectedManager, setSelectedManager] = useState("none");
  const [managerPassword, setManagerPassword] = useState("");

  const handleAuthenticateManager = async (e: any) => {
    e.preventDefault();

    if (selectedManager === "none") {
      return toast.error("Selecione um gerente para autenticar.");
    }

    setIsLoading(true);

    try {
      await ApiService.PostCheckIsManager({
        user: selectedManager,
        password: managerPassword,
      });

      onVerifyManager();
    } catch (err: any) {
      setSelectedManager("none");
      setManagerPassword("");
      return toast.error(err?.response?.data?.error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const getManagers = async () => {
      setIsLoading(true);
      try {
        let adminUsers = [];
        const { data: usersResponse } = await ApiService.GetListarUsuariosPdv(
          user?.enterprise?.cnpj
        );

        const users = decrypt(usersResponse.data, import.meta.env.VITE_PRIVATE_KEY)

        if (!plusManagers && !tagManagers && !manualEvaluationManagers && !eventManagers) {
          adminUsers = users.filter(
            (user: any) => user.codTipo === 1 || user.codigo === "Saullo"
          );
        } else if (tagManagers) {
          adminUsers = users.filter((user: any) => user.codTipo === 3);
        } else if (manualEvaluationManagers) {
          adminUsers = users.filter((user: any) => user.codTipo === 4);
        } else if (eventManagers) {
          adminUsers = users.filter((user: any) => user.codTipo === 5);
        } else {
          adminUsers = users.filter((user: any) => user.codTipo === 2);
        }

        setManagers(adminUsers);
      } catch (err: any) {
        console.log(err);
      } finally {
        setIsLoading(false);
      }
    };

    if (user?._id) {
      getManagers();
    }
  }, [user, tagManagers, eventManagers, manualEvaluationManagers]);

  useEffect(() => {
    if (!isOpen) {
      setSelectedManager("none");
      setManagerPassword("");
    }
  }, [isOpen]);

  return (
    <div
      className="manager-container"
      style={!isOpen ? { display: "none" } : undefined}
    >
      <form className="manager-content" onSubmit={handleAuthenticateManager}>
        <span style={{ fontSize: "25px" }}>Autenticar gerente</span>
        <div
          style={{
            display: "flex",
            gap: ".5rem",
            justifyContent: "space-between",
            width: "100%",
          }}
        >
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              gap: ".5rem",
              width: "50%",
            }}
          >
            <label htmlFor="user">Usuário</label>
            <select
              id="user"
              className="manager-input"
              value={selectedManager}
              onChange={(e) => setSelectedManager(e.target.value)}
            >
              <option value="none">Selecione</option>
              {managers?.map((manager: any, index: number) => (
                <option key={index} value={manager?.codigo}>
                  {manager?.usuario}
                </option>
              ))}
            </select>
          </div>
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              gap: ".5rem",
              width: "50%",
            }}
          >
            <label htmlFor="userPassword">Senha</label>
            <input
              id="userPassword"
              className="manager-input"
              type="password"
              value={managerPassword}
              onChange={(e) => setManagerPassword(e.target.value)}
            />
          </div>
        </div>
        <div className="buttons-container">
          <button type="button" className="secondary" onClick={onClose}>
            Fechar
          </button>
          <button type="submit" className="primary">
            Autenticar
          </button>
        </div>
      </form>
      <Loading isLoading={isLoading} />
    </div>
  );
};

export default ManagerAuth;
