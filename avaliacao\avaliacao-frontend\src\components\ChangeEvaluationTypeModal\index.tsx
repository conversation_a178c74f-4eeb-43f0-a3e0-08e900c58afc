import Modal from "react-modal";
import * as IonIcons from "react-icons/io5";
import "./styles.css";
import { useEffect, useState } from "react";
import Swal from "sweetalert2";
import {
  createNewVoucher,
  deleteVoucherOnChangeType,
} from "../../schema/others/avaliationUtils";
import { PrinterModal } from "../../pages/avaliacao/PrinterModal";
import { DexieDB } from "../../config/Dexie";
import { verifyPlatform } from "../../utils/platform";

interface IChangeEvaluationTypeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (type: string, printer: any) => void;
  currentType?: string;
}

const customStyles = {
  overlay: {
    background: "#00000050",
  },
  content: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    margin: "auto",
    background: "transparent",
    border: "none",
  },
};

const ChangeEvaluationTypeModal: React.FC<IChangeEvaluationTypeModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  currentType,
}) => {
  const [selectedType, setSelectedType] = useState<any>(null);
  const [printerModalIsOpen, setPrinterModalIsOpen] = useState<any>(false);
  const [printers, setPrinters] = useState<any[]>([]);

  const handleClose = () => {
    setSelectedType(null);
    onClose();
  };

  const handleConfirmSelectedType = async (printer?: any) => {
    if (currentType === "Vale") {
      const { isConfirmed } = await Swal.fire({
        ...deleteVoucherOnChangeType,
        icon: "warning",
      });

      if (isConfirmed) {
        return onConfirm(selectedType, printer);
      }
    } else {
      if (selectedType === "Vale") {
        const { isConfirmed } = await Swal.fire({
          ...createNewVoucher,
          icon: "warning",
        });

        if (isConfirmed) {
          return onConfirm(selectedType, printer);
        }
      } else {
        onConfirm(selectedType, printer);
      }
    }
  };

  useEffect(() => {
    const getPrintersList = async () => {
      const printersList = await DexieDB.printers.toArray();
      setPrinters(printersList);
    }
    getPrintersList()
  }, [])

  return (
    <Modal isOpen={isOpen} onRequestClose={handleClose} style={customStyles}>
      <div className="change-type-modal-container">
        <div className="change-type-modal-header">
          <h4>Alterar tipo da avaliação</h4>
          <IonIcons.IoCloseOutline
            size={20}
            style={{ cursor: "pointer" }}
            onClick={handleClose}
          />
        </div>
        <span>Selecione abaixo o novo tipo para a avaliação.</span>
        <div className="type-options">
          <section>
            <input
              type="radio"
              name="type"
              value="Dinheiro"
              id="dinheiro"
              disabled={currentType === "Dinheiro"}
              onChange={(e) => setSelectedType(e.target.value)}
            />
            <label
              htmlFor="dinheiro"
              style={currentType === "Dinheiro" ? { color: "#CCC" } : undefined}
            >
              Dinheiro
            </label>
          </section>
          <section>
            <input
              type="radio"
              name="type"
              value="Vale"
              id="vale"
              disabled={currentType === "Vale"}
              onChange={(e) => setSelectedType(e.target.value)}
            />
            <label
              htmlFor="vale"
              style={currentType === "Vale" ? { color: "#CCC" } : undefined}
            >
              Giracrédito
            </label>
          </section>
          <section>
            <input
              type="radio"
              name="type"
              value="Pix"
              id="pix"
              disabled={currentType === "Pix"}
              onChange={(e) => setSelectedType(e.target.value)}
            />
            <label
              htmlFor="pix"
              style={currentType === "Pix" ? { color: "#CCC" } : undefined}
            >
              Pix
            </label>
          </section>
        </div>
        <div className="change-type-buttons-container">
          <button className="btn-cancel" onClick={handleClose}>
            Cancelar
          </button>
          <button
            disabled={!selectedType}
            className="btn-confirm"
            onClick={() => {
              const platform = verifyPlatform();

              if (platform !== 'Windows') {
                if (printers?.length > 1 ) {
                  setPrinterModalIsOpen(true)
                } else {
                  handleConfirmSelectedType(printers[0]);
                }
              } else {
                handleConfirmSelectedType()
              }
            }}
          >
            Confirmar
          </button>
        </div>
      </div>
      <PrinterModal
        isOpen={printerModalIsOpen}
        onClose={() => setPrinterModalIsOpen(false)}
        onConfirmSelectedPrinter={(printer) => {
          handleConfirmSelectedType(printer)
          setPrinterModalIsOpen(false);
        }}
      />
    </Modal>
  );
};

export { ChangeEvaluationTypeModal };
