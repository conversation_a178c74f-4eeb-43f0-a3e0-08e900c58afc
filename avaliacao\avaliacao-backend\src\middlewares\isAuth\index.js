const jwt = require('jsonwebtoken')
const routerPaths = require('../validationRoutersConfig/index')
const { SECRET_JWT } = require('../../config/jwt')
const UserModel = require('../../models/Usuario')
const RefreshTokenModel = require('../../models/RefreshToken')
const moment = require('moment')

module.exports = async (request, response, next) => {
  const { authorization: auth } = request.headers || {}
  if (!auth) {
    return response.status(401).json({
      code: 130,
      message: 'Token não definido!',
    })
  }

  const [, token] = auth.split(' ')

  try {
    const decodeJWT = jwt.verify(token, SECRET_JWT)
    // verificar o token do usuario logado
    const refreshToken = await RefreshTokenModel.findOne({ id_user: decodeJWT?._id }).lean()
    if (
      !refreshToken ||
      refreshToken?.accessToken?.[0] !== token ||
      moment.utc().isAfter(moment.utc(refreshToken?.expiredAt))
    ) {
      return response.status(401).json({
        code: 130,
        message: 'Token invalido!',
      })
    }

    const existUser = await UserModel.findOne({ _id: decodeJWT._id })

    if (!existUser) {
      return response?.status(401).json({
        status: false,
        error: 'Usuário não existe.',
      })
    }

    request.usuario = decodeJWT
    next()
  } catch (e) {
    console.log("ERRO")
    console.log(e)
    return response.status(401).json({
      code: 130,
      message: 'Token invalido!',
    })
  }
}
