const { Router } = require('express')
const router = Router()

const isAuth = require('../../../middlewares/isAuth')

const EvaluationClass = require('../../../controllers/evaluations')
const EvaluationController = new EvaluationClass()

router.get('/', isAuth, EvaluationController.getEvaluations)

router.get('/get-by-id/:id', isAuth, EvaluationController.getEvaluationById)

router.put('/update-evaluation', isAuth, EvaluationController.updateEvaluation)

router.post('/evaluation-rejected', isAuth, EvaluationController.rejectedEvaluation)

router.get(
  '/evaluation-products/:evaluationId',
  isAuth,
  EvaluationController.getProductsOfEvaluation
)

router.put('/update-items-of-stock/:evaluationId', isAuth, EvaluationController.updateItemsOfStock)

module.exports = router
