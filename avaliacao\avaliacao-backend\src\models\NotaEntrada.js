const { model, Schema } = require('mongoose')

const NotaEntradaSchema = Schema({
  infoNota: {
    tipoEntrada: { type: String, required: true },
    serie: { type: String, required: true },
    numero: { type: Number, required: true },
    dataEntrada: { type: Date, required: true },
    cnpjEmpresa: { type: String, required: true },
    naturezaDaOperacao: { type: String, required: true },
    dataNota: { type: Date, required: true },
    dataDeSaidaDeEntrada: { type: Date, required: true },
    finalidade: { type: Number, required: true },
    indicarPresenca: { type: Number, required: true },
    usuario: { type: String, required: false },
  },
  infoEmit: {
    nomeEmitente: { type: String, required: true },
    nomeFantasia: { type: String, required: true },
    cnpjEmitente: { type: String, required: true },
    foneEmitente: { type: String, required: true },
    inscricaoEstadual: { type: Number, required: true },
    codigoRegimeTributario: { type: Number, required: true },
    cepEmitente: { type: String, required: true },
    estadoEmitente: { type: String, required: true },
    municipioEmitente: { type: Number, required: true },
    bairroEmitente: { type: String, required: true },
    //enderecoEmitente agora Ã© complementoEmitente e required: false **********************
    complementoEmitente: { type: String, required: false },
    numeroEmitente: { type: String, required: true },
    logradouroEmitente: { type: String, required: false },
    paisEmitente: { type: String, required: true },
  },

  impostoTotal: {
    baseIcmsImpostoTotal: { type: Number, required: true },
    icmsImpostoTotal: { type: Number, required: true },
    valorProdutoImpostoTotal: { type: Number, required: true },
    descontoProdutoImpostoTotal: { type: Number, required: true },
    confinsImpostoTotal: { type: Number, required: true },
    valorIPI: { type: Number, required: true },
    outroImpostoTotal: { type: Number, required: true },
    baseIcmsStImpostoTotal: { type: Number, required: true },
    valorTotalDaNotaFiscalImpostoTotal: { type: Number, required: true },
    PisImpostoTotal: { type: Number, required: true },
  },
  infoTranspor: {
    nomeTransportadora: { type: String, required: false },
    inscricaoEstadualTransportadora: { type: String, required: false },
    enderecoTransportadora: { type: String, required: false },
    cnpjTransportadora: { type: String, required: false },
    ufTransportadora: { type: String, required: false },
    MunicipioTransportadora: { type: String },
    volumeProdutos: { type: String, required: false },
    especieProdutos: { type: String, required: false },
    pesoBrutoProdutos: { type: Number, required: false },
    pesoLiquidoProdutos: { type: Number, required: false },
  },
  produtosArray: [
    {
      cfop: { type: Number, required: true },
      ncmProduto: { type: Number, required: true },
      // cEanProduto Required precisa ser false ******************************************************************
      cEanProduto: { type: Number, required: false },
      naturezaDeCompra: { type: String, required: true },
      produtoInternoDescricao: { type: String, required: false },
      cEanTributo: { type: Number, required: true },
      codigoProduto: { type: String, required: true },
      indTotProduto: { type: Number, required: true },
      qCom: { type: Number, required: true },
      qTrib: { type: Number, required: true },
      uCom: { type: String, required: true },
      uTrib: { type: String, required: true },
      vProd: { type: Number, required: true },
      vUnCom: { type: Number, required: true },
      vUnTrib: { type: Number, required: true },
      xPed: { type: Number, required: false },
      xProd: { type: String, required: true },
      codigoProdutoInterno: { type: String, required: false },
      imposto: {
        ICMS: {},
        IPI: {},
        PIS: {},
        COFINS: {},
      },
    },
  ],
  createAt: { type: Date, required: true },
  deleted: { type: Boolean, default: false },
  deleted_at: { type: Date, default: null },
})

const NotaEntradaModel = model('NotaEntrada', NotaEntradaSchema)

module.exports = NotaEntradaModel
