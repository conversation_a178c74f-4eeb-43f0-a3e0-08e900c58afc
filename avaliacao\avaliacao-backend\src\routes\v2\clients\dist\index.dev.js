"use strict";

var _require = require('express'),
    Router = _require.Router;

var router = Router();

var isAuth = require('../../../middlewares/isAuth');

var isTotemAuth = require('../../../middlewares/isTotemAuth');

var ClientsClass = require('../../../controllers/clients');

var ClientsController = new ClientsClass();
router.post('/', isAuth, ClientsController.createClient);
router.put('/', isAuth, ClientsController.updateClient);
router.post('/findClientByCpf', isAuth, ClientsController.getClientsByCpf);
router.post('/findByCpf', isTotemAuth, ClientsController.getTotemClientsByCpf);
router.get('/history', isAuth, ClientsController.getClientHistory);
router.get('/totem', isAuth, ClientsController.getAllClientsTotem);
router.get('/', isAuth, ClientsController.getAllClients);
module.exports = router;