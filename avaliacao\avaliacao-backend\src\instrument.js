const Sentry = require('@sentry/aws-serverless')
const { nodeProfilingIntegration } = require('@sentry/profiling-node')

const isProduction = ['beta', 'production', 'test', 'homolog'].includes(process.env.NODE_ENV)

if (isProduction) {
  Sentry.init({
    dsn: 'https://<EMAIL>/4508027085389824',
    integrations: [nodeProfilingIntegration()],
    tracesSampleRate: 1.0,
    profilesSampleRate: 1.0,
    environment: process.env.NODE_ENV,
  })
}
