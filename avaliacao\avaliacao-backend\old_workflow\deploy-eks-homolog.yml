name: Deploy to EKS - homolog

on:
  push:
    branches: [homolog]

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: homolog

    env:
      NAMESPACE: homolog
      CLUSTER_NAME: cluster-homologacao
      APPLICATION_NAME: avaliacao-backend
      ECR_REPOSITORY: dfcom-avaliacao-homolog
      DOCKERFILE: Dockerfile.eks

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID_EKS }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_EKS }}
          aws-region: ${{ secrets.AWS_REGION }}

      - name: Set up Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'

      - name: Install SST Ion
        run: curl -fsSL https://ion.sst.dev/install | bash

      - name: Install dependencies
        run: npm install --legacy-peer-deps

      - name: Install SST Providers
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: ${{ secrets.AWS_REGION }}
        run: npx sst install

      # - name: Build project
      #   run: npm run build

      # - name: Deploy with SST Ion
      #   env:
      #     AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      #     AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      #     AWS_REGION: ${{ secrets.AWS_REGION }}
      #   run: sst deploy --stage=homolog

      # - name: Login to Amazon ECR
      #   id: login-ecr
      #   uses: aws-actions/amazon-ecr-login@v1

      # - name: Build, tag and push image to Amazon ECR
      #   id: build-image
      #   env:
      #     ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
      #     IMAGE_TAG: ${{ github.sha }}
      #   run: |
      #     docker build -t $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG -f $DOCKERFILE .
      #     docker push $ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG
      #     echo "::set-output name=image::$ECR_REGISTRY/$ECR_REPOSITORY:$IMAGE_TAG"

      # - name: Setup kubectl
      #   uses: azure/k8s-set-context@v3
      #   with:
      #     method: kubeconfig
      #     kubeconfig: ${{ secrets.KUBECONFIG }}
      #     context: ${{ env.CLUSTER_NAME }}

      # - name: Install and configure Helm
      #   uses: azure/setup-helm@v3
      #   with:
      #     version: v3.11.1

      # - name: Deploy to EKS
      #   run: |
      #     helm upgrade --install $APPLICATION_NAME ./helm -f ./helm/$NAMESPACE-values.yaml --namespace $NAMESPACE --set image.repository=${{ steps.build-image.outputs.image }}

      - name: Discord notification
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
        uses: Ilshidur/action-discord@master
        with:
          args: 'Deploy efetuado (avaliacao-backend / homolog)'
