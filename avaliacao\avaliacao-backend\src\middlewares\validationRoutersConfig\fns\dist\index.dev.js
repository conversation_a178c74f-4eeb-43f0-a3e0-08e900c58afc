'use strict'

var EvaluationModel = require('../../../models/Avaliacao')

var deleteEvaluationInProgress = function deleteEvaluationInProgress(req) {
  var _req$body, cnpj, idCaixa, _req$body$id, id

  return regeneratorRuntime.async(function deleteEvaluationInProgress$(_context) {
    while (1) {
      switch ((_context.prev = _context.next)) {
        case 0:
          ;(_req$body = req.body),
            (cnpj = _req$body.cnpj),
            (idCaixa = _req$body.idCaixa),
            (_req$body$id = _req$body.id),
            (id = _req$body$id === void 0 ? null : _req$body$id)
          _context.next = 3
          return regeneratorRuntime.awrap(
            EvaluationModel.updateOne(
              {
                $or: [
                  {
                    cnpj: cnpj,
                    idCaixa: idCaixa,
                  },
                  {
                    _id: id,
                  },
                ],
              },
              {
                finalizado: false,
              }
            )
          )

        case 3:
        case 'end':
          return _context.stop()
      }
    }
  })
}

module.exports = deleteEvaluationInProgress
