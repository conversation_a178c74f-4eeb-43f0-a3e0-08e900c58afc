import moment from 'moment';
import { IPrintReinforcementReceipt } from '../../types';
import './styles.css'

interface ReinforcementReceiptProps {
  data: IPrintReinforcementReceipt
}

const ReinforcementReceipt: React.FC<ReinforcementReceiptProps> = ({ data: {
  value
}}) => {
  return (
    <div
      style={{ display: 'block' }}
    >
      <table>
        <tbody style={{ borderBottom: '2px solid #000' }}>
          <tr style={{ border: 'none' }}>
            <th colSpan={2}>Comprovante de Reforço - Avaliação</th>
          </tr>

          <tr style={{ border: 'none' }}>
            <td colSpan={2}>
              {moment.utc().subtract({ hours: 3 }).format('DD/MM/YYYY HH:mm:ss')}
            </td>
          </tr>

          <tr style={{ border: 'none' }}>
            <td colSpan={2}></td>
          </tr>

          <tr style={{ border: 'none' }}>
            <td>Valor da Reforço</td>
            <td>
              {Intl.NumberFormat('pt-BR', {
                currency: 'BRL',
                style: 'currency'
              }).format(Number(value))}
            </td>
          </tr>

          <tr style={{ border: 'none' }}>
            <td colSpan={2}></td>
          </tr>

          <tr style={{ borderTop: '2px solid #000' }}>
            <td>Assinatura: </td>
            <td>&nbsp;</td>
          </tr>
        </tbody>
      </table>
      <p>.</p>
    </div>
  )
}

export default ReinforcementReceipt;
