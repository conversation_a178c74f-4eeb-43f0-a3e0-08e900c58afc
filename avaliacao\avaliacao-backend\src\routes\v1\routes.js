const { Router } = require('express')
const router = Router()

const CodSituacaoOperacaoModel = require('../../models/CodSituacaoOperacao')
const SituacaoTributacaoModel = require('../../models/SituacaoTributariaICMS')
const OrigemMercadoriaModel = require('../../models/OrigemMercadoria')
const SituacaoTributariaIpiModel = require('../../models/SituacaoTributariaIPI')
const SituacaoTributariaPisModel = require('../../models/SituacaoTributariaPIS')
const CodRegimeTributarioModel = require('../../models/CodRegimeTributario')
const BlackListModel = require('../../models/BlackList')
const Nivel1 = require('../../models/Nivel1')
const Nivel2 = require('../../models/Nivel2')
const Nivel3 = require('../../models/Nivel3')
const Nivel4 = require('../../models/Nivel4')
const Avaliacao = require('../../models/Avaliacao')
const Usuario = require('../../models/Usuario')
const isAuth = require('../../middlewares/isAuth')
const { SECRET_JWT, expiresIn } = require('../../config/jwt')

const bcrypt = require('bcryptjs')
const randomstring = require('randomstring')
const moment = require('moment-timezone')
const jwt = require('jsonwebtoken')
const Cliente = require('../../models/Cliente')
const Vale = require('../../models/Vale')
const Contador = require('../../models/Contador')
const Config = require('../../models/Config')
const Pre_Avaliacao = require('../../models/Pre_Avaliacao')
const Caixa = require('../../models/Caixa')
const Produto = require('../../models/Produto')
const Empresa = require('../../models/Empresa')
const HttpException = require('../../exception/HttpException')

const axios = require('axios')
const mongoose = require('mongoose')
const utils = require('../../utils')
const qs = require('qs')
const FinalidadeNotaModel = require('../../models/FinalidadeNota')
const IndicadorPresencaModel = require('../../models/IndicadorPresenca')
const ObjectId = id => new mongoose.Types.ObjectId(id)
const tecnoSpeedPort = process.env.TECNOSPEED_PORT || '7071'
const amb = process.env.PRODUCTION === 'true' ? '' : 'hom'
const tecnoSpeedBaseUrl = process.env.TECNOSPEED_BASE_URL
const EvaluationExtensions = require('../../controllers/products/extensions')
const inventory = require('../../modules/inventory/services')
const { deleteTx2File } = require('../../helpers/deleteTx2File')
const { uploadTx2File } = require('../../helpers/uploadTx2File')
const { getLatLongByAddress } = require('../../helpers/getLatLongByAddress')

const invoiceNotesRouters = require('./nota-entrada')
const PrintPipelineRouters = require('./print-pipeline')
const PrinterOriginsRouters = require('./print-origins')
const EvaluationsRouters = require('./evaluation')
const LevelsRouters = require('./levels')
const PreEvaluationRouters = require('./pre-evaluations')
const ProductsRouters = require('./products')
const ProductsV2Routers = require('../v2/products')
const AddressRouters = require('./address')
const EnterprisesV2Routers = require('../v2/enterprises')
const UsersV2Routers = require('../v2/users')
const MessageV2Routers = require('../v2/messages')
const ClientsV2Routers = require('../v2/clients')
const ReviewsModel = require('../../models/Reviews')
const { default: Axios } = require('axios')
const { decrypt, encrypt } = require('../../utils/crypto')
const { generatecNF_B03, generateNFeTx2, sendNFe } = require('../../utils/tx2-helper')

router.use('/invoice-notes', invoiceNotesRouters)
router.use('/printer-pipeline', PrintPipelineRouters)
router.use('/printer-origins', PrinterOriginsRouters)
router.use('/evaluations', EvaluationsRouters)
router.use('/product-levels', LevelsRouters)
router.use('/pre-evaluations', PreEvaluationRouters)
router.use('/products', ProductsRouters)
router.use('/products', ProductsV2Routers)
router.use('/address', AddressRouters)
router.use('/enterprises', EnterprisesV2Routers)
router.use('/users', UsersV2Routers)
router.use('/messages', MessageV2Routers)
router.use('/clients', ClientsV2Routers)
// Routes

router.get('/getEmpresaByCnpj/:cnpj', async (req, res) => {
  try {
    const { cnpj } = req.params

    const _empresa = await Empresa.findOne({ cnpj: cnpj })

    if (!_empresa)
      return res.status(400).json({
        status: false,
        data: 'Empresa nao localizada',
      })

    res.status(200).json({
      status: true,
      empresa: _empresa,
    })
  } catch (err) {
    console.log('!! /gerarValePresentes !! \n Error' + err)
    recordingErrors(
      req,
      'Erro ao gerar os Vale Presentes',
      { type: 'gerarValePresentes', interface: '/gerarValePresentes' },
      'Error' + err
    )
    return res.status(400).json({
      status: false,
      data: 'Bad Resquest',
    })
  }
})

// router.get('/checkInternet', async function (req, res, next) {
//   var url = 'https://www.google.com'

//   try {
//     var resp = await axios({
//       method: 'GET',
//       url: url,
//     })

//     res.status(200).json({
//       status: true,
//       message: '',
//     })
//   } catch (e) {
//     console.log('====checkInternet erro:' + e)

//     res.status(200).json({
//       status: false,
//       message: 'não encontrado',
//     })
//   }
// })

//indicador de presença
router.get('/indicadores-presenca', async (req, res) => {
  const codigos = await IndicadorPresencaModel.find({}, { _id: 0, __v: 0 })

  if (!codigos.length)
    return res.status(200).json({
      status: true,
      data: 'Nenhum Codigo de finalidade da nota foi localizado',
    })

  res.status(200).json({
    status: true,
    data: codigos,
  })
})

//Codigo de finalidade da nota
router.get('/finalidade-nota', async (req, res) => {
  const codigos = await FinalidadeNotaModel.find({}, { _id: 0, __v: 0 })

  if (!codigos.length)
    return res.status(200).json({
      status: true,
      data: 'Nenhum Codigo de finalidade da nota foi localizado',
    })

  res.status(200).json({
    status: true,
    data: codigos,
  })
})

//Codigo do regime tributario
router.get('/codigo-regime-tributario', async (req, res) => {
  const codigos = await CodRegimeTributarioModel.find({}, { _id: 0, __v: 0 })

  if (!codigos.length)
    return res.status(200).json({
      status: true,
      data: 'Nenhum Codigo do regime tributario foi localizado',
    })

  res.status(200).json({
    status: true,
    data: codigos,
  })
})

//Situação tributária do PIS
router.get('/situacao-tributaria-pis', async (req, res) => {
  const situacoes = await SituacaoTributariaPisModel.find({}, { _id: 0, __v: 0 })

  if (!situacoes.length)
    return res.status(200).json({
      status: true,
      data: 'Nenhuma origem da mercadoria foi localizado',
    })

  res.status(200).json({
    status: true,
    data: situacoes,
  })
})

//Situação tributária do IPI
router.get('/situacao-tributaria-ipi', async (req, res) => {
  const situacoes = await SituacaoTributariaIpiModel.find({}, { _id: 0, __v: 0 })

  if (!situacoes.length)
    return res.status(200).json({
      status: true,
      data: 'Nenhuma origem da mercadoria foi localizado',
    })

  res.status(200).json({
    status: true,
    data: situacoes,
  })
})

//Origem da Mercadoria
router.get('/origens-mercadoria', async (req, res) => {
  const origens = await OrigemMercadoriaModel.find({}, { _id: 0, __v: 0 })

  if (!origens.length)
    return res.status(200).json({
      status: true,
      data: 'Nenhuma origem da mercadoria foi localizado',
    })

  res.status(200).json({
    status: true,
    data: origens,
  })
})

//Situação tributária do ICMS,
router.get('/situacao-tributaria-icms', async (req, res) => {
  const situacoes = await SituacaoTributacaoModel.find({}, { _id: 0, __v: 0 })

  if (!situacoes.length)
    return res.status(200).json({
      status: true,
      data: 'Nenhuma Situação tributária do ICMS foi localizada',
    })

  res.status(200).json({
    status: true,
    data: situacoes,
  })
})

//Código de Situação da Operação – Simples Nacional (CSOSN), onde CRT = 1
router.get('/csosn', async (req, res) => {
  const codSituacaoOperacao = await CodSituacaoOperacaoModel.find({}, { _id: 0, __v: 0 })

  if (!codSituacaoOperacao.length)
    return res.status(200).json({
      status: true,
      data: 'Nenhum Código de Situação da Operação foi localizado.',
    })

  res.status(200).json({
    status: true,
    data: codSituacaoOperacao,
  })
})

router.post('/itens-estoque', isAuth, async (req, res) => {
  const { idAvaliacao, itens } = req.body

  try {
    const avaliacao = await Avaliacao.findOneAndUpdate(
      { _id: idAvaliacao },
      { $push: { itensEstoque: itens } },
      { upsert: true, new: true }
    )

    return res.status(200).json(avaliacao)
  } catch (error) {
    return res.status(400).json({
      error: `Falha ao atualizar itens de estoque: ${error}`,
    })
  }
})

router.put('/update-itens-estoque', async (req, res) => {
  const { idAvaliacao, newItem } = req.body
  try {
    let avaliationToUpdate = await Avaliacao.findById(idAvaliacao)

    const itenAlreadyPrecify = avaliationToUpdate.itensEstoque
      .filter(item => item.descricao === newItem.descricao && item.codBarras === newItem.codBarras)
      .shift()

    if (!itenAlreadyPrecify) {
      avaliationToUpdate.itensEstoque.filter(item => {
        if (item.description === newItem.description && !item.precificado) {
          if (item.quantidade === 1) {
            item.codBarras = newItem.codBarras
            item.precificado = true
            item.valorDef = newItem.valorDef
          } else {
            avaliationToUpdate.itensEstoque.push(newItem)
            item.quantidade = item.quantidade - 1
          }
        }
      })
    } else {
      const alreadyExists = avaliationToUpdate.itensEstoque.some(
        item =>
          item.description === newItem.description &&
          item.codBarras === newItem.codBarras &&
          item.precificado
      )
      if (alreadyExists) {
        avaliationToUpdate.itensEstoque.filter(item => {
          if (
            item.description === newItem.description &&
            item.codBarras === newItem.codBarras &&
            item.precificado
          ) {
            item.quantidade = item.quantidade + 1
            avaliationToUpdate.itensEstoque.filter(itemNotPrecify => {
              if (
                itemNotPrecify.description === newItem.description &&
                !itemNotPrecify.precificado &&
                itemNotPrecify.quantidade > 0
              ) {
                itemNotPrecify.quantidade = itemNotPrecify.quantidade - 1
              }
            })
          }
        })
      } else {
        avaliationToUpdate.itensEstoque.push(newItem)
        avaliationToUpdate.itensEstoque.filter(product => {
          if (product.description === newItem.description && !product.precificado) {
            product.quantidade = product.quantidade - 1
          }
        })
      }
    }

    avaliationToUpdate.itensEstoque.filter(item => item.quantidade > 0)

    const avaliationUpdated = await Avaliacao.findByIdAndUpdate(idAvaliacao, avaliationToUpdate, {
      upsert: true,
      new: true,
    })

    res.status(200).json(avaliationUpdated)
  } catch (error) {
    console.log(error)
    return res.status(400).json({
      error: `Failed to update item: ${error}`,
    })
  }
})

router.get('/busca', isAuth, async (req, res) => {
  const filter = {}

  if (req?.query?.all === 'false') {
    filter.$or = [
      { create_at: { $gte: moment().startOf('day').toDate() }, deleted: false },
      { update_at: { $gte: moment().startOf('day').toDate() }, deleted: false },
    ]
  } else {
    filter.$or = [{ deleted: { $exists: false } }, { deleted: false }]
  }

  const level1 = await Nivel1.find(filter).lean()
  const level2 = await Nivel2.find(filter).lean()
  const level3 = await Nivel3.find(filter).lean()
  const level4 = await Nivel4.find(filter).lean()

  return res.json([level1 || [], level2 || [], level3 || [], level4 || []])
})

router.get('/busca_avaliacoes', isAuth, async (req, res) => {
  let filter = req.query
  const { page, size } = req.query

  if (filter.startDate) {
    filter['dataFinal'] = {
      $gte: new Date(new Date(filter.startDate)),
    }
    delete filter.startDate
  }

  if (filter.finalDate) {
    const date = new Date(filter.finalDate)
    date.setDate(date.getDate() + 1)

    filter['dataFinal'] = {
      ...filter['dataFinal'],
      $lte: date,
    }
    delete filter.finalDate
  }

  if (filter?.cpf) {
    filter['cpf'] = {
      $regex: filter?.cpf,
    }
  }

  if (filter?.cliente) {
    filter['cliente'] = {
      $regex: filter?.cliente,
      $options: 'i',
    }
  }

  delete filter.page
  delete filter.size

  try {
    let evaluations = await Avaliacao.find(filter)
      .limit(Number(size))
      .skip((page - 1) * size)
      .sort({ dataFinal: -1 })
      .lean()

    // if (filter.cliente) {
    //   evaluations = evaluations?.filter((evaluation) =>
    //     String(evaluation?.cliente)?.toLowerCase()?.includes(filter?.cliente?.toLowerCase())
    //   )
    // }

    // if (filter.cpf) {
    //   evaluations = evaluations?.filter((evaluation) =>
    //     String(evaluation?.cpf)?.toLowerCase()?.includes(filter?.cpf?.toLowerCase())
    //   )
    // }

    const evaluationsWithReviews = await Promise.all(
      evaluations.map(async evaluation => {
        const review = await ReviewsModel.findOne(
          { referenceId: evaluation?._id },
          { evaluator: 1, observation: 1, points: 1 }
        )

        if (review) {
          return {
            ...evaluation,
            evaluatorName: review?.evaluator,
            evaluationPoints: review?.points,
            evaluationObservation: review?.observation,
          }
        }

        return evaluation
      })
    )

    const totalEvaluation = await Avaliacao.countDocuments(filter)

    return res.status(200).json({
      data: encrypt(
        {
          page: Number(page),
          size: evaluations.length,
          totalEvaluation: totalEvaluation,
          avaliacoes: evaluationsWithReviews,
        },
        process.env.PRIVATE_KEY
      ).toString(),
    })
  } catch (error) {
    console.log(error)
  }
})

router.post('/busca_avaliacoes', isAuth, (req, res) => {
  const cnpj = req.body.cnpj
  Avaliacao.find({ cnpj: cnpj })
    .then(data => {
      res.json(data)
    })
    .catch(error => {
      console.log('error: ', error)
    })
})

router.post('/busca_avaliacao/cliente', isAuth, async (req, res) => {
  const { cpf } = req.body || {}

  const findAvaliacao = await Avaliacao.aggregate([
    {
      $match: {
        cpf,
        finalizado: true,
      },
    },
    {
      $sort: {
        dataFinal: -1,
      },
    },
    {
      $limit: 3,
    },
    {
      $lookup: {
        as: 'empresa',
        from: 'empresas',
        localField: 'cnpj',
        foreignField: 'cnpj',
      },
    },
    {
      $project: {
        nomeUnidade: '$empresa.nomeUnidade',
        dataFinal: 1,
        status: 1,
        motivo: 1,
        cancelado: 1,
        reject_at: 1,
      },
    },
  ])

  return res.json(findAvaliacao)
})

router.get('/avaliacao_aberta', isAuth, (req, res) => {
  const { cnpj, idUsuarioAvaliacao } = req.query

  Avaliacao.findOne({
    finalizado: false,
    cnpj: cnpj,
    idUsuarioAvaliacao: idUsuarioAvaliacao,
  })
    .then(data => {
      res.json(data)
    })
    .catch(error => {
      console.log('error: ', error)
    })
})

router.post('/pre_avaliacao_aberta', isAuth, (req, res) => {
  const cnpj = req.body.cnpj
  Pre_Avaliacao.findOne({ finalizado: false, cnpj: cnpj })
    .then(data => {
      res.json(data)
    })
    .catch(error => {
      console.log('error: ', error)
    })
})

async function geraCod() {
  let contador, codigo
  contador = await Produto.countDocuments()
  codigo = contador++
  // gera o codigo de barras essa parte
  let codigoBarra = 123000000000
  let um,
    dois,
    tres,
    quatro,
    cinco,
    seis,
    sete,
    oito,
    nove,
    dez,
    onze,
    doze,
    soma,
    multiplo,
    resultado
  codigoBarra += Number(codigo)
  codigoBarra = codigoBarra.toString()
  um = Number(codigoBarra.substring(0, 1))
  dois = Number(codigoBarra.substring(1, 2))
  tres = Number(codigoBarra.substring(2, 3))
  quatro = Number(codigoBarra.substring(3, 4))
  cinco = Number(codigoBarra.substring(4, 5))
  seis = Number(codigoBarra.substring(5, 6))
  sete = Number(codigoBarra.substring(6, 7))
  oito = Number(codigoBarra.substring(7, 8))
  nove = Number(codigoBarra.substring(8, 9))
  dez = Number(codigoBarra.substring(9, 10))
  onze = Number(codigoBarra.substring(10, 11))
  doze = Number(codigoBarra.substring(11, 12))
  soma =
    um * 1 +
    dois * 3 +
    tres * 1 +
    quatro * 3 +
    cinco * 1 +
    seis * 3 +
    sete * 1 +
    oito * 3 +
    nove * 1 +
    dez * 3 +
    onze * 1 +
    doze * 3
  soma = soma.toString()
  if (soma.slice(1) === '0') {
    codigoBarra += '0'
  } else {
    multiplo = Number(soma.slice(0, -1))
    multiplo = multiplo * 10 + 10
    resultado = multiplo - soma
    codigoBarra += resultado
  }
  return codigoBarra
}

router.post('/busca_amarrados', isAuth, async (req, res) => {
  const { nvl, id: chave } = req.body || {}

  if (!chave) {
    return res.status(400).json({
      status: false,
      error: 'Id/Chave Inválida!',
    })
  }

  const niveisOpts = {
    nivel1: Nivel1,
    nivel2: Nivel2,
    nivel3: Nivel3,
    nivel4: Nivel4,
  }

  const acceptedNiveis = Object.keys(niveisOpts)

  if (!acceptedNiveis.includes(nvl) || !niveisOpts?.[nvl]) {
    return res.status(400).json({
      status: false,
      error: 'Nível não encontrado!',
    })
  }

  try {
    const findInfosByNivel = await niveisOpts[nvl].aggregate([
      {
        $match: {
          chave,
          $or: [{ deleted: { $exists: false } }, { deleted: false }],
        },
      },
      {
        $set: {
          _id: {
            $toString: '$_id',
          },
        },
      },
      {
        $lookup: {
          from: 'produtos',
          localField: '_id',
          foreignField: 'nivel3',
          as: 'produto',
        },
      },
    ])

    const formatProductNivel = findInfosByNivel.map(info => {
      const produto = info['produto'] || {}
      const produtoAtivo = produto?.filter(prod => prod.ativo)?.[0] || {}

      if (produtoAtivo?.ativo) {
        info['valor_antigo'] = info['valor'] || 0
        info['valor'] = produtoAtivo?.['vlrCusto'] || info['valor'] || 0
      }

      delete info['produto']

      return info
    })

    res.status(200).json(formatProductNivel)
  } catch (error) {
    res.status(400).json({
      status: false,
      error,
    })
  }
})

router.post('/save_avaliacao', isAuth, (req, res) => {
  try {
    const {
      motivo,
      usuario,
      cnpj,
      dataInicio,
      // dataFinal,
      totalValePadrao,
      totalValeEfetivado,
      totalDinheiroPadrao,
      totalDinheiroEfetivado,
      oferta,
      tipo,
      cpf,
      cliente,
      idPreAvaliacao,
      idUsuarioAvaliacao,
      caixa,
    } = req.body

    if (!cnpj)
      return res.status(400).json({
        status: false,
        errorMenssage: 'Informe o CNPJ corretamente.',
      })

    const avaliacao = {
      items: [],
      motivo: motivo,
      usuario: usuario,
      cnpj: cnpj,
      dataInicio,
      dataFinal: null,
      totalValePadrao: totalValePadrao,
      totalValeEfetivado: totalValeEfetivado,
      totalDinheiroPadrao: totalDinheiroPadrao,
      totalDinheiroEfetivado: totalDinheiroEfetivado,
      oferta: oferta,
      tipo: tipo,
      finalizado: false,
      cancelado: false,
      cpf: cpf,
      cliente: cliente,
      idPreAvaliacao: idPreAvaliacao,
      caixa: caixa,
      idUsuarioAvaliacao: idUsuarioAvaliacao,
    }
    new Avaliacao(avaliacao)
      .save()
      .then(obj => res.json(obj))
      .catch(erro => res.status(400).json(erro))
  } catch (error) {
    console.log(error)
    res.status(400).json({
      status: false,
      error: error,
    })
  }
})

router.patch('/cancela_avaliacao', isAuth, async (req, res) => {
  const evaluation = await Avaliacao.findOneAndUpdate(
    { _id: req.body.idAvaliacao },
    {
      items: req.body.items,
      motivo: req.body.motivo,
      dataFinal: req.body.dataFinal,
      totalDinheiroPadrao: req.body.totalDinheiroPadrao,
      totalDinheiroEfetivado: req.body.totalDinheiroEfetivado,
      totalValePadrao: req.body.totalValePadrao,
      totalValeEfetivado: req.body.totalValeEfetivado,
      tipo: req.body.tipo,
      codBarras: req.body.codBarras,
      finalizado: true,
      cancelado: true,
      oferta: req.body.oferta,
      caixa: req.body.idCaixa,
      rejected: false,
      canceledAt: req.body.canceledAt || null,
    }
  )

  inventory.removeProduct({
    items: req.body.items,
    cnpj: req.body.cnpj,
  })

  // const payload = new Payload(RoutingKeys.EVALUATION_CANCELED, evaluation)
  // Broker.publish(payload)

  res.json(evaluation)
})

router.get('/get_avaliacao', (req, res) => {
  const avaliacao = process.env
  res.status(200).json({
    avaliacao,
  })
})

router.post('/buscar_por_nvs', isAuth, async (req, res) => {
  const { produtos } = decrypt(req.body?.data, process.env.PRIVATE_KEY)
  let prodEncontrado
  let prodGen
  let qtd
  let precoBruto
  let descPesquisa = null
  let ePesquisa = false
  let excluido = false

  const produtosEncontrados = []
  // percorre os produtos
  for (let i = 0; i < produtos.length; i++) {
    descPesquisa = null
    ePesquisa = false
    // vai verficar quantos niveis tem e buscar o produto
    if (produtos[i].fechamento.length === 1) {
      prodEncontrado = await Produto.findOne({
        nivel1: produtos[i].fechamento[0].item._id,
        ativo: true,
      })
      qtd = produtos[i].fechamento[0].qtd
      if (prodEncontrado && (prodEncontrado.pesquisa || prodEncontrado.pesquisa == 'sim')) {
        precoBruto = Number(produtos[i].fechamento[0].item.valor)
        descPesquisa = produtos[i].fechamento[0].item.descPesquisa
        ePesquisa = true
      }
      if (!prodEncontrado) {
        const req = {
          nivel: 'nivel1',
          pesquisa: produtos[i].fechamento[0].item.pesquisa,
          valor: produtos[i].fechamento[0].item.valor,
          valorMax: produtos[i].fechamento[0].item.valorMax,
          coeficiente: produtos[i].fechamento[0].item.coeficiente,
          descricao: produtos[i].fechamento[0].item.descricao,
          peso: produtos[i].fechamento[0].item.peso,
        }
        prodGen = await EvaluationExtensions.geraProduto(req, produtos[i].fechamento[0].item)
        prodEncontrado = await new Produto(prodGen).save()
      }
    } else if (produtos[i].fechamento.length === 2) {
      prodEncontrado = await Produto.findOne({
        nivel1: produtos[i].fechamento[0].item._id,
        nivel2: produtos[i].fechamento[1].item._id,
        ativo: true,
      })
      qtd = produtos[i].fechamento[1].qtd
      if (prodEncontrado && (prodEncontrado.pesquisa || prodEncontrado.pesquisa == 'sim')) {
        precoBruto = Number(produtos[i].fechamento[1].item.valor)
        descPesquisa = produtos[i].fechamento[1].item.descPesquisa
        ePesquisa = true
      }
      if (!prodEncontrado) {
        const req = {
          nivel: 'nivel2',
          pesquisa: produtos[i].fechamento[1].item.pesquisa,
          valor: produtos[i].fechamento[1].item.valor,
          valorMax: produtos[i].fechamento[1].item.valorMax,
          coeficiente: produtos[i].fechamento[1].item.coeficiente,
          descricao: produtos[i].fechamento[1].item.descricao,
          peso: produtos[i].fechamento[1].item.peso,
        }
        prodGen = await EvaluationExtensions.geraProduto(req, produtos[i].fechamento[1].item)
        prodEncontrado = await new Produto(prodGen).save()
      }
    } else if (produtos[i].fechamento.length === 3) {
      prodEncontrado = await Produto.findOne({
        nivel1: produtos[i].fechamento[0].item._id,
        nivel2: produtos[i].fechamento[1].item._id,
        nivel3: produtos[i].fechamento[2].item._id,
        ativo: true,
      })
      qtd = produtos[i].fechamento[2].qtd
      if (prodEncontrado && (prodEncontrado.pesquisa || prodEncontrado.pesquisa == 'sim')) {
        precoBruto = Number(produtos[i].fechamento[2].item.valor)
        descPesquisa = produtos[i].fechamento[2].item.descPesquisa
        ePesquisa = true
      }
      if (!prodEncontrado) {
        const req = {
          nivel: 'nivel3',
          pesquisa: produtos[i].fechamento[2].item.pesquisa,
          valor: produtos[i].fechamento[2].item.valor,
          valorMax: produtos[i].fechamento[2].item.valorMax,
          coeficiente: produtos[i].fechamento[2].item.coeficiente,
          descricao: produtos[i].fechamento[2].item.descricao,
          peso: produtos[i].fechamento[2].item.peso,
        }
        prodGen = await EvaluationExtensions.geraProduto(req, produtos[i].fechamento[2].item)
        prodEncontrado = await new Produto(prodGen).save()
      }
    } else if (produtos[i].fechamento.length === 4) {
      prodEncontrado = await Produto.findOne({
        nivel1: produtos[i].fechamento[0].item._id,
        nivel2: produtos[i].fechamento[1].item._id,
        nivel3: produtos[i].fechamento[2].item._id,
        nivel4: produtos[i].fechamento[3].item._id,
        ativo: true,
      })
      qtd = produtos[i].fechamento[3].qtd

      if (prodEncontrado && (prodEncontrado.pesquisa || prodEncontrado.pesquisa == 'sim')) {
        precoBruto = Number(produtos[i].fechamento[3].item.valor)
        descPesquisa = produtos[i].fechamento[3].item.descPesquisa
        ePesquisa = true
      }

      if (!prodEncontrado) {
        const req = {
          nivel: 'nivel4',
          pesquisa: produtos[i].fechamento[3].item.pesquisa,
          valor: produtos[i].fechamento[3].item.valor,
          valorMax: produtos[i].fechamento[3].item.valorMax,
          coeficiente: produtos[i].fechamento[3].item.coeficiente,
          descricao: produtos[i].fechamento[3].item.descricao,
          peso: produtos[i].fechamento[3].item.peso,
        }
        prodGen = await EvaluationExtensions.geraProduto(req, produtos[i].fechamento[3].item)
        prodEncontrado = await new Produto(prodGen).save()
      }
    }
    excluido = qtd === 0 ? true : false

    const payload = {
      idProduto: prodEncontrado._id,
      qtd: qtd,
      excluido,
      descPesquisa,
      ePesquisa,
      vlrVenda: prodEncontrado?.vlrVenda,
      vlrCusto: prodEncontrado?.vlrCusto,
    }

    if (prodEncontrado.pesquisa) {
      payload.coeficiente = prodEncontrado.coeficiente
      payload.precoBruto = precoBruto
    }

    produtosEncontrados.push(payload)
  }
  return res.json({
    data: encrypt(produtosEncontrados, process.env.PRIVATE_KEY).toString(),
  })
})

// requisicao para finalizar a pre avaliacao
router.put('/update_pre_avaliacao', isAuth, async (req, res) => {
  const preEvaluationToUpdate = await Pre_Avaliacao?.findOne({ _id: req?.body?._id }).lean()
  const client = await Cliente.findOne({ _id: preEvaluationToUpdate?.idCliente }).lean()

  const clientInBlackList = await BlackListModel.findOne({
    cpf: client?.cpf,
    cnpj: { $in: [req?.usuario?.enterprise?.cnpj, '*'] },
    deletedAt: null,
  })

  if (clientInBlackList) {
    return res.status(400).json({
      status: false,
      error: 'Não é possivel prosseguir, cliente está na blacklist!',
    })
  }

  Pre_Avaliacao.findByIdAndUpdate(
    { _id: req.body._id },
    {
      finalizado: true,
    }
  )
    .then(() => res.json('pre avaliação atualizada!!!!!!!'))
    .catch(erro => res.json(erro))
})

router.patch('/update_avaliacao/cliente', isAuth, (req, res) => {
  if (req.body.data !== null || typeof req.body.data !== 'undefined') {
    Avaliacao.findOneAndUpdate(
      { cnpj: req.body.cnpj, finalizado: false },
      {
        cpf: req.body.cpf,
        cliente: req.body.nome,
        data: req.body.data,
      }
    )
      .then(() => res.json('avaliação atualizada!!!!!!!'))
      .catch(erro => res.json(erro))
  } else {
    Avaliacao.findOneAndUpdate(
      { cnpj: req.body.cnpj, finalizado: false },
      {
        cpf: req.body.cpf,
        cliente: req.body.nome,
      }
    )
      .then(() => res.json('avaliação atualizada!!!!!!!'))
      .catch(erro => res.json(erro))
  }
})

router.post('/registra_usuario', (req, res) => {
  const usuario = {
    usuario: req.body.usuario,
    senha: req.body.senha,
    tipo: req.body.tipo,
    unidades: req.body.unidades,
  }
  bcrypt.genSalt(10, (erro, salt) => {
    bcrypt.hash(usuario.senha, salt, (erro, hash) => {
      if (erro) {
        console.log(erro)
      } else {
        usuario.senha = hash
        new Usuario(usuario)
          .save()
          .then(obj => res.send(obj))
          .catch(erro => res.status(400).json(erro))
      }
    })
  })
})

router.get('/usuarios', isAuth, (req, res) => {
  Usuario.find()
    .then(data => {
      res.send(data)
    })
    .catch(err => console.log(err))
})

router.post('/busca_cpf', isAuth, (req, res) => {
  Cliente.findOne({ cpf: req.body.cpf })
    .then(data => res.send(data))
    .catch(err => console.log(err))
})

router.get('/busca_todos_clientes', isAuth, (req, res) => {
  Cliente.find({})
    .then(data => res.send(data))
    .catch(erro => console.log(erro))
})

router.post('/salva_cliente', isAuth, async (req, res) => {
  try {
    const cliente = {
      nome: req.body.nome,
      cpf: req.body.cpf,
      dtNascimento:
        req.body.dtNascimento === '0000-00-00' ? moment.utc().toDate() : req.body.dtNascimento,
      celular: req.body.celular,
      rua: req.body.rua,
      bairro: req.body.bairro,
      numero: req.body.numero,
      cep: req.body.cep,
      cod_cidade: req.body.cod_cidade,
      cod_uf: req.body.cod_uf,
      estado: req.body.estado,
      cidade: req.body.cidade,
      complemento: req.body.complemento,
      ...(req?.body?.childrens && {
        childrens: req?.body?.childrens,
      }),
    }

    const clientInBlackList = await BlackListModel.findOne({
      cpf: cliente?.cpf,
      cnpj: { $in: [req?.usuario?.enterprise?.cnpj, '*'] },
      deletedAt: null,
    })

    if (clientInBlackList) {
      return res.status(400).json({
        status: false,
        error: 'Não é possivel prosseguir, cliente está na blacklist!',
      })
    }

    const findClient = await Cliente.findOne({ cpf: cliente?.cpf }).lean()

    // Se for a mesma rua ou número ou o usuário não tiver geo, consulta e atualiza
    if (
      !findClient ||
      String(cliente.rua).trim() != String(findClient.rua).trim() ||
      Number(cliente.numero) !== Number(findClient.numero) ||
      !findClient?.['geo']?.['latitude']
    ) {
      const addressFormatted = [
        cliente.rua,
        cliente.numero,
        cliente.bairro,
        cliente.cidade,
        cliente.estado,
      ].join(', ')

      let geo

      try {
        geo = await getLatLongByAddress(addressFormatted)
      } catch (err) {
        geo = null
      }

      if (geo?.latitude) {
        cliente.geo = {
          latitude: geo.latitude,
          longitude: geo.longitude,
        }
      }
    }

    const response = await Cliente.create(cliente)
    // const response = await Cliente.findOneAndUpdate({ cpf: cliente?.cpf }, cliente, { new: true }).lean()
    res.json(response)
  } catch (erro) {
    console.log(erro)
    res.json(erro)
  }
})

router.put('/atualiza_cliente', isAuth, async (req, res) => {
  try {
    const {
      nome,
      cpf,
      dtNascimento,
      celular,
      rua,
      bairro,
      numero,
      cep,
      cod_uf,
      cod_cidade,
      estado,
      cidade,
      complemento,
      childrens,
    } = decrypt(req.body.data || {}, process.env.PRIVATE_KEY)

    if (!cpf || cpf.length < 4) {
      return res.json({
        status: false,
        message: 'Cliente não encontrado!',
      })
    }

    const findCliente = await Cliente.findOne({ cpf }).lean()

    if (!findCliente) {
      return res.json({
        status: false,
        message: 'Usuário não encontrado!',
      })
    }

    const clienteRequest = {
      nome,
      cpf,
      dtNascimento: dtNascimento === '0000-00-00' ? moment.utc().toDate() : dtNascimento,
      celular,
      rua,
      bairro,
      numero,
      cep,
      cod_uf,
      cod_cidade,
      estado,
      cidade,
      complemento,
      childrens,
    }

    // Se for a mesma rua ou número ou o usuário não tiver geo, consulta e atualiza
    if (
      String(clienteRequest.rua).trim() != String(findCliente.rua).trim() ||
      Number(clienteRequest.numero) !== Number(findCliente.numero) ||
      !findCliente?.['geo']?.['latitude']
    ) {
      const addressFormatted = [
        clienteRequest.rua,
        clienteRequest.numero,
        clienteRequest.bairro,
        clienteRequest.cidade,
        clienteRequest.estado,
      ].join(', ')

      const geo = await getLatLongByAddress(addressFormatted)

      if (geo?.latitude) {
        clienteRequest.geo = {
          latitude: geo.latitude,
          longitude: geo.longitude,
        }
      }
    }

    const update = await Cliente.findOneAndUpdate({ cpf }, clienteRequest, { new: true }).lean()
    res.status(200).json({
      data: encrypt(update, process.env.PRIVATE_KEY).toString(),
    })
  } catch (erro) {
    res.json(erro)
  }
})

router.post('/gera_vale', isAuth, async (req, res) => {
  const {
    cpf,
    idAvaliacao,
    vlrAberto,
    vlrTotal,
    codBarras,
    nomeCliente,
    cnpj,
    codCidade,
    data,
    dataExpiracao,
  } = req.body || {}

  const existValeInAvaliacao = await Vale.findOne({
    idAvaliacao,
  })

  if (existValeInAvaliacao) {
    return res.status(400).json({
      status: false,
      errorMenssage: 'Já existe um vale criado para esta avaliação!',
    })
  }

  const newVale = {
    cpf,
    idAvaliacao,
    vlrAberto,
    vlrTotal,
    codBarras,
    nomeCliente,
    cnpj,
    codCidade,
    data,
    dataExpiracao,
  }

  new Vale(newVale)
    .save()
    .then(obj => res.json(obj))
    .catch(erro => res.status(400).json(erro))
})

router.post('/busca_vale', isAuth, (req, res) => {
  if (req.body.cpf !== null)
    Vale.find({ cpf: req.body.cpf })
      .then(data => res.send(data))
      .catch(err => console.log(err))
  else
    Vale.find({ codBarras: req.body.codBarras })
      .then(data => res.send(data))
      .catch(err => console.log(err))
})

router.post('/deleta_vale', isAuth, (req, res) => {
  Vale.findOneAndDelete({ codBarras: req.body.codBarras })
    .then(data => res.send(data))
    .catch(err => console.log(err))
})

router.get('/busca_rodape', isAuth, (req, res) => {
  Config.find({ cod: 1 })
    .then(data => res.send(data))
    .catch(err => console.log(err))
})

router.post('/cadastra_pre_avaliacao', isAuth, (req, res) => {
  try {
    const { usuario, cnpj, data, finalizado, idCliente, volume } = req.body

    if (!cnpj)
      return res.status(400).json({
        status: false,
        errorMenssage: 'Informe o cnpj corretamente.',
      })

    const newPre = {
      usuario: usuario,
      cnpj: cnpj,
      data: data,
      finalizado: finalizado,
      idCliente: idCliente,
      volume: volume,
    }
    new Pre_Avaliacao(newPre)
      .save()
      .then(obj => res.json(obj))
      .catch(erro => res.status(400).json(erro))
  } catch (error) {
    console.log(error)
    res.status(400).json({
      status: false,
      error: error,
    })
  }
})

router.get('/busca_pre/:idPre', isAuth, async (req, res) => {
  try {
    const { idPre } = req.params

    const pre_avaliation = await Pre_Avaliacao.findById(idPre)

    if (pre_avaliation.finalizado === true) {
      throw new Error('Pré Avaliação já finalizada')
    }

    return res.status(200).json(pre_avaliation)
  } catch (error) {
    return res.status(400).json({
      error: `Something went wrong: ${error}`,
    })
  }
})

router.post('/busca_cliente', isAuth, (req, res) => {
  const { cpf, idCliente } = decrypt(req.body.data, process.env.PRIVATE_KEY)

  if (typeof idCliente !== 'undefined') {
    Cliente.findOne({ _id: idCliente })
      .then(data =>
        res.json({
          data: encrypt({ data }, process.env.PRIVATE_KEY).toString(),
        })
      )
      .catch(err => console.log(err))
  } else {
    Cliente.findOne({ cpf: cpf })
      .then(data =>
        res.json({
          data: encrypt({ data }, process.env.PRIVATE_KEY).toString(),
        })
      )
      .catch(err => console.log(err))
  }
})

router.post('/abrir_caixa', isAuth, (req, res) => {
  const dataAbertura = new Date()
  dataAbertura.setHours(dataAbertura.getHours() - 3)
  const newCaixa = {
    // unidade: req.body.unidade,
    cnpj: req.body.cnpj,
    dataAbertura: dataAbertura,
    vlrAbertura: req.body.vlrAbertura,
    idUsuario: req.body.idUsuario,
    sangria: 0,
    reforco: 0,
  }
  new Caixa(newCaixa)
    .save()
    .then(obj => res.json(obj))
    .catch(erro => res.status(400).json(erro))
})

router.post('/fechar_caixa', isAuth, (req, res) => {
  const data = new Date()
  data.setHours(data.getHours() - 3)
  Caixa.findOneAndUpdate(
    { _id: req.body.idCaixa },
    {
      condPagamento: req.body.condPagamento,
      dataFechamento: data,
    }
  )
    .then(caixa => res.json(caixa))
    .catch(erro => res.status(400).json(erro))
})

router.post('/sangria', isAuth, (req, res) => {
  Caixa.findById(req.body.id).then(caixa => {
    Caixa.findOneAndUpdate(
      { _id: req.body.id },
      { sangria: Number(req.body.sangria) + caixa.sangria }
    )
      .then(() => res.send({ message: 'Sangria realizada' }))
      .catch(erro => res.status(400).json(erro))
  })
})

router.post('/reforco', isAuth, (req, res) => {
  Caixa.findById(req.body.id).then(caixa => {
    Caixa.findOneAndUpdate(
      { _id: req.body.id },
      { reforco: Number(req.body.reforco) + caixa.reforco }
    )
      .then(() => res.send({ message: 'Reforço realizado' }))
      .catch(erro => res.status(400).json(erro))
  })
})

// rota para retornar as condiçoes de pagamento de dado caixa
router.post('/condPagamento_caixa', isAuth, (req, res) => {
  const { idCaixa } = req.body
  let somaDinheiro = 0
  let somaVale = 0
  // encontra vendas amarradas ao id do caixa
  Avaliacao.find({ caixa: idCaixa })
    .then(avaliacoes => {
      // percorre as avaliacoes
      avaliacoes.forEach(avaliacao => {
        if (!avaliacao.cancelado && avaliacao.finalizado)
          if (avaliacao.tipo === 'Dinheiro') {
            if (!isNaN(Number(avaliacao.totalDinheiroEfetivado)))
              somaDinheiro += Number(avaliacao.totalDinheiroEfetivado)
          } else if (avaliacao.tipo === 'Vale') {
            if (!isNaN(Number(avaliacao.totalValeEfetivado)))
              somaVale += Number(avaliacao.totalValeEfetivado)
          }
      })
      res.send({ dinheiro: somaDinheiro, vale: somaVale })
    })
    .catch(erro => res.status(400).json(erro))
})

router.post('/buscar_caixa', isAuth, (req, res) => {
  Caixa.findOne({ _id: req.body.idCaixa })
    .then(caixa => res.json(caixa))
    .catch(erro => res.status(400).json(erro))
})

// buscar caixa aberto de dado usuario
router.get('/buscar_caixa/idUsuario/:id', isAuth, (req, res) => {
  Caixa.findOne({ idUsuario: req.params.id, dataFechamento: undefined })
    .then(caixa => res.json(caixa))
    .catch(erro => res.status(400).json(erro))
})

router.post('/busca_itens', isAuth, async (req, res) => {
  let { idAvaliacao } = req.body || {}

  if (!idAvaliacao) {
    return res.status(400).json({
      status: false,
      errorMenssage: 'Esta avaliação não existe!',
    })
  }

  const _id = ObjectId(idAvaliacao)

  let listItems = []

  try {
    listItems = await Avaliacao.aggregate([
      { $match: { _id } },
      { $unwind: '$items' },
      {
        $addFields: {
          userObjectId: {
            $convert: {
              input: '$items.idProduto',
              to: 'objectId',
              onError: '',
              onNull: '',
            },
          },
        },
      },
      {
        $lookup: {
          localField: 'userObjectId',
          from: 'produtos',
          foreignField: '_id',
          as: 'prod_item',
        },
      },
      {
        $unwind: {
          path: '$prod_item',
        },
      },
      {
        $project: {
          id: 1,
          items: 1,
          prod_item: 1,
        },
      },
    ])
  } catch (e) {}

  return res.json(listItems)
})

router.post('/buscar_caixa_por_usuario', isAuth, async (req, res) => {
  const { idUsuario } = req.body
  const caixas = await Caixa.find({ idUsuario: idUsuario }).sort({ _id: -1 }).limit(30)
  return res.send(caixas)
})

// função para gerar os produtos pelos niveis cadastrados
router.get('/gera_produtos', isAuth, async (req, res) => {
  let contador, codigo, produto, vlrCusto, vlrVenda, coeficiente, pesquisa, nvl1, nvl2, nvl3, nvl4
  contador = await Contador.findOne({ id: 'produtoid' })
  codigo = contador.sequencia
  let um,
    dois,
    tres,
    quatro,
    cinco,
    seis,
    sete,
    oito,
    nove,
    dez,
    onze,
    doze,
    soma,
    multiplo,
    resultado

  // percorre o nivel1 e monta os produtos
  Nivel1.find().then(async res => {
    for (let i = 0; i < res.length; i++) {
      if (res[i].ultimoNvl === 'sim') {
        if (!res[i].pesquisa || res[i].pesquisa === 'não') {
          pesquisa = false
          coeficiente = null
          vlrCusto = res[i].valor
          vlrVenda = res[i].valorMax
        } else {
          pesquisa = true
          coeficiente = res[i].coeficiente
          vlrCusto = null
          vlrVenda = null
        }
        // gera o codigo de barras essa parte
        let codigoBarra = 123000000000
        codigoBarra += Number(codigo)
        codigoBarra = codigoBarra.toString()
        um = Number(codigoBarra.substring(0, 1))
        dois = Number(codigoBarra.substring(1, 2))
        tres = Number(codigoBarra.substring(2, 3))
        quatro = Number(codigoBarra.substring(3, 4))
        cinco = Number(codigoBarra.substring(4, 5))
        seis = Number(codigoBarra.substring(5, 6))
        sete = Number(codigoBarra.substring(6, 7))
        oito = Number(codigoBarra.substring(7, 8))
        nove = Number(codigoBarra.substring(8, 9))
        dez = Number(codigoBarra.substring(9, 10))
        onze = Number(codigoBarra.substring(10, 11))
        doze = Number(codigoBarra.substring(11, 12))
        soma =
          um * 1 +
          dois * 3 +
          tres * 1 +
          quatro * 3 +
          cinco * 1 +
          seis * 3 +
          sete * 1 +
          oito * 3 +
          nove * 1 +
          dez * 3 +
          onze * 1 +
          doze * 3
        soma = soma.toString()
        if (soma.slice(1) === '0') {
          codigoBarra += '0'
        } else {
          multiplo = Number(soma.slice(0, -1))
          multiplo = multiplo * 10 + 10
          resultado = multiplo - soma
          codigoBarra += resultado
        }
        produto = {
          descricao: res[i].descricao,
          nivel1: res[i]._id,
          vlrCusto: vlrCusto,
          vlrVenda: vlrVenda,
          codigo: codigo,
          codBarras: codigoBarra,
          pesquisa: pesquisa,
          coeficiente: coeficiente,
          favorito: false,
        }
        codigo++
        await new Produto(produto).save()
      }
    }

    // percorre o nivel2 montando os produtos
    Nivel2.find().then(async res => {
      for (let i = 0; i < res.length; i++) {
        if (res[i].ultimoNvl === 'sim') {
          nvl1 = await Nivel1.findById(res[i].chave)

          if (!res[i].pesquisa || res[i].pesquisa === 'não') {
            pesquisa = false
            coeficiente = null
            vlrCusto = res[i].valor
            vlrVenda = res[i].valorMax
          } else {
            pesquisa = true
            coeficiente = res[i].coeficiente
            vlrCusto = null
            vlrVenda = null
          }

          // gera o codigo de barras essa parte
          let codigoBarra = 123000000000
          codigoBarra += Number(codigo)
          codigoBarra = codigoBarra.toString()
          um = Number(codigoBarra.substring(0, 1))
          dois = Number(codigoBarra.substring(1, 2))
          tres = Number(codigoBarra.substring(2, 3))
          quatro = Number(codigoBarra.substring(3, 4))
          cinco = Number(codigoBarra.substring(4, 5))
          seis = Number(codigoBarra.substring(5, 6))
          sete = Number(codigoBarra.substring(6, 7))
          oito = Number(codigoBarra.substring(7, 8))
          nove = Number(codigoBarra.substring(8, 9))
          dez = Number(codigoBarra.substring(9, 10))
          onze = Number(codigoBarra.substring(10, 11))
          doze = Number(codigoBarra.substring(11, 12))
          soma =
            um * 1 +
            dois * 3 +
            tres * 1 +
            quatro * 3 +
            cinco * 1 +
            seis * 3 +
            sete * 1 +
            oito * 3 +
            nove * 1 +
            dez * 3 +
            onze * 1 +
            doze * 3
          soma = soma.toString()
          if (soma.slice(1) === '0') {
            codigoBarra += '0'
          } else {
            multiplo = Number(soma.slice(0, -1))
            multiplo = multiplo * 10 + 10
            resultado = multiplo - soma
            codigoBarra += resultado
          }

          produto = {
            descricao: `${nvl1.descricao} - ${res[i].descricao}`,
            nivel1: nvl1._id,
            nivel2: res[i]._id,
            vlrCusto: vlrCusto,
            vlrVenda: vlrVenda,
            codigo: codigo,
            codBarras: codigoBarra,
            pesquisa: pesquisa,
            coeficiente: coeficiente,
            favorito: false,
          }
          codigo++
          await new Produto(produto).save()
        }
      }

      // percorre o nivel3 montando os produtos
      Nivel3.find().then(async res => {
        for (let i = 0; i < res.length; i++) {
          if (res[i].ultimoNvl === 'sim') {
            nvl2 = await Nivel2.findById(res[i].chave)
            nvl1 = await Nivel1.findById(nvl2.chave)
            if (!res[i].pesquisa || res[i].pesquisa === 'não') {
              pesquisa = false
              coeficiente = null
              vlrCusto = res[i].valor
              vlrVenda = res[i].valorMax
            } else {
              pesquisa = true
              coeficiente = res[i].coeficiente
              vlrCusto = null
              vlrVenda = null
            }

            // gera o codigo de barras essa parte
            let codigoBarra = 123000000000
            codigoBarra += Number(codigo)
            codigoBarra = codigoBarra.toString()
            um = Number(codigoBarra.substring(0, 1))
            dois = Number(codigoBarra.substring(1, 2))
            tres = Number(codigoBarra.substring(2, 3))
            quatro = Number(codigoBarra.substring(3, 4))
            cinco = Number(codigoBarra.substring(4, 5))
            seis = Number(codigoBarra.substring(5, 6))
            sete = Number(codigoBarra.substring(6, 7))
            oito = Number(codigoBarra.substring(7, 8))
            nove = Number(codigoBarra.substring(8, 9))
            dez = Number(codigoBarra.substring(9, 10))
            onze = Number(codigoBarra.substring(10, 11))
            doze = Number(codigoBarra.substring(11, 12))
            soma =
              um * 1 +
              dois * 3 +
              tres * 1 +
              quatro * 3 +
              cinco * 1 +
              seis * 3 +
              sete * 1 +
              oito * 3 +
              nove * 1 +
              dez * 3 +
              onze * 1 +
              doze * 3
            soma = soma.toString()
            if (soma.slice(1) === '0') {
              codigoBarra += '0'
            } else {
              multiplo = Number(soma.slice(0, -1))
              multiplo = multiplo * 10 + 10
              resultado = multiplo - soma
              codigoBarra += resultado
            }

            produto = {
              descricao: `${nvl1.descricao} - ${nvl2.descricao} - ${res[i].descricao}`,
              nivel1: nvl1._id,
              nivel2: nvl2._id,
              nivel3: res[i]._id,
              vlrCusto: vlrCusto,
              vlrVenda: vlrVenda,
              codigo: codigo,
              codBarras: codigoBarra,
              pesquisa: pesquisa,
              coeficiente: coeficiente,
              favorito: false,
            }
            codigo++
            await new Produto(produto).save()
          }
        }

        // percorre o nivel3 montando os produtos
        Nivel4.find().then(async res => {
          for (let i = 0; i < res.length; i++) {
            if (res[i].ultimoNvl === 'sim') {
              nvl3 = await Nivel3.findById(res[i].chave)
              if (nvl3 !== null) {
                nvl2 = await Nivel2.findById(nvl3.chave)
                nvl1 = await Nivel1.findById(nvl2.chave)
                if (!res[i].pesquisa || res[i].pesquisa === 'não') {
                  pesquisa = false
                  coeficiente = null
                  vlrCusto = res[i].valor
                  vlrVenda = res[i].valorMax
                } else {
                  pesquisa = true
                  coeficiente = res[i].coeficiente
                  vlrCusto = null
                  vlrVenda = null
                }

                // gera o codigo de barras essa parte
                let codigoBarra = 123000000000
                codigoBarra += Number(codigo)
                codigoBarra = codigoBarra.toString()
                um = Number(codigoBarra.substring(0, 1))
                dois = Number(codigoBarra.substring(1, 2))
                tres = Number(codigoBarra.substring(2, 3))
                quatro = Number(codigoBarra.substring(3, 4))
                cinco = Number(codigoBarra.substring(4, 5))
                seis = Number(codigoBarra.substring(5, 6))
                sete = Number(codigoBarra.substring(6, 7))
                oito = Number(codigoBarra.substring(7, 8))
                nove = Number(codigoBarra.substring(8, 9))
                dez = Number(codigoBarra.substring(9, 10))
                onze = Number(codigoBarra.substring(10, 11))
                doze = Number(codigoBarra.substring(11, 12))
                soma =
                  um * 1 +
                  dois * 3 +
                  tres * 1 +
                  quatro * 3 +
                  cinco * 1 +
                  seis * 3 +
                  sete * 1 +
                  oito * 3 +
                  nove * 1 +
                  dez * 3 +
                  onze * 1 +
                  doze * 3
                soma = soma.toString()
                if (soma.slice(1) === '0') {
                  codigoBarra += '0'
                } else {
                  multiplo = Number(soma.slice(0, -1))
                  multiplo = multiplo * 10 + 10
                  resultado = multiplo - soma
                  codigoBarra += resultado
                }

                produto = {
                  descricao: `${nvl1.descricao} - ${nvl2.descricao} - ${nvl3.descricao} -  ${res[i].descricao}`,
                  nivel1: nvl1._id,
                  nivel2: nvl2._id,
                  nivel3: nvl3._id,
                  nivel4: res[i]._id,
                  vlrCusto: vlrCusto,
                  vlrVenda: vlrVenda,
                  codigo: codigo,
                  codBarras: codigoBarra,
                  pesquisa: pesquisa,
                  coeficiente: coeficiente,
                  favorito: false,
                }
                codigo++
                await new Produto(produto).save()
              }
            }
          }
        })
      })
    })
  })

  res.send('ok')
})

// rota que remove todos os produtos que nao forem favoritos
router.get('/rm_produto', isAuth, async (req, res) => {
  await Produto.deleteMany({ favorito: false })
  res.send('ok')
})

router.post('/gerar_nfe', isAuth, async (req, res, next) => {
  try {
    const managerSaasApi = Axios.create({
      baseURL: `${process.env.TECNOSPEED_BASE_URL}/ManagerAPIWeb/nfe/`,
      headers: {
        Authorization: 'Basic YWRtaW46ZGZjb20xNDc=',
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    })

    const { idAvaliacao, dataEmissao } = req.body
    const avaliacao = await Avaliacao.findById(idAvaliacao)

    let evento = {}

    let jaEmitiu = false

    for (let i = 0; i < avaliacao?.eventosFiscais?.length; i++) {
      if (typeof avaliacao.eventosFiscais[i].excecao === 'undefined') {
        if (avaliacao.eventosFiscais[i].status === '100') {
          evento = 'Nota já emitida'
          jaEmitiu = true
        }
      }
    }

    if (jaEmitiu) {
      return res.status(400).send(evento)
    } else {
      if (avaliacao?.eventosFiscais?.length) {
        const fiscalEvent = avaliacao.eventosFiscais[avaliacao?.eventosFiscais?.length - 1] || {}

        if (fiscalEvent) {
          const { data } = await managerSaasApi.get(`/consulta`, {
            params: {
              CNPJ: req?.usuario?.enterprise?.cnpj,
              Grupo: 'CresciPerdi',
              Filtro: `codnf=${fiscalEvent?.codNf}`,
              Campos: 'chave, situacao, nnf, dtemissao, cnpj, motivo, serie, cstat',
            },
          })
          const nfeInfos = data?.split(',')

          if (nfeInfos[1] === 'ENVIADA' || nfeInfos[1] === 'RECEBIDA') {
            const { data: nfeResolve } = await managerSaasApi.request({
              method: 'POST',
              url: '/resolve',
              params: {
                Grupo: 'CresciPerdi',
                CNPJ: Number(req?.usuario?.enterprise?.cnpj),
                ChaveNota: nfeInfos[0],
                CamposRetorno: 'serie,chave,cstat,motivo,numero,dtemissao,situacao',
              },
            })

            const sendNfeInfos = nfeResolve?.split(',')
            const [serie, chave, status, motivo, numero, dataEmissao, situacao] = sendNfeInfos
            const formatSituation = String(situacao).trim()

            if (formatSituation == 'AUTORIZADA' || formatSituation == 'REJEITADA') {
              await Avaliacao.findOneAndUpdate(
                { _id: new ObjectId(avaliacao?._id) },
                {
                  eventosFiscais: [
                    ...avaliacao.eventosFiscais,
                    {
                      serie,
                      chave,
                      status,
                      motivo,
                      numero,
                      dataEmissao,
                      situacao: formatSituation,
                      codNf: fiscalEvent?.codNf,
                    },
                  ],
                }
              )

              if (sendNfeInfos[2] === '100') {
                return res.status(200).send({
                  status: '100',
                })
              } else {
                return res.status(400).send({
                  mensagem: motivo,
                  situacao: formatSituation,
                })
              }
            } else if (formatSituation === 'EXCEPTION') {
              await Avaliacao.findOneAndUpdate(
                { _id: new ObjectId(avaliacao?._id) },
                {
                  eventosFiscais: [
                    ...avaliacao.eventosFiscais,
                    {
                      serie,
                      chave,
                      status,
                      motivo,
                      numero,
                      dataEmissao,
                      situacao: formatSituation,
                      codNf: fiscalEvent?.codNf,
                    },
                  ],
                }
              )

              return res.status(400).send({
                mensagem: motivo,
                situacao: situacao,
              })
            } else if (nfeInfos[1] === 'RECEBIDA') {
              await Avaliacao.findOneAndUpdate(
                { _id: new ObjectId(avaliacao?._id) },
                {
                  eventosFiscais: [
                    ...avaliacao.eventosFiscais,
                    {
                      serie: nfeInfos[6],
                      chave: nfeInfos[0],
                      status: nfeInfos[7],
                      motivo: nfeInfos[5],
                      numero: nfeInfos[2],
                      dataEmissao: nfeInfos[3],
                      situacao: 'RECEBIDA',
                      codNf: fiscalEvent?.codNf,
                    },
                  ],
                }
              )

              return res.status(200).send({
                mensagem: 'A nota foi recebida pela SEFAZ, tente consultar mais tarde',
                status: nfeInfos[7],
                situacao: 'RECEBIDA',
              })
            }
          } else if (nfeInfos[1] === 'REJEITADA') {
            return res.status(400).send('Nota rejeitada')
          }
        }
      }
    }

    const counter = await Caixa.findById(avaliacao?.caixa)
    let company = await Empresa.findOne({ cnpj: avaliacao.cnpj })
    company = JSON.stringify(company)
    company = JSON.parse(company)
    const { products, sum, sum_icms, discount, others } = await getItems(avaliacao, company)
    const tx2Payments = [
      {
        tPag_YA02: '01',
        vPag_YA03: 0,
      },
    ]

    const user = await Usuario.findById(counter.idUsuario)
    const client = await Cliente.findOne({ cpf: avaliacao.cpf })

    verifyTmpDir()

    let tx2FileName = `../../../tmp/${randomstring.generate({
      length: 12,
      charset: 'alphabetic',
    })}.tx2`

    const cnf_b03 = (await generatecNF_B03()).toString()
    const notaData = await getNotaData(company, user, cnf_b03, 55, 3, avaliacao, dataEmissao)
    const issuer = getIssuer(company)
    const clientData = getClient(company, client)
    const totalizer = getTotalizer(sum, discount, others, company, sum_icms)
    const technician = getTechnician()
    const cnpjContador = company.cnpjContador ? company.cnpjContador : ''

    const tx2File = await generateNFeTx2(
      tx2FileName,
      notaData,
      issuer,
      clientData,
      products,
      tx2Payments,
      totalizer,
      technician,
      cnpjContador,
      '',
      {}
    )

    const response = await sendNFe(
      tx2File,
      avaliacao.cnpj,
      'CresciPerdi',
      'Basic YWRtaW46ZGZjb20xNDc=',
      tecnoSpeedPort,
      amb
    )

    const uploadData = await uploadTx2File(tx2File)

    deleteTx2File(tx2FileName)

    const info = response.split(',')

    let status = 200
    if (!info[0]?.trim()?.toUpperCase()?.includes('EXCEPTION')) {
      evento = {
        serie: info[0],
        chave: info[1],
        status: info[2],
        motivo: info[3],
        numero: info[4],
        dataEmissao: info[5],
        situacao: info[6],
        codNf: cnf_b03,
        ...uploadData,
      }
    } else {
      evento = {
        resposta: info[0],
        tipoException: info[1],
        mensagem: info[2],
        codNf: cnf_b03,
        // serie: user.serie
        ...uploadData,
      }
      status = 400
    }

    const eventosFiscais = avaliacao?.eventosFiscais || []
    eventosFiscais.push(evento)
    const invoiceEvent = eventosFiscais?.[eventosFiscais?.length - 1]
    const invoiceAt = moment.utc(invoiceEvent?.dataEmissao, 'DD/MM/YYYY HH:mm:ss').toDate()

    await Avaliacao.findByIdAndUpdate(idAvaliacao, {
      eventosFiscais: eventosFiscais,
      invoiceAt:
        moment.utc(eventosFiscais[eventosFiscais?.length - 1]?.dataEmissao).toISOString() ||
        moment.utc().toDate(),
    })
    res.status(status).send(evento)
  } catch (e) {
    console.log(e)
    return next(new HttpException(500, 'Algo deu errado. Tente novamente, por favor.', e))
  }
})

async function getNotaData(company, user, cnf_b03, mod, tpImp, evaluation, dataEmissao = '') {
  const evaluationClient = await Cliente.findOne({ cpf: evaluation?.cpf }).lean()
  const evaluationEnterprise = await Empresa.findOne({ cnpj: evaluation?.cnpj }).lean()

  let idDest = 1

  if (evaluationClient?.cod_uf != evaluationEnterprise?.codUf) {
    idDest = 2
  }

  const dhEmi_B09 = dataEmissao
    ? moment(dataEmissao, 'YYYY-MM-DD')
        .set({ hours: 12 })
        .tz('America/Sao_Paulo')
        .format('YYYY-MM-DDTHH:mm:ssZ')
    : moment.tz('America/Sao_Paulo').format('YYYY-MM-DDTHH:mm:ssZ')

  const data = {
    versao_A02: '4.00',
    cUF_B02: company.codUf,
    cNF_B03: cnf_b03,
    natOp_B04: 'Compra para comercialização',
    mod_B06: mod,
    serie_B07: 1,
    nNF_B08: 200,
    dhEmi_B09,
    tpNF_B11: 0,
    idDest_B11a: idDest,
    cMunFG_B12: parseInt(company.codCidade),
    tpImp_B21: tpImp,
    tpEmis_B22: 1,
    cDV_B23: 0,
    tpAmb_B24: process.env.PRODUCTION === 'true' ? 1 : 2,
    finNFe_B25: 1,
    indFinal_B25a: 1,
    indPres_B25b: 1,
    procEmi_B26: 0,
    verProc_B27: 'dfcomonline 1.0',
    CamposRetorno: 'serie, chave, cstat, motivo, numero, dtemissao, situacao',
  }

  const contingencyUFSVCRS = ['AM', 'BA', 'CE', 'GO', 'MA', 'MS', 'MT', 'PA', 'PE', 'PI', 'PR']

  // Fluxo Provisório Contingency ( TODO: Deve ser posteriormente automatizado )
  if (company?.contingencyNFEMode) {
    data['dhCont_B28'] = dhEmi_B09
    data['xJust_B29'] = 'Modo de Contingência'
    data['tpEmis_B22'] = 6
    // Estados de uso isolado
    if (contingencyUFSVCRS.includes(company.uf)) {
      data['tpEmis_B22'] = 7
    }
  }

  return data
}

function getIssuer(company) {
  const nomeEmpresa = (company?.razaoSocial || company?.nomeReduzido || '')
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')

  return {
    CNPJ_C02: company.cnpj,
    xNome_C03: nomeEmpresa,
    xFant_C04: company.nomeReduzido,
    xLgr_C06: company.endereco,
    nro_C07: parseInt(company.numero),
    xCpl_C08: '',
    xBairro_C09: company.bairro,
    cMun_C10: company.codCidade,
    xMun_C11: company.cidade,
    UF_C12: company.uf,
    CEP_C13: company.cep.replace(/\D/g, ''),
    cPais_C14: '1058',
    xPais_C15: 'BRASIL',
    indIEDest_E16a: 9,
    IE_C17: company.ie.replace(/\D/g, ''),
    IEST_C18: '',
    CRT_C21: company.crt,
  }
}

async function getItems(evaluation, company) {
  let products = []
  let sum = 0
  let sum_icms = 0
  let counter = 1
  let product
  let value
  let discount = 0
  let others = 0

  const evaluationClient = await Cliente.findOne({ cpf: evaluation?.cpf }).lean()
  const evaluationEnterprise = await Empresa.findOne({ cnpj: evaluation?.cnpj }).lean()

  let CFOP = '1102'

  if (evaluationClient?.cod_uf != evaluationEnterprise?.codUf) {
    CFOP = '2102'
  }

  if (evaluation.manual) {
    if (evaluation.tipo === 'Vale') {
      products = [
        {
          nItem_H02: counter,
          cEAN_I03: 'SEM GTIN',
          cProd_I02: 0,
          xProd_I04:
            process.env.PRODUCTION === 'true'
              ? 'Produto Seminovo'
              : 'NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL',
          NCM_I05: '63090010',
          CEST_I05c: '0000000',
          CFOP_I08: CFOP,
          uCom_I09: 'UND',
          qCom_I10: 1,
          vUnCom_I10a: Number(evaluation.totalValeEfetivado || 0).toFixed(2),
          vProd_I11: Number(evaluation.totalValeEfetivado || 0).toFixed(2),
          cEANTrib_I12: 'SEM GTIN',
          uTrib_I13: 'UND',
          qTrib_I14: 1.0,
          vUnTrib_I14a: Number(evaluation.totalValeEfetivado || 0).toFixed(2),
          indTot_I17b: 1,
          orig_N11: 0,
          CST_Q06: '98',
          CST_S06: '98',
          vBC_Q07: '0',
          pPIS_Q08: '0',
          vPIS_Q09: '0',
          vBC_S07: '0',
          pCOFINS_S08: '0',
          vCOFINS_S11: '0',
          CSOSN_N12a: '102', //simples
        },
      ]
      if (company.crt !== 1) {
        products[0].CST_N12 = '00'
        products[0].modBC_N13 = '0'
        products[0].vBC_N15 = Number(evaluation.totalValeEfetivado || 0).toFixed(2)
        products[0].pICMS_N16 = '18'
        products[0].vICMS_N17 = Number((evaluation.totalValeEfetivado || 0) * 0.18).toFixed(2)
        sum_icms = Number(products[0].vICMS_N17) || 0
        delete products[0].CSOSN_N12a
      }
      sum = Number(evaluation.totalValeEfetivado) || 0
    } else if (evaluation.tipo === 'Pix') {
      products = [
        {
          nItem_H02: counter,
          cEAN_I03: 'SEM GTIN',
          cProd_I02: 0,
          xProd_I04:
            process.env.PRODUCTION === 'true'
              ? 'Produto Seminovo'
              : 'NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL',
          NCM_I05: '63090010',
          CEST_I05c: '0000000',
          CFOP_I08: CFOP,
          uCom_I09: 'UND',
          qCom_I10: 1,
          vUnCom_I10a: Number(evaluation.totalPixEfetivado || 0).toFixed(2),
          vProd_I11: Number(evaluation.totalPixEfetivado || 0).toFixed(2),
          cEANTrib_I12: 'SEM GTIN',
          uTrib_I13: 'UND',
          qTrib_I14: 1.0,
          vUnTrib_I14a: Number(evaluation.totalPixEfetivado || 0).toFixed(2),
          indTot_I17b: 1,
          orig_N11: 0,
          CST_Q06: '98',
          CST_S06: '98',
          vBC_Q07: '0',
          pPIS_Q08: '0',
          vPIS_Q09: '0',
          vBC_S07: '0',
          pCOFINS_S08: '0',
          vCOFINS_S11: '0',
          CSOSN_N12a: '102',
        },
      ]
      if (company.crt !== 1) {
        products[0].CST_N12 = '00'
        products[0].modBC_N13 = '0'
        products[0].vBC_N15 = Number(evaluation.totalPixEfetivado || 0).toFixed(2)
        products[0].pICMS_N16 = '18'
        products[0].vICMS_N17 = Number((evaluation.totalPixEfetivado || 0) * 0.18).toFixed(2)
        sum_icms = Number(products[0].vICMS_N17) || 0
        delete products[0].CSOSN_N12a
      }
      sum = Number(evaluation.totalPixEfetivado) || 0
    } else if (evaluation?.tipo === 'Dinheiro') {
      products = [
        {
          nItem_H02: counter,
          cEAN_I03: 'SEM GTIN',
          cProd_I02: 0,
          xProd_I04:
            process.env.PRODUCTION === 'true'
              ? 'Produto Seminovo'
              : 'NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL',
          NCM_I05: '63090010',
          CEST_I05c: '0000000',
          CFOP_I08: CFOP,
          uCom_I09: 'UND',
          qCom_I10: 1,
          vUnCom_I10a: Number(evaluation.totalDinheiroEfetivado || 0).toFixed(2),
          vProd_I11: Number(evaluation.totalDinheiroEfetivado || 0).toFixed(2),
          cEANTrib_I12: 'SEM GTIN',
          uTrib_I13: 'UND',
          qTrib_I14: 1.0,
          vUnTrib_I14a: Number(evaluation.totalDinheiroEfetivado || 0).toFixed(2),
          indTot_I17b: 1,
          orig_N11: 0,
          CST_Q06: '98',
          CST_S06: '98',
          vBC_Q07: '0',
          pPIS_Q08: '0',
          vPIS_Q09: '0',
          vBC_S07: '0',
          pCOFINS_S08: '0',
          vCOFINS_S11: '0',
          CSOSN_N12a: '102',
        },
      ]
      if (company.crt !== 1) {
        products[0].CST_N12 = '00'
        products[0].modBC_N13 = '0'
        products[0].vBC_N15 = Number(evaluation.totalDinheiroEfetivado || 0).toFixed(2)
        products[0].pICMS_N16 = '18'
        products[0].vICMS_N17 = Number((evaluation.totalDinheiroEfetivado || 0) * 0.18).toFixed(2)
        sum_icms = Number(products[0].vICMS_N17) || 0
        delete products[0].CSOSN_N12a
      }
      sum = Number(evaluation.totalDinheiroEfetivado) || 0
    }
  } else {
    let maiorValor = 0
    let indexDoMaior

    const evaluationItems = evaluation?.items?.filter(item => item?.excluido === false)

    let indexOfHighValue = 0

    for (let i = 0; i < evaluationItems.length; i++) {
      product = await Produto.findById(evaluationItems?.[i].idProduto)
      if (!product) continue

      if (typeof evaluationItems?.[i].precoBruto !== 'undefined') {
        if (evaluationItems?.[i].precoBruto > 2000) {
          value = evaluationItems?.[i].precoBruto / 6
        } else {
          value = evaluationItems?.[i].precoBruto / 5
        }
      } else {
        value = Number(product?.vlrCusto) || 0
      }

      sum += (evaluationItems?.[i].qtde || evaluationItems?.[i]?.qtd || 0) * value

      products.push({
        nItem_H02: counter,
        cEAN_I03: 'SEM GTIN',
        cProd_I02: product.codigo,
        xProd_I04: product.descricao,
        NCM_I05: '63090010',
        CEST_I05c: '0000000',
        CFOP_I08: CFOP,
        uCom_I09: 'UND',
        qCom_I10: (evaluationItems?.[i]?.qtde || evaluationItems?.[i]?.qtd || 0)?.toFixed(2),
        vUnCom_I10a: value.toFixed(2),
        vProd_I11: ((evaluationItems?.[i]?.qtde || evaluationItems?.[i]?.qtd || 0) * value).toFixed(
          2
        ),
        cEANTrib_I12: 'SEM GTIN',
        uTrib_I13: 'UND',
        qTrib_I14: (evaluationItems?.[i]?.qtde || evaluationItems?.[i]?.qtd || 0)?.toFixed(2),
        vUnTrib_I14a: value?.toFixed(2),
        indTot_I17b: 1,
        orig_N11: 0,
        CST_Q06: '98',
        CST_S06: '98',
        vBC_Q07: '0',
        pPIS_Q08: '0',
        vPIS_Q09: '0',
        vBC_S07: '0',
        pCOFINS_S08: '0',
        vCOFINS_S11: '0',
        CSOSN_N12a: '102',
      })

      counter += 1

      if (company?.crt !== 1 && products?.[i]) {
        products[i].CST_N12 = '00'
        products[i].modBC_N13 = '0'
        products[i].vBC_N15 = (
          (evaluationItems?.[i]?.qtde || evaluationItems?.[i]?.qtd || 0) * value
        ).toFixed(2)
        products[i].pICMS_N16 = '18'
        products[i].vICMS_N17 = Number(
          (evaluationItems?.[i]?.qtde || evaluationItems?.[i]?.qtd || 0) * value * 0.18
        ).toFixed(2)
        sum_icms += Number(products[i].vICMS_N17) || 0
        delete products[i].CSOSN_N12a
      }

      // encontra o maior valor entre os produtos
      if (
        Number(maiorValor || 0) <
        Number((evaluationItems?.[i]?.qtde || evaluationItems?.[i]?.qtd || 0) * value).toFixed(2)
      ) {
        maiorValor = (evaluation.items[i]?.qtde * value).toFixed(2)
        indexDoMaior = indexOfHighValue
      }

      indexOfHighValue += 1
    }

    let diferenca
    if (evaluation.tipo === 'Vale') {
      diferenca = (Number(evaluation.totalValeEfetivado) || 0) - sum
    } else if (evaluation.tipo === 'Pix') {
      diferenca = (Number(evaluation.totalPixEfetivado) || 0) - sum
    } else if (evaluation.tipo === 'Dinheiro') {
      diferenca = (Number(evaluation.totalDinheiroEfetivado) || 0) - sum
    }

    if (diferenca < 0) {
      discount = diferenca * -1
      const vProdi_I11 = products?.[indexDoMaior]?.vProd_I11 || 0

      if (products?.[indexDoMaior]) {
        if (discount > 0) {
          if (discount > vProdi_I11) {
            products[indexDoMaior].vDesc_I17 = Number(vProdi_I11 || 0).toFixed(2)
            diferenca =
              Number(Number(Number(diferenca).toFixed(2)) || 0) +
              Number((Number(Number(vProdi_I11)) || 0).toFixed(2))
          } else {
            products[indexDoMaior].vDesc_I17 = Number((Number(diferenca) || 0) * -1).toFixed(2)
            diferenca = 0
          }

          while (diferenca < 0) {
            const productWithoutDiscount = products?.filter(product => !product?.vDesc_I17)[0]
            const indexOfProductWithoutDiscount = products.indexOf(productWithoutDiscount)

            if (diferenca > products[indexOfProductWithoutDiscount]?.vProd_I11) {
              products[indexOfProductWithoutDiscount].vDesc_I17 = Number(
                products[indexOfProductWithoutDiscount].vProd_I11 || 0
              ).toFixed(2)

              diferenca =
                Number(diferenca) + Number(products[indexOfProductWithoutDiscount].vProd_I11)
            } else {
              products[indexOfProductWithoutDiscount].vDesc_I17 = Number(diferenca * -1).toFixed(2)
              diferenca = 0
            }
          }
        }
      }
    } else if (diferenca > 0) {
      others = diferenca

      if (products?.[0]) {
        products[0].vOutro_I17a = others.toFixed(2)
      }
    }
  }

  const productsList = products?.map(product => {
    if (product?.vDesc_I17 === 0) delete product?.vDesc_I17

    return product
  })

  return {
    sum: sum.toFixed(2),
    sum_icms,
    products: productsList,
    discount,
    others,
  }
}

function getClient(company, client) {
  if (company?.uf !== 'MG') {
    const { cpf, nome, rua, numero, bairro, cod_cidade, cidade, estado } = client || {}

    return {
      CPF_E03: cpf || '',
      idEstrangeiro_E03a: '',
      xNome_E04:
        process.env.PRODUCTION === 'true'
          ? nome
          : 'NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL',
      xLgr_E06: rua || 'SEM LOGRADOURO',
      nro_E07: numero || 1,
      xBairro_E09: bairro || 'SEM BAIRRO',
      cMun_E10: cod_cidade || '',
      xMun_E11: cidade || 'SEM CIDADE',
      UF_E12: estado || '',
    }
  } else {
    const { cnpj, razaoSocial, endereco, numero, bairro, codCidade, cidade, uf, ie } = company || {}
    const {
      nome,
      rua,
      numero: number,
      bairro: neighborhood,
      cidade: city,
      estado,
      cpf,
      complemento,
    } = client || {}
    const aditionalMessage =
      'Não incidência de ICMS por não ocorrência do fato gerador nos termos do artigo 7º do RICMS/2023. Emitida nos termos do artigo 4º, inciso I, parte 1, anexo V do RICMS-2023.'

    return {
      CNPJ_E02: cnpj || '',
      idEstrangeiro_E03a: '',
      xNome_E04:
        process.env.PRODUCTION === 'true'
          ? razaoSocial
          : 'NF-E EMITIDA EM AMBIENTE DE HOMOLOGACAO - SEM VALOR FISCAL',
      xLgr_E06: endereco || 'SEM LOGRADOURO',
      nro_E07: numero || 1,
      xBairro_E09: bairro || 'SEM BAIRRO',
      cMun_E10: codCidade || '',
      xMun_E11: cidade || 'SEM CIDADE',
      UF_E12: uf || '',
      indIEDest_E16a: ie ? 1 : 9,
      IE_E17: ie || '',
      infCpl_Z03: `CPF: ${cpf} | Nome: ${nome} | Endereço: ${rua}, ${number}, ${neighborhood} ${
        complemento ? `, ${complemento}` : ''
      } - ${city}, ${estado} ${aditionalMessage}`,
    }
  }
}

function getTechnician() {
  return {
    CNPJ_ZD02: '23212902000197',
    email_ZD05: '<EMAIL>',
    fone_ZD06: '1936637938',
    xContato_ZD04: 'Eduardo',
  }
}

function getTotalizer(sum, discount, others, company, sum_icms) {
  let w03, w04
  if (company.crt === 1) {
    w03 = 0
    w04 = 0
  } else {
    w03 = sum
    w04 = Number(sum_icms).toFixed(2)
  }

  return {
    vBC_W03: w03,
    vICMS_W04: w04,
    vICMSDeson_W04a: 0,
    vFCPUFDest_W04c: 0,
    VICMSUFDest_W04e: 0,
    vProd_W07: sum,
    vNF_W16: Number(sum - discount + others).toFixed(2),
    modFrete_X02: 9,
    VICMSUFRemet_W04g: 0,
    vBCST_W05: 0,
    vCOFINS_W14: 0,
    vDesc_W10: discount.toFixed(2),
    vFCPSTRet_W06b: 0,
    vFCPST_W06a: 0,
    vFCP_W04h: 0,
    vFrete_W08: 0,
    vII_W11: 0,
    vIPIDevol_W12a: 0,
    vIPI_W12: 0,
    vOutro_W15: others.toFixed(2),
    vPIS_W13: 0,
    vST_W06: 0,
    vSeg_W09: 0,
    vTotTrib_W16a: 0,
  }
}

router.post('/consultar_nfe', isAuth, async (req, res) => {
  const { cnpj, codNf } = req.body
  var config = {
    method: 'get',
    url: `${tecnoSpeedBaseUrl}/ManagerAPIWeb/nfe/consulta?grupo=CresciPerdi&cnpj=${cnpj}&Filtro=serie=1 and codnf=${codNf}&campos=situacao,chave`,
    headers: {
      Authorization: 'Basic YWRtaW46ZGZjb20xNDc=',
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  }
  axios(config)
    .then(function (response) {
      return res.status(200).send(response.data)
    })
    .catch(function (error) {
      return res.status(400).send(error)
    })
})

router.post('/cancelar_nfe', isAuth, async (req, res) => {
  const { cnpj, chavenota, justificativa } = req.body
  var data = qs.stringify({
    grupo: 'CresciPerdi',
    cnpj: cnpj,
    chavenota: chavenota,
    justificativa: justificativa,
  })
  var config = {
    method: 'post',
    url: `${tecnoSpeedBaseUrl}/ManagerAPIWeb/nfe/cancela`,
    headers: {
      Authorization: 'Basic YWRtaW46ZGZjb20xNDc=',
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  }

  axios(config)
    .then(function (response) {
      return res.status(200).send(response.data)
    })
    .catch(function (error) {
      return res.status(400).send(error)
    })
})

router.post('/imprimeNf', isAuth, async (req, res) => {
  const { cnpj, chavenota } = req.body
  var config = {
    method: 'get',
    url: `${tecnoSpeedBaseUrl}/ManagerAPIWeb/nfe/imprime?grupo=CresciPerdi&cnpj=${cnpj}&chavenota=${chavenota}&url=1`,
    headers: {
      Authorization: 'Basic YWRtaW46ZGZjb20xNDc=',
    },
  }

  axios(config)
    .then(function (response) {
      return res.status(200).send(response.data)
    })
    .catch(function (error) {
      return res.status(400).send(error)
    })
})

router.post('/status_sefaz', isAuth, async (req, res) => {
  const { cnpj } = req.body
  var config = {
    method: 'get',
    url: `${tecnoSpeedBaseUrl}/ManagerAPIWeb/nfe/status?grupo=CresciPerdi&cnpj=${cnpj}`,
    headers: {
      Authorization: 'Basic YWRtaW46ZGZjb20xNDc=',
      'Content-Type': 'application/x-www-form-urlencoded',
    },
  }
  axios(config)
    .then(function (response) {
      return res.status(200).send(response.data)
    })
    .catch(function (error) {
      return res.status(400).send(error)
    })
})

router.post('/descartar_nfe', isAuth, async (req, res) => {
  const { cnpj, chavenota } = req.body
  var data = qs.stringify({
    grupo: 'CresciPerdi',
    cnpj: cnpj,
    chavenota: chavenota,
  })
  var config = {
    method: 'post',
    url: `${tecnoSpeedBaseUrl}/ManagerAPIWeb/nfe/descarta`,
    headers: {
      Authorization: 'Basic YWRtaW46ZGZjb20xNDc=',
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    data: data,
  }

  axios(config)
    .then(function (response) {
      return res.status(200).send(response.data)
    })
    .catch(function (error) {
      return res.status(400).send(error)
    })
})

router.patch('/update_eventosFiscais', isAuth, async (req, res) => {
  const { eventosFiscais } = req.body
  const update = {
    serie: eventosFiscais.serie,
    chave: eventosFiscais.chave,
    status: eventosFiscais.status,
    motivo: eventosFiscais.motivo,
    numero: eventosFiscais.numero,
    dataEmissao: eventosFiscais.dataEmissao,
    codNf: eventosFiscais.codNf,
    situacao: eventosFiscais.situacao,
  }
  await Avaliacao.findByIdAndUpdate(eventosFiscais.idAvaliacao, {
    $push: { eventosFiscais: update },
  })
  return res.send('Atualizado array de eventos fiscais')
})

// rota que busca todas as avaliações que possuem eventos fiscais
router.get('/buscar_nfes/:cnpj', isAuth, async (req, res) => {
  const { cnpj } = req.params
  const avaliacoes = await Avaliacao.find({
    'eventosFiscais.0': { $exists: true },
    cnpj: cnpj,
  }).sort({ dataInicio: -1 })
  return res.send(avaliacoes)
})

module.exports = router
