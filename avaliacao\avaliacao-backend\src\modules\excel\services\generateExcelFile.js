const exceljs = require('exceljs');

const generateExcelFile = async (evaluations = []) => {
  const fileHeader = Object.keys(evaluations?.[0])

  const workbook = new exceljs.Workbook()
  const worksheet = workbook.addWorksheet('Avaliações')
  const path = process.cwd() + '/temp'

  worksheet.columns = fileHeader.map(column => ({
    header: column.toUpperCase(),
    key: column,
    width: 20
  }))
  worksheet.state = 'visible'

  evaluations.forEach(evaluation => {
    worksheet.addRow(evaluation)
  })

  worksheet.addRows(evaluations)

  worksheet.getRow(1).eachCell((cell) => {
    cell.font = { bold: true, }
  })

  await workbook.xlsx.writeFile(`${path}/evaluations.xlsx`)
  return `${path}/evaluations.xlsx`
}

module.exports = { generateExcelFile }
