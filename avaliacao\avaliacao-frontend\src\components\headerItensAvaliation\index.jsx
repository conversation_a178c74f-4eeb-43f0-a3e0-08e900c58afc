import moment from 'moment'
import { Component } from 'react'

export class HeaderItensAvaliation extends Component {
  constructor(props) {
    super(props)
  }

  render() {
    const { clienteTitulo, usuarioTitulo, dataTitulo, status } = this.props

    return (
      <>
        <div className="title-search hidden-print" id="produtos-avaliacao-itens">
          Fornecedor: {clienteTitulo}
        </div>
        <div
          className="title-search hidden-print"
          style={{ marginTop: '0' }}
          id="produtos-avaliacao-itens">
          Usuário: {usuarioTitulo}
        </div>
        <div
          className="title-search hidden-print"
          style={{ marginTop: '0' }}
          id="produtos-avaliacao-itens">
          Finalizada em: {dataTitulo}
        </div>
        <div
          className="title-search hidden-print"
          style={{ marginTop: '0' }}
          id="produtos-avaliacao-itens">
          Status: {status}
        </div>
        <div
          className="title-search hidden-print"
          style={{ marginTop: '0' }}
          id="produtos-avaliacao-itens">
          Itens da Avaliação:
        </div>
      </>
    )
  }
}
