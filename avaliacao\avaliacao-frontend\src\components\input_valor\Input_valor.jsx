import React from 'react'
import './Input_valor.css'

const Input_valor = props => {
  return (
    <>
      <div className="linha-descricao">
        <input
          id="input-desc1"
          placeholder="Nome do produto"
          className="descricao"
          name="descricao1"
          style={{ display: 'initial' }}
          defaultValue={props.value}
          readOnly
        />
        <input
          className="checkbox"
          type="checkbox"
          id={`cbx-modal${props.arg}`}
          onChange={props.handleChange}
        />
      </div>
      <div>Ultimo Nível:</div>
      <div className="label">
        <input
          type="radio"
          name={`ultimoNvlModal${props.arg}`}
          value="sim"
          style={{ marginRight: '5px', marginBottom: '10px' }}
          onChange={props.handleChange}
        />
        <label className="label-checkbox" htmlFor="sim">
          Sim
        </label>
        <input
          type="radio"
          name={`ultimoNvlModal${props.arg}`}
          value="não"
          style={{ marginRight: '5px', marginLeft: '15px', marginBottom: '10px' }}
          onChange={props.handleChange}
        />
        <label className="label-checkbox" htmlFor="não">
          Não
        </label>
      </div>
      <div className="linha-valores">
        <input
          type="number"
          placeholder="Custo"
          className="descricao"
          name={`custoModal${props.arg}`}
          onChange={props.handleChange}
          value={props.custoModal}
        />
        <input
          type="number"
          placeholder="Valor Mín."
          className="descricao"
          name={`minModal${props.arg}`}
          onChange={props.handleChange}
          value={props.minModal}
        />
        <input
          type="number"
          placeholder="Valor Máx."
          className="descricao"
          name={`maxModal${props.arg}`}
          onChange={props.handleChange}
          value={props.maxModal}
        />
        <input
          type="number"
          placeholder="Peso"
          className="descricao"
          name={`pesoModal${props.arg}`}
          onChange={props.handleChange}
          value={props.pesoModal}
        />
      </div>
    </>
  )
}

export default Input_valor
