const { SecretsManagerClient, GetSecretValueCommand } = require('@aws-sdk/client-secrets-manager')

const fetchSecrets = async () => {
  if (!process.env.AWS_SECRET_NAME) {
    throw new Error('AWS_SECRET_NAME não está definido.')
  }

  console.log(
    'Dica: Configure AWS_SECRET_NAME no arquivo .env.development. Use test/avaliacao-test para desenvolvimento e test/avaliacao-homolog para homologação.'
  )

  const secretName = process.env.AWS_SECRET_NAME

  const client = new SecretsManagerClient({
    region: 'sa-east-1',
  })

  try {
    const response = await client.send(
      new GetSecretValueCommand({
        SecretId: secretName,
        VersionStage: 'AWSCURRENT',
      })
    )

    if (!response.SecretString) {
      throw new Error('SecretString está vazio ou indefinido')
    }

    try {
      return JSON.parse(response.SecretString)
    } catch (parseError) {
      console.error('Erro ao analisar SecretString:', parseError)
      throw new Error('Falha ao analisar o segredo como JSON')
    }
  } catch (error) {
    console.error('Erro ao buscar o secret:', error)

    if (error.name === 'ResourceNotFoundException') {
      throw new Error(`Segredo '${secretName}' não encontrado`)
    }

    throw new Error('Falha ao recuperar o segredo do AWS Secrets Manager')
  }
}

module.exports = {
  fetchSecrets,
}
