import Modal from "react-modal";
import * as IonIcons from "react-icons/io5";
import ImageViewer from "react-simple-image-viewer";
import "./styles.css";
import { useCallback, useState } from "react";
import { BsChevronLeft } from "react-icons/bs";

interface IBatchImagesModalProps {
  isOpen: boolean;
  onClose: () => void;
  images: {
    key: string;
    url: string;
  }[];
}

const customStyles = {
  overlay: {
    background: "#00000050",
  },
  content: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    margin: "auto",
    background: "transparent",
    border: "none",
  },
};

export const BatchImagesModal: React.FC<IBatchImagesModalProps> = ({
  isOpen,
  onClose,
  images,
}) => {
  const [currentImage, setCurrentImage] = useState(0);
  const [isViewerOpen, setIsViewerOpen] = useState(false);

  const openImageViewer = useCallback((index: number) => {
    setCurrentImage(index);
    setIsViewerOpen(true);
  }, []);

  const closeImageViewer = () => {
    setCurrentImage(0);
    setIsViewerOpen(false);
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={() => {
        onClose();
        setIsViewerOpen(false);
      }}
      style={customStyles}
    >
      <div className="batch-images-modal-container">
        <div className="batch-images-modal-header">
          <h2>Imagens do lote</h2>
          <IonIcons.IoCloseOutline
            size={30}
            style={{ cursor: "pointer" }}
            onClick={() => {
              onClose();
              setIsViewerOpen(false);
            }}
          />
        </div>
        <div className="preview-row">
          {images?.map((image: any, index: number) => (
            <img
              className="preview-image"
              src={image?.url}
              alt={image?.key}
              onClick={() => openImageViewer(index)}
            />
          ))}
        </div>
      </div>

      {isViewerOpen && (
        <div className="img-viewer">
          <ImageViewer
            disableScroll
            src={images?.map((image) => image?.url)}
            closeOnClickOutside
            currentIndex={currentImage}
            backgroundStyle={{
              height: "95%",
              marginTop: "60px",
            }}
            onClose={() => {
              setIsViewerOpen(false);
            }}
          />
        </div>
      )}
    </Modal>
  );
};
