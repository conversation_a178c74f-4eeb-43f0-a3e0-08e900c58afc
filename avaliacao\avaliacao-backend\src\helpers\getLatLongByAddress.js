const NodeGeocoder = require('node-geocoder')

const getLatLongByAddress = async address => {
  const options = {
    provider: 'google',
    apiKey: process.env.GOOGLE_API_KEY,
    formatter: null,
  }

  const geocoder = NodeGeocoder(options)
  const res = await geocoder.geocode(address)

  const hasStreet = res.find(data => data.streetName)
  if (hasStreet) return hasStreet

  return res.length ? res[0] : {}
}

module.exports = { getLatLongByAddress }
