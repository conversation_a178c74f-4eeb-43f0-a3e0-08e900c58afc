name: Deploy test

on:
  push:
    branches: [test]

jobs:
  wait_for_previous_deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Check for Running Deployments
        id: check_deploy
        run: |
          while : ; do
            RUNNING_WORKFLOWS=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
            "https://api.github.com/repos/${{ github.repository }}/actions/runs?branch=homolog&status=in_progress" \
            | jq -r '.workflow_runs[] | select(.name == "Deploy test") | .id')

            if [ -z "$RUNNING_WORKFLOWS" ]; then
              echo "No running deployments found. Proceeding with new deployment."
              break
            else
              echo "Found a running deployment with ID $RUNNING_WORKFLOWS. Waiting for it to finish..."
              sleep 30
            fi
          done

  build_deploy:
    runs-on: ubuntu-latest
    needs: wait_for_previous_deploy
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID_AMPLIFY }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY_AMPLIFY }}
      AWS_DEFAULT_REGION: sa-east-1
      AWS_DEFAULT_OUTPUT: json
      APP_ID: ${{ secrets.AWS_AMPLIFY_APP_ID }}
      BRANCH_NAME: test

    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: AWS Amplify Start Job
        id: StartJob
        run: |
          jobId=$(aws amplify start-job --app-id $APP_ID --branch-name $BRANCH_NAME --job-type RELEASE | jq -r '.jobSummary.jobId')
          echo "jobId=$jobId" >> $GITHUB_ENV

      - name: AWS Amplify Build
        run: |
          while [[ "$(aws amplify get-job --app-id $APP_ID --branch-name $BRANCH_NAME --job-id ${{ env.jobId }} | jq -r '.job.summary.status')" =~ ^(PENDING|RUNNING)$ ]]; do sleep 1; done
          RESULT="$(aws amplify get-job --app-id $APP_ID --branch-name $BRANCH_NAME --job-id ${{ env.jobId }} | jq -r '.job.steps | .[] | select(.stepName == "BUILD") | .status')"
          echo "Build result: $RESULT"
          LOG_URL="$(aws amplify get-job --app-id $APP_ID --branch-name $BRANCH_NAME --job-id ${{ env.jobId }} | jq -r '.job.steps | .[] | select(.stepName == "BUILD") | .logUrl')"
          if [[ $RESULT == "SUCCEED" ]]; then
            echo "Build succeeded."
            echo "Step logs in Amplify:"
            curl $LOG_URL
          else
            echo "Build failed."
            echo "Step logs in Amplify:"
            curl $LOG_URL
            exit 1
          fi

      - name: AWS Amplify Deploy
        run: |
          RESULT="$(aws amplify get-job --app-id $APP_ID --branch-name $BRANCH_NAME --job-id ${{ env.jobId }} | jq -r '.job.steps | .[] | select(.stepName == "DEPLOY") | .status')"
          echo "Deploy result: $RESULT"
          LOG_URL="$(aws amplify get-job --app-id $APP_ID --branch-name $BRANCH_NAME --job-id ${{ env.jobId }} | jq -r '.job.steps | .[] | select(.stepName == "DEPLOY") | .logUrl')"
          if [[ $RESULT == "SUCCEED" ]]; then
            echo "Deploy succeeded."
            echo "Step logs in Amplify:"
            curl $LOG_URL
          else
            echo "Deploy failed."
            echo "Step logs in Amplify:"
            curl $LOG_URL
            exit 1
          fi

      - name: Send success notification to Discord
        if: success()
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
        uses: Ilshidur/action-discord@master
        with:
          args: |
            Deploy efetuado com sucesso (avaliacao-frontend / test)
            PR: ${{ github.event.pull_request.title || 'Direct Push - No PR' }}
            Autor: ${{ github.actor }}

  error:
    runs-on: ubuntu-latest
    needs: build_deploy
    if: failure()
    steps:
      - name: Notify Discord on failure
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
        uses: Ilshidur/action-discord@master
        with:
          args: |
            Deploy falhou (avaliacao-frontend / test)
            PR: ${{ github.event.pull_request.title || 'Direct Push - No PR' }}
            Autor: ${{ github.actor }}
