const { Router } = require('express')
const router = Router()
const isAuth = require('../../../middlewares/isAuth')

const InvoiceNoteClass = require('../../../controllers/nota-entrada')
const InvoiceNotesControllers = new InvoiceNoteClass()

router.get('/getInvoicesNotes', isAuth, InvoiceNotesControllers.getInvoiceNotes)

router.post('/registerEntryInvoice', isAuth, InvoiceNotesControllers.registerInvoiceNote)

router.delete('/delete-invoice-note/:invoiceId', isAuth, InvoiceNotesControllers.deletedInvoiceNote)

router.put('/update-invoice-note/:invoiceId', isAuth, InvoiceNotesControllers.updateInvoiceNotes)

module.exports = router
