const PrinterModel = require('../../models/Printer');

class PrintersClass {
  constructor() { }

  async listAll(req, res) {
    const { enterprise: { cnpj, } } = req.usuario;

    if (!cnpj) {
      return res.status(400).json({
        error: true,
        message: 'Usuário sem CNPJ vinculado!'
      });
    };

    const printersList = await PrinterModel.find({ cnpj, active: true });
    return res.status(200).json(printersList)
  };
};

module.exports = PrintersClass;
