const ReviewModel = require('../../models/Reviews')
const EvaluationModel = require('../../models/Avaliacao')
const { decrypt, encrypt } = require('../../utils/crypto');

class ReviewsClass {
  async addReview(req, res) {
    const {
      type,
      referenceId,
      cpf,
      name,
      points,
      evaluator,
      observation,
    } = decrypt(req.body.data || {}, process.env.PRIVATE_KEY);

    try {
      const createdReview = await ReviewModel.create({
        type,
        referenceId,
        cpf,
        name,
        points,
        evaluator,
        observation,
      })

      await EvaluationModel.findOneAndUpdate({ _id: referenceId }, {
        evaluatorName: evaluator,
        evaluationPoints: points
      })

      return res.status(200).json({
        data: encrypt({
          status: true,
          review: createdReview,
        }, process.env.PRIVATE_KEY).toString()
      })
    } catch (error) {
      console.log(error)
      return res.status(400).json({
        status: false,
        error: error
      })
    }
  }
}

module.exports = ReviewsClass
