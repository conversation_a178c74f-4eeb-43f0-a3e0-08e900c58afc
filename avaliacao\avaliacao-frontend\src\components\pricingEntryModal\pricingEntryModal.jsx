import React from "react";
import Modal from "react-modal";
import ApiService from "../../services/ApiService";
import Swal from "sweetalert2";

import * as FontAwesomeIcon from "react-icons/fa";
import { toast } from "react-toastify";
import "./index.scss";
import { generateBarCodeItem } from "../../utils/avaliationUtils";
import { PrintUrl } from "../../services/PrintRoute";
import { removingDecimalPlacesUtils } from "../../utils/removingDecimalPlacesUtils";
import { calculateDefValueUtils } from "../../utils/calculateDefValueUtils";
import { formatDefValueUtils } from "../../utils/formatDefValueUtils";
import { checksIfValuesAreIntegers } from "../../utils/checksIfValuesAreIntegers";
import { AuthContext } from "../../context/auth";
import { verifyPlatform } from "../../utils/platform";
import GroupItemsModal from "../pricingModal/GroupItemsModal";
import { removeDuplicatesFromArray } from "../../utils/removeDuplicatesFromArray";
import * as BsIcons from "react-icons/bs";
import { Tooltip as ReactTooltip } from "react-tooltip";
import { DexieDB } from "../../config/Dexie";
import { handleWithRoundingNumbersGreaterThan100 } from "../../utils/handleWithRoundingNumbersGreaterThan100Utils";
import { invalidNumbers } from "../../utils/invalidNumbersUtils";
import { IoRefreshCircleOutline } from "react-icons/io5";
import { calcGiracreditValue } from "../../utils/calcGiracreditValue";

const modalStyles = {
  content: {
    width: "900px",
    top: "50%",
    left: "50%",
    right: "50%",
    bottom: "auto",
    marginRight: "-50%",
    transform: "translate(-50%, -50%)",
    borderRadius: "10px",
    height: "50em",
    maxHeight: "80%",
    maxWidth: "80%",
    zIndex: 1000,
  },
  overlay: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    zIndex: 999,
  },
};

class PricingEntryModal extends React.Component {
  static contextType = AuthContext;

  constructor(props) {
    super(props);
    this.state = {
      show: false,
      printProducts: [],
      defValueProduct: "",
      isAllPrecified: false,
      defaultValuesOfEvaluationProducts: [],
      productItems: [],
      profitMargin: 0,
      concatItemsModalIsOpen: false,
    };
  }

  async componentDidUpdate(prevProps) {
    if (prevProps.showModal !== this.props.showModal && this.props.showModal) {
      this.setState({ show: this.props.showModal });
      this.buildProductsOfEvaluation();
    }
  }

  handleExcludeItemsConcat(index) {
    const product = this.state.printProducts[index];
    let newProductsArray = this.state.printProducts.filter(
      (product, idx) => idx !== index
    );

    const productQtd = product?.qtd;

    for (let i = 0; i < productQtd; i++) {
      newProductsArray.push({
        ...product,
        qtd: 1,
      });
    }

    this.setState({
      printProducts: newProductsArray.sort((a, b) =>
        String(a.produtoInternoDescricao).localeCompare(
          b.produtoInternoDescricao
        )
      ),
      defaultValuesOfEvaluationProducts: newProductsArray.sort((a, b) =>
        String(a.produtoInternoDescricao).localeCompare(
          b.produtoInternoDescricao
        )
      ),
    });
  }

  handleConcatItems(selectedProduct, qtd) {
    let productsArray = [...this.state.printProducts];

    let newProductsArray = [];

    const selectedProductsArray = productsArray.filter(
      (product) =>
        String(product.produtoInternoDescricao).toLowerCase() ===
        selectedProduct && product?.qtd === 1
    );
    const unselectedProductsArray = productsArray.filter(
      (product) =>
        String(product.produtoInternoDescricao).toLowerCase() !==
        selectedProduct || product?.qtd !== 1
    );

    if (selectedProductsArray?.length < qtd) {
      return toast.error(
        "Quantidade a agrupar não pode ser maior que a quantidade de produtos."
      );
    }

    const qtdRest = selectedProductsArray?.length - qtd;

    newProductsArray.push({
      ...selectedProductsArray[0],
      valorMin: Number(selectedProductsArray[0]?.valorMin),
      valorMax: Number(selectedProductsArray[0]?.valorMax),
      qtd: qtd,
      percentage: 0,
      isEnabledPercentage: false,
      giraCreditDiscount: 0,
    });

    for (let i = 0; i < qtdRest; i++) {
      newProductsArray.push({
        ...selectedProductsArray[0],
        valorMin: Number(selectedProductsArray[0]?.valorMin),
        valorMax: Number(selectedProductsArray[0]?.valorMax),
        qtd: 1,
        percentage: 0,
        isEnabledPercentage: false,
        giraCreditDiscount: 0,
      });
    }

    newProductsArray.push(...unselectedProductsArray);

    this.setState({
      printProducts: newProductsArray.sort((a, b) =>
        String(a.produtoInternoDescricao).localeCompare(
          b.produtoInternoDescricao
        )
      ),
      defaultValuesOfEvaluationProducts: newProductsArray.sort((a, b) =>
        String(a.produtoInternoDescricao).localeCompare(
          b.produtoInternoDescricao
        )
      ),
      concatItemsModalIsOpen: false,
    });
  }

  async buildProductsOfEvaluation() {
    const { data } = this.props;

    let productsItems = [];

    for (let product of data?.products) {
      if (this.props.modalType !== 'automatic') {
        if (product.qtd > 1) {
          for (let i = 0; i < product.qtd; i++) {
            productsItems.push({
              ...product,
              valorMin: Number(product.valorMin),
              valorMax: Number(product.valorMax),
              qtd: 1,
              percentage: 0,
              isEnabledPercentage: false,
              giraCreditDiscount: this.props.modalType === 'default' ? 0 : product?.valorGiracredito,
              defValueProduct: this.props.modalType === 'default' ? 0 : product?.valorDef,
              valorUnitario: Math.round(product?.valorUnitario),
            });
          }
        } else {
          productsItems.push({
            ...product,
            valorMin: Number(product.valorMin),
            valorMax: Number(product.valorMax),
            percentage: 0,
            isEnabledPercentage: false,
            giraCreditDiscount: this.props.modalType === 'default' ? 0 : product?.valorGiracredito,
            defValueProduct: this.props.modalType === 'default' ? 0 : product?.valorDef,
            valorUnitario: Math.round(product?.valorUnitario),
            precificado: false,
          });
        }
      } else {
        productsItems.push({
          ...product,
          valorMin: Number(product.valorMin),
          valorMax: Number(product.valorMax),
          percentage: 0,
          isEnabledPercentage: false,
          giraCreditDiscount: this.props.modalType === 'default' ? 0 : product?.valorGiracredito,
          defValueProduct: this.props.modalType === 'default' ? 0 : product?.valorDef,
          valorUnitario: Math.round(product?.valorUnitario),
          precificado: false,
          qtd: product?.qtd
        });
      }
    }

    this.setState({
      // defaultValuesOfEvaluationProducts: data.products,
      defaultValuesOfEvaluationProducts: [...productsItems],
      printProducts: productsItems,
    });
  }

  printItems = async () => {
    const printProducts = this.state.printProducts.filter(
      (product) => product.precificado
    );

    if (printProducts.length === 0) {
      toast.error("Nenhum Produto selecionado para impressão", {
        position: "top-center",
        autoClose: 2000,
        hideProgressBar: false,
        closeOnClick: true,
      });
      return;
    }

    if (printProducts.some((product) => !product.defValueProduct)) {
      toast.error("Por favor, preencha todos os valores", {
        position: "top-center",
        autoClose: 2000,
        hideProgressBar: false,
        closeOnClick: true,
      });
      return;
    }

    const { user } = this.context;
    const operationSystem = verifyPlatform();
    const [printPayload, printPayloadWithDiscount] = this.printItensPayload();

    if (operationSystem !== "Windows") {
      const [printerOrigins] = await DexieDB.printerOrigins.toArray();

      const origin = printerOrigins
        .filter((item) => item.description === "ETIQUETA_ENTRADA_V2")
        .shift();

      if (printPayload.Itens.length) {
        const dataPrint = {
          ownerPrinterCnpj: user?.enterprise?.cnpj,
          typeId: origin._id,
          data: printPayload,
        };

        try {
          await ApiService.CreatePipeline(dataPrint);
        } catch (err) {
          console.log(err);
        }
      }

      if (printPayloadWithDiscount.Itens.length) {
        const originGiracredito = response.printOrigins
          .filter((item) => item.description === "ETIQUETA_GIRA_CREDITO_V2")
          .shift();

        const printItens = printPayloadWithDiscount.Itens.map((product) => ({
          descricao: product.produtoInternoDescricao,
          qtd: product.qtd,
          codbarras: product.codbarras,
          valor: product.valor.replace(".", "").replace(",", "."),
          valor_giracredito: product.giraCreditDiscount
            .replace(".", "")
            .replace(",", "."),
        }));

        const dataPayload = {
          ownerPrinterCnpj: user?.enterprise?.cnpj,
          typeId: originGiracredito._id,
          codunidade: printPayloadWithDiscount.codunidade,
          data: {
            codunidade: printPayloadWithDiscount.codunidade,
            Itens: printItens,
          },
        };

        try {
          await ApiService.CreatePipeline(dataPayload);
        } catch (err) {
          console.log(err);
        }
      }
    } else {
      if (printPayload.Itens.length) {
        try {
          await PrintUrl.PrintItemCupomV2(printPayload);
        } catch (err) {
          console.log(err);
        }
      }

      if (printPayloadWithDiscount.Itens.length) {
        const printItens = printPayloadWithDiscount.Itens.map((product) => ({
          descricao: product.descricao,
          qtd: product.qtd,
          codbarras: product.codbarras,
          valor: product.valor.replace(".", "").replace(",", "."),
          valor_giracredito: product.giraCreditDiscount
            .replace(".", "")
            .replace(",", "."),
        }));

        await PrintUrl.PrintGiraCreditoV2({
          codunidade: printPayloadWithDiscount.codunidade,
          Itens: printItens,
        });
      }
    }

    toast.success(`Impressão realizada com sucesso`, {
      autoClose: 3000,
      closeOnClick: false,
      pauseOnHover: false,
      position: "top-center",
    });
  };

  buildUnityCode(product) {
    const { user } = this.context;

    let finalValueProcuct = "";
    let unityCode;
    let codUnd = user?.enterprise?.codUnd;

    const valorDef = String(Math.floor(product.defValueProduct));

    if (valorDef.length === 1) {
      finalValueProcuct = `000${valorDef.replace(".", "")}00`;
    } else if (valorDef.length === 2) {
      finalValueProcuct = `00${valorDef.replace(".", "")}00`;
    } else if (valorDef.length === 3) {
      finalValueProcuct = `0${valorDef.replace(".", "")}00`;
    } else {
      finalValueProcuct = `${valorDef.replace(".", "")}00`;
    }

    if (String(codUnd).length === 1) {
      unityCode = `000${codUnd}`;
    } else if (String(codUnd).length === 2) {
      unityCode = `00${codUnd}`;
    } else {
      unityCode = `0${codUnd}`;
    }

    let giraCreditDiscountCode = "";
    const giraCreditDiscount = String(
      Number(product.giraCreditDiscount).toFixed(0)
    );

    if (giraCreditDiscount.length === 1) {
      giraCreditDiscountCode = `000${giraCreditDiscount.replace(".", "")}00`;
    } else if (giraCreditDiscount.length === 2) {
      giraCreditDiscountCode = `00${giraCreditDiscount.replace(".", "")}00`;
    } else if (giraCreditDiscount.length === 3) {
      giraCreditDiscountCode = `0${giraCreditDiscount.replace(".", "")}00`;
    } else {
      giraCreditDiscountCode = `${giraCreditDiscount.replace(".", "")}00`;
    }

    let giraPercentageCode = "";
    const giraPercentage = String(product.percentage);
    if (giraPercentage.length === 1) {
      giraPercentageCode = `00${giraPercentage.replace(".", "")}`;
    } else if (giraPercentage.length === 2) {
      giraPercentageCode = `0${giraPercentage.replace(".", "")}`;
    } else {
      giraPercentageCode = `${giraPercentage.replace(".", "")}`;
    }

    return `${unityCode}${generateBarCodeItem(
      product
    )}${finalValueProcuct}${giraCreditDiscountCode}${giraPercentageCode}`;
  }

  printItensPayload = () => {
    const { user } = this.context;
    const items = this.state.printProducts
      .filter((product) => product.precificado)
      .filter((product) => Number(product.percentage) === 0)
      .filter((product) => !!Number(product.qtd))
      .filter((product) => !isNaN(Number(product.defValueProduct)))
      .filter((product) => Number(product?.valorGiracredito) > 0)
      .map((product) => {
        product = {
          ...product,
          valor: String(Number(product.defValueProduct).toFixed(2)).replace(
            ",",
            "."
          ),
          giraCreditDiscount: this.props.modalType === 'default' ? String(
            (
              Number(product.defValueProduct) *
              (Number(product.percentage) / 100 + 1)
            ).toFixed(2)
          ) : product?.valorGiracredito,
        };

        return {
          codbarras: this.buildUnityCode(product),
          descricao: product.produtoInternoDescricao,
          codunidade: user?.enterprise?.codUnd,
          qtd: product.qtd,
          valor: product.valor,
          percentage: Number(product.percentage),
          giraCreditDiscount: product.giraCreditDiscount,
        };
      });

    const itemsWithDiscount = this.state.printProducts
      .filter((product) => product.precificado)
      // .filter((product) => Number(product.percentage) !== 0)
      .filter((product) => Number(product.valorGiracredito) !== 0)
      .filter((product) => !!Number(product.qtd))
      .filter((product) => !isNaN(Number(product.defValueProduct)))
      .map((product) => ({
        codbarras: this.buildUnityCode(product),
        descricao: product.produtoInternoDescricao,
        valor: String(Number(product.defValueProduct).toFixed(2)).replace(
          ".",
          ","
        ),
        codunidade: user?.enterprise?.codUnd,
        qtd: product.qtd,
        percentage: Number(product.percentage),
        giraCreditDiscount: Number(this.props.modalType === 'default' ? product.giraCreditDiscount : product?.valorGiracredito)
          .toFixed(2)
          .toString()
          .replace(".", ","),
      }));

    return [
      {
        codunidade: user?.enterprise?.codUnd,
        Itens: items,
      },
      {
        codunidade: user?.enterprise?.codUnd,
        Itens: itemsWithDiscount,
      },
    ];
  };

  saveNewValues = async () => {
    this.setState({
      ...this.state,
      isEnabledPercentage: false,
    });

    return this.props.handleOpenClosePricingModal(false);
  };

  setIsEditProduct = async (index) => {
    this.setState({ isEditProduct: !this.state.isEditProduct });
    document.getElementById(`td-${index}`).style.display = "none";
    document.getElementById(`edit-product-value${index}`).style.display =
      "block";
    document.getElementById(`edit-product-value${index}`).focus();
    document.getElementById(`pen-${index}`).style.display = "none";
    document.getElementById(`check-${index}`).style.display = "block";
  };

  setIsEditProfitProduct = async (index) => {
    this.setState({ isEditProduct: !this.state.isEditProduct });
    document.getElementById(`profit-input-${index}`).style.display =
      "block";
    document.getElementById(`profit-label-${index}`).style.display =
      "none";
    document.getElementById(`edit-product-value${index}`).focus();
    document.getElementById(`pen-profit-${index}`).style.display = "none";
    document.getElementById(`check-profit-${index}`).style.display = "block";
  };

  setNewDefValueProduct = (product, index) => {
    const { user } = this.context;
    const defValueProduct = formatDefValueUtils(this.state.defValueProduct);
    const printProducts = this.state.printProducts;

    let valueToSet = defValueProduct;
    let unityCode = "";
    let codUnd = user?.enterprise?.codUnd;

    let finalValueProcuct = "";
    let finalDiscountProduct = "";

    valueToSet = Number(defValueProduct);

    if (!checksIfValuesAreIntegers(valueToSet)) return;

    this.setState({
      ...this.state,
      defValueProduct: defValueProduct,
    });

    printProducts[index].defValueProduct = String(`${valueToSet}`);
    this.state.defaultValuesOfEvaluationProducts[index].defValueProduct =
      Number(valueToSet).toFixed(2);
    printProducts[index].isEnabledPercentage = true;
    printProducts[index].giraCreditDiscount = 0;
    printProducts[index].percentage = 0;

    if (String(valueToSet).length === 1) {
      finalValueProcuct = `00${valueToSet}00`;
    } else if (String(valueToSet).length === 2) {
      finalValueProcuct = `0${valueToSet}00`;
    } else if (String(valueToSet).length === 3) {
      finalValueProcuct = `${valueToSet}0`;
    } else {
      finalValueProcuct = `${valueToSet}`;
    }

    if (String(codUnd).length === 1) {
      unityCode = `000${codUnd}`;
    } else if (String(codUnd).length === 2) {
      unityCode = `00${codUnd}`;
    } else {
      unityCode = `0${codUnd}`;
    }

    this.state.defaultValuesOfEvaluationProducts.forEach((itemFromList) => {
      if (
        itemFromList.descricao === product.description &&
        itemFromList.ativo
      ) {
        const productValue = `0${finalValueProcuct}`;
        const productDiscount = `0${finalDiscountProduct}`;
        const finalBarCodeWithDiscount = `${unityCode}${generateBarCodeItem(
          itemFromList
        )}${productValue}${productDiscount}`;
        product.codBarras = finalBarCodeWithDiscount;
      }
    });

    this.state.defaultValuesOfEvaluationProducts[index].precificado = true;


    const value = this.state.defaultValuesOfEvaluationProducts[index]?.defValueProduct
    let giracreditValue = calcGiracreditValue(Number(value));

    this.state.defaultValuesOfEvaluationProducts[index].giraCreditDiscount = giracreditValue < 0 ? 0 : giracreditValue;

    const profitMargin = ((value - this.state.defaultValuesOfEvaluationProducts[index]?.valorUnitario) / this.state.defaultValuesOfEvaluationProducts[index]?.valorUnitario) * 100

    this.state.defaultValuesOfEvaluationProducts[index].profitMargin = Math.round(Number(Number(profitMargin).toFixed(0)));

    this.setState({
      printProducts: printProducts,
      defaultValuesOfEvaluationProducts:
        this.state.defaultValuesOfEvaluationProducts,
      defValueProduct: "",
    });

    document.getElementById(`td-${index}`).style.display = "";
    document.getElementById(`edit-product-value${index}`).style.display =
      "none";
    document.getElementById(`pen-${index}`).style.display = "block";
    document.getElementById(`check-${index}`).style.display = "none";
  };

  setProductPrint(index) {
    const { defaultValuesOfEvaluationProducts, printProducts } = this.state;
    defaultValuesOfEvaluationProducts[index].precificado =
      !defaultValuesOfEvaluationProducts[index].precificado;

    // printProducts[index].precificado = printProducts[index].precificado

    this.setState({ defaultValuesOfEvaluationProducts, printProducts });
  }

  handleSetPercentage(product, index, value) {
    const { printProducts } = this.state;

    if (!checksIfValuesAreIntegers(value)) return;

    printProducts[index].percentage = value;

    this.setState({
      ...this.state,
      printProducts: printProducts,
    });
  }

  handleSetDefinedValue(product, index, value) {
    if (!checksIfValuesAreIntegers(value)) return;

    const { printProducts } = this.state;

    printProducts[index].profitMargin = removingDecimalPlacesUtils(value);

    this.setState({
      ...this.state,
      printProducts: printProducts,
    });
  }

  handleCalculateDefinedValue(product, index) {
    const { printProducts } = this.state;

    if (!printProducts?.[index]) {
      return false;
    }

    let { profitMargin, valorUnitario } = printProducts[index];

    if (!profitMargin) {
      profitMargin = 0;
    }

    if (!valorUnitario) {
      valorUnitario = 0;
    }

    const valueCalculate = formatDefValueUtils(
      Math.round(valorUnitario * (profitMargin / 100 + 1))
    );

    const value = valueCalculate
    let giracreditValue = calcGiracreditValue(Number(value));

    this.state.defaultValuesOfEvaluationProducts[index].giraCreditDiscount = giracreditValue < 0 ? 0 : giracreditValue;

    printProducts[index].defValueProduct = valueCalculate;
    printProducts[index].valorDef = printProducts[index].defValueProduct;
    printProducts[index].precificado = true;
    printProducts[index].isEnabledPercentage = true;
    printProducts[index].giraCreditDiscount = giracreditValue;
    printProducts[index].percentage = 0;

    document.getElementById(`profit-label-${index}`).style.display = "block";
    document.getElementById(`profit-input-${index}`).style.display = "none";
    document.getElementById(`pen-profit-${index}`).style.display = "block";
    document.getElementById(`check-profit-${index}`).style.display = "none";

    this.setState({
      ...this.state,
      printProducts: printProducts,
    });
  }

  // handleCalculateDefValueWithPercentage(product, index) {
  //   const { printProducts } = this.state

  //   if (printProducts[index].valorDef === 0) {
  //     printProducts[index].valorDef = printProducts[index].defValueProduct
  //   }

  //   printProducts[index].giraCreditDiscount = Math.round(
  //     printProducts[index].valorDef -
  //       printProducts[index].valorDef * (printProducts[index].percentage / 100)
  //   )

  //   if (printProducts[index].percentage === 0) return

  //   this.setState({
  //     ...this.state,
  //     printProducts: printProducts,
  //   })
  // }

  handleCalculateDefValueWithPercentage(product, index, event) {
    const { printProducts, defaultValuesOfEvaluationProducts } = this.state;

    const productSelected = defaultValuesOfEvaluationProducts.find(
      (p) => p._id === product._id
    );

    const formatPrintProducts = calculateDefValueUtils(
      productSelected,
      printProducts,
      index,
      event
    );

    this.setState({
      ...this.state,
      printProducts: formatPrintProducts,
    });
  }

  handleGiraCreditDiscount(product, index, value) {
    const { printProducts } = this.state;

    if (!checksIfValuesAreIntegers(value)) return;

    printProducts[index].giraCreditDiscount = value;

    this.setState({
      ...this.state,
      printProducts: printProducts,
    });
  }

  setProfitMarginForAllProducts() {
    this.state.printProducts.forEach((product, index) => {
      this.handleSetDefinedValue(product, index, this.state.printProducts?.[0]?.profitMargin || 0)
      this.handleCalculateDefinedValue(product, index)
    })
  }

  render() {
    return (
      <>
        <Modal
          isOpen={this.props.showModal}
          style={modalStyles}
          ariaHideApp={false}
          className=""
        >
          <div style={{ textAlign: "center" }}>
            <h1 className="title-modal">Definição de Valores</h1>
          </div>
          <hr />
          <div className="div-with-scroll pricing-entry">
            <div className="div-parent-with-scroll">
              <table>
                <thead>
                  <tr>
                    {this.props.modalType !== 'automatic' &&
                      <th></th>
                    }
                    <th>Descrição</th>
                    <th>Qtd</th>
                    {this.props.modalType === 'default' &&
                      <>
                        <th>Valor Custo</th>
                        <th>Margem Lucro (%)</th>
                        <th>
                          <button
                            onClick={() => this.setProfitMarginForAllProducts()}
                            style={{
                              border: 'none',
                              outline: 'none',
                              backgroundColor: 'transparent'
                            }}
                          >
                            <IoRefreshCircleOutline size={25} />
                          </button>
                        </th>
                      </>
                    }
                    <th>{this.props.modalType === 'default' ? 'Valor' : 'Preço'} Def</th>
                    {this.props.modalType === 'default' && <th></th>}
                    <th>
                      {this.props.modalType === 'default' ? 'Valor' : 'Preço'}
                      <br />
                      {import.meta.env.VITE_GIRACREDITO}
                    </th>
                    {/* {this.props.modalType === 'default' &&
                      <th>{import.meta.env.VITE_GIRACREDITO} (%)</th>
                    } */}
                    <th />
                    <th>Imprimir</th>
                  </tr>
                </thead>
                <tbody>
                  {this.state.printProducts?.map((product, index) => (
                    <tr key={index}>
                      {this.props.modalType !== 'automatic' &&
                        <td>
                          {product?.qtd > 1 ? (
                            <>
                              <button
                                id={`exclude-items-concat-${index}`}
                                className="exlude-items-concat"
                                data-tooltip-content="Desfazer concatenação de itens"
                                onClick={() =>
                                  this.handleExcludeItemsConcat(index)
                                }
                              >
                                <BsIcons.BsX size={20} color="#FFF" />
                              </button>
                              <ReactTooltip
                                anchorId={`exclude-items-concat-${index}`}
                              />
                            </>
                          ) : null}
                        </td>
                      }
                      <td>
                        {product.produtoInternoDescricao || product.description}
                      </td>
                      <td>{product.qtd}</td>
                      {this.props.modalType === 'default' &&
                        <>
                          <td>
                            {Number(product.valorUnitario).toLocaleString("pt-br", {
                              style: "currency",
                              currency: "BRL",
                            })}
                          </td>
                          <td id={`edit-product${index}`} style={{ display: 'flex', alignItems: 'center', gap: '5px', justifyContent: 'center' }}>
                            <span
                              id={`profit-label-${index}`}
                            >
                              {product?.profitMargin || 0}
                            </span>

                            <input
                              className="input-percentage"
                              inputMode="numeric"
                              type="text"
                              placeholder="(%) aaa"
                              value={product.profitMargin}
                              style={{ display: 'none' }}
                              id={`profit-input-${index}`}
                              onChange={(e) => {
                                this.handleSetDefinedValue(
                                  product,
                                  index,
                                  e.target.value
                                );
                              }}
                            />

                            <div className="item-modal-edit">
                              <FontAwesomeIcon.FaPencilAlt
                                id={`pen-profit-${index}`}
                                color="black"
                                cursor="pointer"
                                onClick={() => this.setIsEditProfitProduct(index)}
                              />
                              <FontAwesomeIcon.FaCheck
                                id={`check-profit-${index}`}
                                style={{ display: "none" }}
                                color="green"
                                cursor="pointer"
                                onClick={(event) =>
                                  this.handleCalculateDefinedValue(
                                    product,
                                    index,
                                    event
                                  )
                                }
                              />
                            </div>
                          </td>
                          <td></td>
                        </>
                      }
                      <td id={`td-${index}`}>
                        {Number(product.defValueProduct || 0).toLocaleString(
                          "pt-br",
                          {
                            style: "currency",
                            currency: "BRL",
                          }
                        )}
                      </td>
                      <td
                        id={`edit-product-value${index}`}
                        style={{ display: "none" }}
                      >
                        <input
                          type="text"
                          value={this.state.defValueProduct}
                          onChange={(e) => {
                            if (!checksIfValuesAreIntegers(e.target.value))
                              return;
                            this.setState({ defValueProduct: e.target.value });
                          }}
                        />
                      </td>
                      {this.props.modalType === 'default' &&
                        <td>
                          <div className="item-modal-edit">
                            <FontAwesomeIcon.FaPencilAlt
                              id={`pen-${index}`}
                              color="black"
                              cursor="pointer"
                              onClick={() => this.setIsEditProduct(index)}
                            />
                            <FontAwesomeIcon.FaCheck
                              id={`check-${index}`}
                              style={{ display: "none" }}
                              color="green"
                              cursor="pointer"
                              onClick={() =>
                                this.setNewDefValueProduct(product, index)
                              }
                            />
                          </div>
                        </td>
                      }

                      {/* <td id={`edit-product${index}`}>
                        <input
                          className="input-percentage"
                          type="text"
                          placeholder="(%)"
                          value={product.percentage}
                          onChange={e => {
                            this.handleSetPercentage(product, index, e.target.value)
                          }}
                          disabled={!product.isEnabledPercentage}
                          onBlur={event =>
                            this.handleCalculateDefValueWithPercentage(product, index, event)
                          }
                        />
                      </td>
                      <td id={`td-${index}`}>
                        {Number(product.giraCreditDiscount || 0).toLocaleString('pt-br', {
                          style: 'currency',
                          currency: 'BRL',
                        })}
                      </td> */}

                      <td id={`td-${index}`} style={{}}>
                        <input
                          className="input-percentage"
                          type="text"
                          id="input-gira-value"
                          inputMode="numeric"
                          placeholder="(R$)"
                          value={product.giraCreditDiscount}
                          onChange={(e) => {
                            this.handleGiraCreditDiscount(
                              product,
                              index,
                              e.target.value
                            );
                          }}
                          // disabled={!product.isEnabledPercentage}
                          disabled
                          onBlur={(event) =>
                            this.handleCalculateDefValueWithPercentage(
                              product,
                              index,
                              event
                            )
                          }
                        />
                      </td>
                      {/* {this.props.modalType === 'default' &&
                        <td id={`edit-product${index}`}>
                          <input
                            className="input-percentage"
                            inputMode="numeric"
                            id="input-percentage"
                            type="text"
                            placeholder="(%)"
                            value={product.percentage}
                            onChange={(e) => {
                              this.handleSetPercentage(
                                product,
                                index,
                                e.target.value
                              );
                            }}
                            disabled={!product.isEnabledPercentage}
                            onBlur={(event) =>
                              this.handleCalculateDefValueWithPercentage(
                                product,
                                index,
                                event
                              )
                            }
                          />
                        </td>
                      } */}

                      <td></td>
                      <td>
                        <input
                          type="checkbox"
                          value={product.precificado}
                          checked={product.precificado}
                          onChange={() => this.setProductPrint(index)}
                          style={{ cursor: "pointer" }}
                        />
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          <div className="modal-footer pricing-entry">
            <div style={{ marginTop: '20px', width: '100%', background: "#FFF" }}>
              {this.props.modalType !== 'automatic' &&
                <button
                  className="concatenate-button"
                  onClick={() => this.setState({ concatItemsModalIsOpen: true })}
                >
                  Agrupar itens
                </button>
              }
            </div>
            <div className="button-print pricing">
              <button onClick={this.printItems} id="button-print-itens">
                Imprimir
              </button>
              <button
                id="button-save-itens"
                style={{
                  cursor: "pointer",
                  backgroundColor: "red",
                }}
                onClick={() => {
                  Swal.fire({
                    title: "Deseja realmente sair da precificação?",
                    icon: "question",
                    html: '<span  style="color: red;">todos os itens serão perdidos.</span>',
                    showCancelButton: true,
                    cancelButtonText: "Não",
                    confirmButtonColor: "#3085d6",
                    cancelButtonColor: "#d33",
                    confirmButtonText: "Sim",
                    allowEscapeKey: false,
                    allowEnterKey: false,
                    allowOutsideClick: false,
                    stopKeydownPropagation: false,
                  }).then((result) => {
                    if (result.isConfirmed) {
                      this.props.handleOpenClosePricingModal(false);
                      this.setState({
                        ...this.state,
                        isEnabledPercentage: false,
                      });
                    }
                  });
                }}
              >
                Cancelar
              </button>
            </div>
            <GroupItemsModal
              isOpen={this.state.concatItemsModalIsOpen}
              onClose={() => this.setState({ concatItemsModalIsOpen: false })}
              products={removeDuplicatesFromArray(
                this.state.printProducts,
                "description"
              )?.map((product) => ({
                label: product?.produtoInternoDescricao || product?.descricao,
                value: String(product.produtoInternoDescricao || product?.descricao).toLowerCase(),
                qtd:
                  this.state.printProducts?.filter(
                    (p) =>
                      p?.qtd === 1 && p?.description === product?.description
                  ).length || 0,
              }))}
              onConcatClick={(selectedProduct, qtd) =>
                this.handleConcatItems(selectedProduct, qtd)
              }
            />
          </div>
        </Modal>
      </>
    );
  }
}

export default PricingEntryModal;
