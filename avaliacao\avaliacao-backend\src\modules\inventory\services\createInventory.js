const inventoryApi = require('../config/api')
const CompanyModel = require('../../../models/Empresa')

const createInventory = async cnpj => {
  const company = await CompanyModel.findOne({ cnpj })

  if (!company) {
    throw new Error('Company not found')
  }

  const payload = {
    company: {
      _id: company._id,
      cnpj: company.cnpj,
      fantasyName: company.nomeReduzido,
      socialName: company.razaoSocial,
      codeUnity: company.codUnd,
      address: `${company.endereco}, ${company.numero} - ${company.bairro}`,
      city: company.cidade,
      state: company.estado,
    },
    products: [],
  }

  return inventoryApi.createInventory(payload)
}

module.exports = {
  createInventory,
}
