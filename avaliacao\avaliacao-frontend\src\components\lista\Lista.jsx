import { useEffect, useMemo, useState } from 'react'
import './Lista.css'

import * as FontAwesomeIcon from 'react-icons/fa'
import moment from 'moment'
import Tabs from '../Tabs'
import { Tooltip as ReactTooltip } from 'react-tooltip'
import { verifyPlatform } from '../../utils/platform'
import { isDesktop, isMobile } from 'react-device-detect'
import { DexieDB } from '../../config/Dexie'

const Lista = ({
  nvl,
  listaNvl,
  descricao,
  valor,
  valorMin,
  valorMax,
  peso,
  coeficiente,
  limitValue,
  handleChange,
  buscaAmarrados,
  editaValor,
  deleteItemNvl,
  adicionaNivel,
  showModal,
  showTabs,
  setAutomaticPrecify,
  automaticPrecify,
  productsWithIndexingTime,
  pointValue,
  ultimoNvl,
  pesquisaFoto,
  novo,
  visualizar,
  locacao,
  marca,
  modelo,
  pesquisa,
  valor2Semanas,
  valor4Semanas,
}) => {
  const [selectedFiles, setSelectedFiles] = useState([])
  const [fixedPoints, setFixedPoints] = useState(false)
  const [searchProductMenuIsOpen, setSearchProductMenuIsOpen] = useState(false)

  const platform = verifyPlatform()

  const levelsToDisplay = useMemo(() => {
    return listaNvl
  }, [listaNvl])

  return (
    <div id={`lista${nvl}`} className="lista" style={{ margin: '0.5rem 0rem' }}>
      <h1>Nível {nvl}</h1>
      {levelsToDisplay.map((item, index) => {
        const productIndexingTime = productsWithIndexingTime?.find(product => product?._id === item?._id)
        let statusInGCP = null

        if (productIndexingTime) {
          if (moment.utc(productIndexingTime?.indexingTime).toDate() < moment.utc().subtract({ hours: 3 }).toDate()) {
            statusInGCP = 'indexed'
          } else {
            statusInGCP = 'indexing'
          }
        }

        return (
          <div className="item-lista" key={index}>
            {/* {`nivel${nvl + 1}` === 'nivel5' ? (
              <div
                id={`lista${nvl}${index}`}
                className="item-nvl"
                onClick={() => buscaAmarrados(`nivel${nvl}`, item, index)}>
                {item.descricao}
              </div>
            ) : ( */}
            <div
              id={`lista${nvl}${index}`}
              className="item-nvl"
              onClick={() => buscaAmarrados(`nivel${nvl + 1}`, item, index)}
              style={{ display: 'flex', gap: '1rem', justifyContent: 'center', alignItems: 'center' }}
            >
              {item.descricao}
              {statusInGCP !== null &&
                <FontAwesomeIcon.FaCheckCircle
                  id={`status-gcp-tip-${index}`}
                  data-tooltip-content={statusInGCP === 'indexing'
                    ? 'Este produto ainda está sendo indexado, portanto, não será possível utilizar a Inteligência Artificial para buscá-lo enquanto isso! Aguarde!'
                    : 'Produto indexado na inteligência artificial, pronto para pesquisa por imagem.'
                  }
                  color={statusInGCP === 'indexing' ? '#ffe88e' : '#a4ffab'}
                />
              }
              <ReactTooltip anchorId={`status-gcp-tip-${index}`} place={nvl === 1 ? 'right' : nvl === 2 || nvl === 3 ? 'top' : 'left'} />
            </div>
            {/* )} */}
            <button
              className="btn-edit"
              id={`btn-edit${index}`}
              onClick={() => editaValor(item, nvl)}>
              <FontAwesomeIcon.FaPencilAlt style={{ color: 'white' }} />
            </button>
            <button className="btn-delete" onClick={() => deleteItemNvl(item, `nivel${nvl}`)}>
              <FontAwesomeIcon.FaTrash style={{ color: 'white' }} />
            </button>
          </div>
        )
      })}
      <input
        id={`input-desc${nvl}`}
        placeholder="Nome do produto"
        className="descricao"
        name={`descricao${nvl}`}
        onChange={handleChange}
        value={descricao}
      />
      <div className="label" id={`checkbox${nvl}`} style={{ marginTop: '1rem' }}>
        Último Nível:
      </div>
      <div className="label" id={`checkbox${nvl}`}>
        <input
          type="radio"
          name={`ultimoNvl${nvl}`}
          value={'sim'}
          style={{ marginRight: '5px' }}
          onChange={handleChange}
        />
        <label className="label-checkbox">Sim</label>
        <input
          type="radio"
          name={`ultimoNvl${nvl}`}
          value={'não'}
          style={{ margin: '0 5px 0 1rem' }}
          onChange={handleChange}
        />
        <label className="label-checkbox">Não</label>
      </div>

      {/* GOOGLE LENS */}
      {/* {nvl == 2 && ultimoNvl === 'não' && (
        <>
          <div className="label" id={`imageSearch${nvl}`} style={{ marginTop: '1rem' }}>
            Pesquisar por foto:
          </div>
          <div className="label" id={`imageSearch${nvl}`}>
            <input
              type="radio"
              name={`pesquisaFoto${nvl}`}
              value={'sim'}
              style={{ marginRight: '5px' }}
              onChange={handleChange}
            />
            <label className="label-imageSearch">Sim</label>
            <input
              type="radio"
              name={`pesquisaFoto${nvl}`}
              value={'não'}
              style={{ margin: '0 5px 0 1rem' }}
              onChange={handleChange}
            />
            <label className="label-imageSearch">Não</label>
          </div>
        </>
      )} */}
      {((ultimoNvl === 'sim') || (nvl == '4' && listaNvl?.length > 0)) && (
        <>
          <div style={{ marginTop: '1rem' }}>
            Item novo:
          </div>
          <div>
            <input
              type="radio"
              name={`itemNovo${nvl}`}
              value={'sim'}
              style={{ marginRight: '5px' }}
              onChange={handleChange}
            />
            <label>Sim</label>
            <input
              type="radio"
              name={`itemNovo${nvl}`}
              value={'não'}
              style={{ margin: '0 5px 0 1rem' }}
              onChange={handleChange}
            />
            <label>Não</label>
          </div>

          <div className="label" style={{ marginTop: '1rem' }}>
            Deseja visualizar na avaliação?:
          </div>
          <div className="label">
            <input
              type="radio"
              name={`visualizarAvaliacao${nvl}`}
              value={'sim'}
              style={{ marginRight: '5px' }}
              onChange={handleChange}
              checked={visualizar === 'sim'}
            />
            <label className="label-checkbox">Sim</label>
            <input
              type="radio"
              name={`visualizarAvaliacao${nvl}`}
              value={'não'}
              style={{ margin: '0 5px 0 1rem' }}
              onChange={handleChange}
              checked={visualizar === 'não'}
            />
            <label className="label-checkbox">Não</label>
          </div>
        </>
      )}

      <div className="label" id={`cbxpesq${nvl}`} style={{ marginTop: '1rem' }}>
        Item de Pesquisa:
      </div>
      <div className="label" id={`cbxpesq${nvl}`} style={{ marginBottom: '20px' }}>
        <input
          type="radio"
          name={`pesquisa${nvl}`}
          value={'sim'}
          style={{ marginRight: '5px' }}
          onChange={handleChange}
        />
        <label className="label-checkbox">Sim</label>
        <input
          type="radio"
          name={`pesquisa${nvl}`}
          value={'não'}
          style={{ margin: '0 5px 0 1rem' }}
          onChange={handleChange}
        />
        <label className="label-checkbox">Não</label>
      </div>

      {/* GOOGLE LENS */}
      {/* {(ultimoNvl === 'não') && (pesquisaFoto === 'sim') && nvl == 2 && (
        <div style={{ marginTop: '1rem', display: 'flex', flexFlow: 'column', gap: '.3rem' }}>
          <strong>Imagens do produto</strong>
          <button
            className='select-images-button'
            onClick={() => setSearchProductMenuIsOpen(!searchProductMenuIsOpen)}
          >
            Anexar
          </button>
          {selectedFiles?.length ? (
            <div>
              <span>{`${selectedFiles?.length} Arquivos selecionados`}</span>
              <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginTop: '1rem', flexWrap: 'wrap' }}>
                {selectedFiles?.map((file, index) => {
                  const fileUrl = URL.createObjectURL(file)

                  return (
                    <div>
                      <img
                        src={fileUrl}
                        alt="Imagem do produto selecionado"
                        width={100}
                        height={100}
                      />
                      <button
                        className="delete-attached-image-button"
                        onClick={() => setSelectedFiles(selectedFiles?.filter((file, idx) => idx !== index))}
                      >
                        <FontAwesomeIcon.FaTrash size={15}/>
                      </button>
                    </div>
                  )
                })}
              </div>
            </div>
          ) : null}

          {searchProductMenuIsOpen &&
            <div
              className="attach-product-menu-list"
            >
              <label
                className={`${!isMobile ? 'disabled' : ''}`}
                htmlFor='take-picture'
                style={{ borderBottom: '1px solid #DDD'}}
              >
                <FontAwesomeIcon.FaCamera />
                Tirar foto
              </label>
              <input
                id="take-picture"
                type="file"
                style={{ display: 'none' }}
                multiple={false}
                disabled={isDesktop}
                onChange={(e) => {
                  e.preventDefault()
                  setSearchProductMenuIsOpen(false)

                  if (!e.target.files[0]) {
                    return toast.error('Você precisa selecionar uma imagem para realizar a consulta!')
                  }

                  setSelectedFiles([...selectedFiles, ...Array.from(e.target.files)])
                }}
                accept="image/*"
                capture
              />
              <label htmlFor="select-photo">
                <FontAwesomeIcon.FaPhotoVideo />
                Selecionar foto
              </label>
              <input
                id="select-photo"
                type="file"
                style={{ display: 'none' }}
                multiple={true}
                onChange={(e) => {
                  e.preventDefault()
                  setSearchProductMenuIsOpen(false)

                  if (!e.target.files.length) {
                    return toast.error('Você precisa selecionar uma imagem para realizar a consulta!')
                  }

                  setSelectedFiles([...selectedFiles, ...Array.from(e.target.files)])
                }}
                accept=".jpg, .jpeg"
              />
            </div>
          }
        </div>
      )} */}

      {showTabs && (
        <>
          <>
            <div className="label" id={`cbxAccPontos${nvl}`} style={{ marginTop: '1rem', display: 'flex', gap: '.3rem' }}>
              Pontos fixos:
              <FontAwesomeIcon.FaQuestionCircle
                size={12}
                id={`fixed-points-${nvl}`}
                data-tooltip-content="Caso o produto seja com ponto fixo ele irá valer essa quantidade de pontos na hora da venda, se não fará parte da porcentagem da franquia"
              />
              <ReactTooltip anchorId={`fixed-points-${nvl}`} place="right" />
            </div>
            <div className="label" id={`cbxAccPontos${nvl}`} style={{ marginBottom: '20px' }}>
              <input
                type="radio"
                name={`fixedPoints${nvl}`}
                value={'sim'}
                style={{ marginRight: '5px' }}
                onChange={(e) => {
                  handleChange(e)
                  setFixedPoints(true)
                }}
                id="fixed-points-yes"
              />
              <label htmlFor="fixed-points-yes">Sim</label>
              <input
                type="radio"
                name={`fixedPoints${nvl}`}
                value={'não'}
                style={{ margin: '0 5px 0 1rem' }}
                onChange={(e) => {
                  handleChange(e)
                  setFixedPoints(false)
                }}
                id="fixed-points-no"
              />
              <label htmlFor="fixed-points-no">Não</label>
            </div>

            {fixedPoints &&
              <div style={{ display: 'flex', flexFlow: 'column', gap: '.5rem', marginBottom: '20px' }}>
                <label htmlFor="points-value">Valor dos pontos</label>
                <input
                  type="number"
                  name={`pointValue${nvl}`}
                  value={pointValue}
                  style={{ display: 'flex' }}
                  onChange={handleChange}
                  id='points-value'
                  className="descricao"
                  maxLength={3}
                />
              </div>
            }
          </>
          <Tabs
            tabTitles={['Sugestão de preço']}
            tabsContent={[
              {
                tabKey: 'Sugestão de preço',
                content: (
                  <>
                    <div className="input-line">
                      <div className='input-container'>
                        <label id={`label-valor${nvl}`} style={{ display: 'block' }}>Valor de Custo</label>
                        <input
                          id={`input-valor${nvl}`}
                          type="number"
                          min="1"
                          placeholder="Valor de Custo..."
                          className="descricao"
                          name={`valor${nvl}`}
                          onChange={handleChange}
                          value={valor}
                          style={{ display: 'block', width: '100%' }}
                        />
                      </div>
                      <div className='input-container'>
                        <label id={`label-valor${nvl}`} style={{ display: 'block' }}>Mínimo de venda</label>
                        <input
                          id={`input-valor${nvl}`}
                          type="number"
                          min="1"
                          placeholder="Valor Mínimo..."
                          className="descricao"
                          name={`valorMin${nvl}`}
                          onChange={handleChange}
                          value={valorMin}
                          style={{ display: 'block', width: '100%' }}
                        />
                      </div>
                    </div>
                    <div className="input-line">
                      <div className='input-container'>
                        <label id={`label-valor${nvl}`} style={{ display: 'block' }}>Máximo de venda</label>
                        <input
                          id={`input-valor${nvl}`}
                          type="number"
                          min="1"
                          placeholder="Valor Máximo..."
                          className="descricao"
                          name={`valorMax${nvl}`}
                          onChange={handleChange}
                          value={valorMax}
                          style={{ display: 'block', width: '100%' }}
                        />
                      </div>
                      <div className='input-container'>
                        <label id={`label-valor${nvl}`} style={{ display: 'block' }}>Peso do item</label>
                        <input
                          id={`input-valor${nvl}`}
                          type="number"
                          min="1"
                          placeholder="Peso..."
                          className="descricao"
                          name={`peso${nvl}`}
                          onChange={handleChange}
                          value={peso}
                          style={{ display: 'block', width: '100%' }}
                        />
                      </div>
                    </div>
                  </>
                )
              },
            ]}
          />
        </>
      )}
      <div className='input-container'>
        <label id={`label-coef${nvl}`} style={{ display: 'none' }}>Coeficiente do item</label>
        <input
          id={`input-coef${nvl}`}
          type="number"
          min="1"
          placeholder="Coeficiente..."
          className="descricao"
          name={`coeficiente${nvl}`}
          onChange={handleChange}
          value={coeficiente}
        />
      </div>
      <div className='input-container'>
        <label id={`label-limit-value${nvl}`} style={{ display: 'none' }}>Limite de valor</label>
        <input
          id={`input-limit-value${nvl}`}
          type="number"
          min="1"
          placeholder="Limite de valor..."
          className="descricao"
          name={`limitValue${nvl}`}
          onChange={handleChange}
          value={limitValue}
        />
      </div>
      {ultimoNvl === 'sim' && pesquisa === 'não' &&
        <>
          <div className="label" id={`location${nvl}`}>
            Produto para locação?
          </div>
          <div className="label" id={`location${nvl}`} style={{ marginBottom: '20px' }}>
            <input
              type="radio"
              name={`locacao${nvl}`}
              value={'sim'}
              style={{ marginRight: '5px' }}
              onChange={handleChange}
              id="location-yes"
            />
            <label className="label-checkbox" htmlFor={'location-yes'}>Sim</label>
            <input
              type="radio"
              name={`locacao${nvl}`}
              value={'não'}
              style={{ margin: '0 5px 0 1rem' }}
              onChange={handleChange}
              id="location-no"
            />
            <label className="label-checkbox" htmlFor={'location-no'}>Não</label>
          </div>

          {locacao === 'sim' &&
            <div style={{ display: 'flex', flexFlow: 'column', gap: '10px', width: '100%' }}>
              <div style={{ display: 'flex', gap: '20px', width: '100%' }}>
                <section style={{ width: '100%' }}>
                  <label className='label' htmlFor="">Marca</label>
                  <input
                    className="descricao"
                    style={{ display: 'block', width: '100%', maxWidth: '100%' }}
                    placeholder="Marca..."
                    name={`marca`}
                    onChange={handleChange}
                    value={marca}
                  />
                </section>
                <section style={{ width: '100%' }}>
                  <label className='label' htmlFor="">Modelo</label>
                  <input
                    className="descricao"
                    style={{ display: 'block', width: '100%', maxWidth: '100%' }}
                    placeholder="Modelo..."
                    name={`modelo`}
                    onChange={handleChange}
                    value={modelo}
                  />
                </section>
              </div>
              <div style={{ display: 'flex', gap: '20px', width: '100%' }}>
                <section style={{ width: '100%' }}>
                  <label className='label' htmlFor="">Valor 2 semanas</label>
                  <input
                    className="descricao"
                    style={{ display: 'block', width: '100%', maxWidth: '100%' }}
                    placeholder="Valor 2 semanas..."
                    name={`valor2Semanas${nvl}`}
                    onChange={handleChange}
                    value={valor2Semanas}
                    disabled
                  />
                </section>
                <section style={{ width: '100%' }}>
                  <label className='label' htmlFor="">Valor 4 semanas</label>
                  <input
                    className="descricao"
                    style={{ display: 'block', width: '100%', maxWidth: '100%' }}
                    placeholder="Valor 4 semanas..."
                    name={`valor4Semanas${nvl}`}
                    onChange={handleChange}
                    value={valor4Semanas}
                    disabled
                  />
                </section>
              </div>
            </div>
          }
        </>
      }
      <button
        id={`btn-adc${nvl}`}
        className="btn-adicionar"
        onClick={e => adicionaNivel(e, nvl, selectedFiles)}
        style={{ marginBottom: '0' }}>
        Adicionar
      </button>
      <button id={`btn-adc${nvl}`} className="btn-adicionar" onClick={() => showModal(nvl)}>
        Pré-selecionar
      </button>
    </div >
  )
}
export default Lista
