const { fetchSecrets } = require('./secretsService')

const loadSecrets = async () => {
  const secrets = await fetchSecrets()

  Object.keys(secrets).forEach(key => {
    if (!process.env[key]) {
      process.env[key] = secrets[key]
    } else {
      console.log(`Uma variável de ambiente já está definida e não será sobrescrita. ${key}`)
    }
  })

  return secrets
}

module.exports = {
  loadSecrets,
}
