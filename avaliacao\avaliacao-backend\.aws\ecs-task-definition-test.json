{"inferenceAccelerators": [], "containerDefinitions": [{"name": "avaliacao-backend", "resourceRequirements": null, "essential": true, "portMappings": [{"containerPort": "8080", "protocol": "tcp"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/avaliacao-backend-test", "awslogs-region": "sa-east-1", "awslogs-stream-prefix": "ecs"}}}], "volumes": [], "networkMode": "awsvpc", "memory": "956", "cpu": "2048", "family": "ecs-avaliacao-backend-task-definition-test", "placementConstraints": []}