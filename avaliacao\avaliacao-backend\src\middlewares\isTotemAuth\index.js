const jwt = require('jsonwebtoken')
const routerPaths = require('../validationRoutersConfig/index')
const RefreshTokenModel = require('../../models/RefreshToken')
const { SECRET_JWT } = require('../../config/jwt')
const moment = require('moment')

module.exports = async (request, response, next) => {
  const { authorization: auth } = request.headers || {}

  if (!auth) {
    return response.status(401).json({
      code: 130,
      message: 'Token não definido!',
    })
  }

  const [, token] = auth.split(' ')

  try {
    const decodeJWT = jwt.verify(token, SECRET_JWT)
    request.usuario = decodeJWT

    const refreshToken = await RefreshTokenModel.findOne({ id_user: decodeJWT?._id }).lean()

    if (
      !refreshToken ||
      refreshToken?.accessToken?.[0] !== token ||
      moment.utc().isAfter(moment.utc(refreshToken?.expiredAt))
    ) {
      return response.status(401).json({
        code: 130,
        message: 'Token invalido!',
      })
    }

    next()
  } catch (e) {
    return response.status(401).json({
      code: 130,
      message: 'Token invalido!',
    })
  }
}
