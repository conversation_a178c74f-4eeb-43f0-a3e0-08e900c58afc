import React, { Component } from 'react'

import '../../pages/lista_niveis/Lista_niveis.css'

class Input_lista_nvs extends Component {
  constructor(props) {
    super(props)
    this.state = {
      qtd: 1,
    }
  }

  componentDidMount = () => {
    const { qtd } = this.state
    this.props.recebeQtdInput(Number(qtd), this.props.index)
  }

  handleChange = ({ target }) => {
    const { name, value } = target
    this.setState({ [name]: value })
  }

  // quando perde o foco, envia o valor de qtd para lista niveis
  confirmaValor = () => {
    const { qtd } = this.state
    this.props.recebeQtdInput(Number(qtd), this.props.index)
  }

  resetaQtd = qtd => {
    this.setState({ qtd: qtd })
  }

  componentDidUpdate(prevProps) {
    if (prevProps.qtd !== this.props.qtd) this.setState({ qtd: this.props.qtd })
  }

  render() {
    const { qtd } = this.state

    return (
      <input
        id="qtd4"
        className="qtd"
        name="qtd"
        type="number"
        min="1"
        style={{ margin: '0' }}
        value={qtd}
        onChange={this.handleChange}
        onBlur={this.confirmaValor}
      />
    )
  }
}

export default Input_lista_nvs
