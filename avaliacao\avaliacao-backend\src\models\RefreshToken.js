const { model, Schema } = require("mongoose");
const moment = require('moment');

const currentDate = Date.now();

// Definindo Schema
const RefreshTokenSchema = new Schema({
  id_user: String,
  accessToken: Array,
  refreshToken: String,
  expiredAt: {
    type: Date,
    default: moment.utc().add({ days: 30 }).toDate(),
  },
  createdAt: {
    type: Date,
    default: currentDate,
  },
  updatedAt: {
    type: Date,
    default: currentDate,
  },
});

const RefreshTokenModel = model("refresh_tokens", RefreshTokenSchema);
module.exports = RefreshTokenModel;
