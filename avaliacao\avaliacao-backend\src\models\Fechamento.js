const { Schema, model } = require('mongoose')

// Definindo Schema
const FechamentoSchema = Schema({
  fechamento: {
    type: [Object],
    require: true,
  },

  idUsuario: {
    type: String,
    require: true,
  },

  // nivel1: {
  //     type: Object,
  //     require: true
  // },
  // nivel2: {
  //     type: Object,
  //     require: true
  // },
  // nivel3: {
  //     type: Object,
  //     require: true
  // },
  // nivel4: {
  //     type: Object,
  //     require: true
  // },
  // peso: {
  //     type: Number,
  // },
})

// Definindo collection
const Fechamento = model('fechamento', FechamentoSchema)
module.exports = Fechamento
