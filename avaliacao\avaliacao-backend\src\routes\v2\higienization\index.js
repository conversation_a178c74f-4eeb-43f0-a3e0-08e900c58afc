const { Router } = require('express');
const router = Router();

const isAuth = require('../../../middlewares/isAuth');

const HigienizationClass = require('../../../controllers/higienizations');
const HigienizationController = new HigienizationClass();

router.get('/', isAuth, HigienizationController.findAll);
router.post('/', isAuth, HigienizationController.create);
router.post('/remove', isAuth, HigienizationController.remove);
router.post('/discard', isAuth, HigienizationController.discard);

module.exports = router;
