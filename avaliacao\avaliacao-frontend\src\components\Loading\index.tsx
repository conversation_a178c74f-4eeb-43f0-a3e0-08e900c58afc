import React from 'react';
import { HashLoader } from 'react-spinners';
import './styles.css'

interface LoadingProps {
  isLoading: boolean;
}

const Loading: React.FC<LoadingProps> = ({ isLoading }) => {
  return (
    <>
      {isLoading && (
        <div className="loading-container">
          <HashLoader
            size={150}
            color={'#ffc107'}
            loading={isLoading}
          />
        </div>
      )}
    </>
  )
}

export default Loading;
