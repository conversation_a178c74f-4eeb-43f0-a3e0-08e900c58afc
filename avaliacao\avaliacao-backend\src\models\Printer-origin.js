const { model, Schema } = require('mongoose')

const PrinterOriginSchema = new Schema({
  description: {
    type: String,
  },
  origin_path: {
    type: String,
  },
  base_url: {
    type: String,
  },
  create_at: {
    type: Date,
    default: Date.now,
  },
  update_at: {
    type: Date,
    default: null,
  },
  delete_at: {
    type: Date,
    default: null,
  },
})

module.exports = model('printer_origin', PrinterOriginSchema)
