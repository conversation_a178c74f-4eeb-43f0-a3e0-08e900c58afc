const EnterpriseModel = require('../../models/Empresa')

class EnterprisesClass {
  constructor() {}

  async list(req, res) {
    const enterprises = await EnterpriseModel.find().lean()
    return res.status(200).json(enterprises)
  }

  async getByCnpj(req, res) {
    const { cnpj } = req.params
    const enterprise = await EnterpriseModel.findOne({
      cnpj,
    })
    return res.json(enterprise)
  }
}

module.exports = EnterprisesClass
