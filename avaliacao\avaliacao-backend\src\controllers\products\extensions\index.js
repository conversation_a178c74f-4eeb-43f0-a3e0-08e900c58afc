const Produto = require('../../../models/Produto')
const Nivel1 = require('../../../models/Nivel1')
const Nivel2 = require('../../../models/Nivel2')
const Nivel3 = require('../../../models/Nivel3')

async function geraProduto(req, nivelAtual) {
  const descricao = req && req.descricao ? req.descricao : ''

  // parte que verifica se é pesquisa
  let pesquisa, coeficiente, vlrCusto, vlrVenda, vlrMin, precificacao_automatica
  const nivel = req.nivel || `nivel${req.nvl}`
  if (!req.pesquisa || req.pesquisa === 'não') {
    pesquisa = false
    coeficiente = null
    vlrCusto = req.valor
    vlrVenda = req.valorMax
    vlrMin = req.valorMin
    precificacao_automatica = req.precificacao_automatica
  } else {
    pesquisa = true
    coeficiente = req.coeficiente
    vlrCusto = null
    vlrVenda = null
    vlrMin = null
  }

  const codBarras = await geraCod()

  const lastProduct = await Produto.find().limit(1).sort({ codigo: -1 })
  const codigo = Number(lastProduct?.[0]?.codigo) + 1;

  const produto = {
    vlrCusto,
    vlrVenda,
    vlrMin,
    codigo,
    codBarras,
    pesquisa,
    coeficiente,
    favorito: false,
    peso: Number(req.peso || 0),
    precificacao_automatica,
    acumulaPontos: req.acumulaPontos,
    tipoFixo: req.tipoFixo,
    valorPontosFixos: req.valorPontosFixos,
    limitValue: req?.limitValue,
    novo: req?.novo,
    visualizar: req?.visualizar,
    allowLocation: !!req?.locInfos,
    ...(req?.locInfos && ({
      locInfos: req?.locInfos,
    }))
    // pesquisaFoto: req?.pesquisaFoto || false,
  }

  // Provisory Flux
  let nvl1, nvl2, nvl3
  let descNvl1, descNvl2, descNvl3

  switch (nivel) {
    case 'nivel1':
      produto['descricao'] = descricao
      produto['nivel1'] = nivelAtual?._id || ''
      break
    case 'nivel2':
      nvl1 = await Nivel1.findById(nivelAtual?.chave)
      descNvl1 = nvl1?.descricao || ''

      produto['descricao'] = `${descNvl1} - ${descricao}`
      produto['nivel1'] = nvl1?._id || ''
      produto['nivel2'] = nivelAtual?._id || ''
      break
    case 'nivel3':
      nvl2 = await Nivel2.findById(nivelAtual?.chave)
      nvl1 = await Nivel1.findById(nvl2?.chave)
      descNvl2 = nvl2?.descricao || ''
      descNvl1 = nvl1?.descricao || ''

      produto['descricao'] = `${descNvl1} - ${descNvl2} - ${descricao}`
      produto['nivel1'] = nvl1?._id || ''
      produto['nivel2'] = nvl2?._id || ''
      produto['nivel3'] = nivelAtual?._id || ''
      break
    case 'nivel4':
      nvl3 = await Nivel3.findById(nivelAtual?.chave)
      nvl2 = await Nivel2.findById(nvl3?.chave)
      nvl1 = await Nivel1.findById(nvl2?.chave)
      descNvl3 = nvl3?.descricao || ''
      descNvl2 = nvl2?.descricao || ''
      descNvl1 = nvl1?.descricao || ''

      produto['descricao'] = `${descNvl1} - ${descNvl2} - ${descNvl3} - ${descricao}`
      produto['nivel1'] = nvl1?._id || ''
      produto['nivel2'] = nvl2?._id || ''
      produto['nivel3'] = nvl3?._id || ''
      produto['nivel4'] = nivelAtual?._id || ''
      break
    default:
      break
  }

  return produto
}

async function geraCod() {
  const lastProduct = await Produto.find().limit(1).sort({ codigo: -1 })
  const codigo = Number(lastProduct?.[0]?.codigo) + 1;

  // gera o codigo de barras essa parte
  let codigoBarra = 123000000000
  let um,
    dois,
    tres,
    quatro,
    cinco,
    seis,
    sete,
    oito,
    nove,
    dez,
    onze,
    doze,
    soma,
    multiplo,
    resultado
  codigoBarra += Number(codigo)
  codigoBarra = codigoBarra.toString()
  um = Number(codigoBarra.substring(0, 1))
  dois = Number(codigoBarra.substring(1, 2))
  tres = Number(codigoBarra.substring(2, 3))
  quatro = Number(codigoBarra.substring(3, 4))
  cinco = Number(codigoBarra.substring(4, 5))
  seis = Number(codigoBarra.substring(5, 6))
  sete = Number(codigoBarra.substring(6, 7))
  oito = Number(codigoBarra.substring(7, 8))
  nove = Number(codigoBarra.substring(8, 9))
  dez = Number(codigoBarra.substring(9, 10))
  onze = Number(codigoBarra.substring(10, 11))
  doze = Number(codigoBarra.substring(11, 12))
  soma =
    um * 1 +
    dois * 3 +
    tres * 1 +
    quatro * 3 +
    cinco * 1 +
    seis * 3 +
    sete * 1 +
    oito * 3 +
    nove * 1 +
    dez * 3 +
    onze * 1 +
    doze * 3
  soma = soma.toString()
  if (soma.slice(1) === '0') {
    codigoBarra += '0'
  } else {
    multiplo = Number(soma.slice(0, -1))
    multiplo = multiplo * 10 + 10
    resultado = multiplo - soma
    codigoBarra += resultado
  }
  return codigoBarra
}

module.exports = { geraProduto, geraCod }
