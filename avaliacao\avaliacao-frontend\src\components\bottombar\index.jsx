import { useEffect, useState } from 'react'

import './Bottombar.css'

import * as FontAwesomeIcon from 'react-icons/fa'
import ApiService from '../../services/ApiService'
import Swal from 'sweetalert2'
import { useAuth } from '../../context/auth'
import { parseCookies } from 'nookies'
import { useHistory } from 'react-router-dom'
const Bottombar = () => {
  const [state, setState] = useState({
    tipoUsuario: null
  })
  const { user } = useAuth()
  const history = useHistory()

  useEffect(() => {
    setState({
      tipoUsuario: user?.tipo
    })
  }, [])

  const verificaAvaliacaoEmAberto = async () => {
    const { token } = parseCookies()
    try {
      const avaluation = await ApiService.GetOpenAvaliation(
        user?.enterprise?.cnpj,
        user?._id,
        token
      )
      return avaluation
    } catch (e) {}
  }

  async function handleNavigation(pathUrl) {
    const chamouReforco = Boolean(localStorage.getItem('chamouReforco'))
    const verifyUrlAvaliation =
      window.location.pathname === '/lista_niveis' ||
      window.location.pathname === '/avaliacao' ||
      chamouReforco
    if (verifyUrlAvaliation) {
      if ((await verificaAvaliacaoEmAberto()) !== null) {
        Swal.fire(
          'Erro',
          'Não é possível mudar de página sem antes terminar a avaliação.',
          'error'
        )
        return
      }
    }
    history.push(pathUrl)
  }

  const { tipoUsuario } = state
  let ehAdm = false
  if (tipoUsuario === 'administrador') ehAdm = true

  return (
    <div id="sb" className="hidden-print">
      <a onClick={() => handleNavigation('/')} style={{}}>
        <FontAwesomeIcon.FaTachometerAlt
          style={{ color: '#b3b3b3', fontSize: '1.5rem' }}
        />
      </a>
      <a onClick={() => handleNavigation('/avaliacoes_em_aberto')}>
        <FontAwesomeIcon.FaCopy
          style={{ color: '#b3b3b3', fontSize: '1.5rem' }}
        />
      </a>
      <a onClick={() => handleNavigation('/operacoes')}>
        <FontAwesomeIcon.FaCogs
          style={{ color: '#b3b3b3', fontSize: '1.5rem' }}
        />
      </a>
      <a onClick={() => handleNavigation('/etiquetas')}>
        <FontAwesomeIcon.FaPrint
          style={{ color: '#b3b3b3', fontSize: '1.5rem' }}
        />
      </a>
      <a onClick={() => handleNavigation('/caixas')}>
        <FontAwesomeIcon.FaCashRegister
          style={{ color: '#b3b3b3', fontSize: '1.5rem' }}
        />
      </a>
      {ehAdm && (
        <>
          <a onClick={() => handleNavigation('/cadastra_clientes')}>
            <FontAwesomeIcon.FaUserPlus
              style={{ color: '#b3b3b3', fontSize: '1.5rem' }}
            />
          </a>
          <a onClick={() => handleNavigation('/cadastra_niveis')}>
            <FontAwesomeIcon.FaLayerGroup
              style={{ color: '#b3b3b3', fontSize: '1.5rem' }}
            />
          </a>
        </>
      )}
    </div>
  )
}

export default Bottombar
