const { Router } = require('express')
const router = Router()

const isAuth = require('../../../middlewares/isAuth')

const UsersClass = require('../../../controllers/users')
const UsersController = new UsersClass()

router.post('/login', UsersController.login)
router.post('/login/totem', UsersController.totemLogin)
router.get('/list', isAuth, UsersController.list)
router.get('/fetch', isAuth, UsersController.fetch)
router.post('/check_is_manager', isAuth, UsersController.checkIsManager)
router.post('/createUser', isAuth, UsersController.createUser)
router.post('/logout', isAuth, UsersController.logout)

module.exports = router
