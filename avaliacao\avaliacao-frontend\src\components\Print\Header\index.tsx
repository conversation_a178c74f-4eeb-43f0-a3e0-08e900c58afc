import { useAuth } from "../../../context/auth";

interface PrintHeaderProps {
  showCashierInfo: boolean;
  evaluationId?: string | null;
}

const PrintHeader: React.FC<PrintHeaderProps> = ({
  showCashierInfo = true,
  evaluationId = null,
}) => {
  const { user, cashier } = useAuth();

  return (
    <header
      style={{
        margin: "0.25rem 0 1rem",
        textAlign: "center",
        fontFamily: "'Poppins', sans-serif",
      }}
    >
      <h4 style={{ marginTop: 0 }}>{user?.enterprise?.nomeReduzido}</h4>
      <p style={{ fontSize: "12px", margin: 0 }}>
        CNPJ:{" "}
        {String(user?.enterprise?.cnpj).replace(
          /^(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/,
          "$1.$2.$3/$4-$5"
        )}
      </p>
      <p style={{ fontSize: "12px", margin: 0 }}>
        {user?.enterprise?.endereco}, {user?.enterprise?.numero} -{" "}
        {user?.enterprise?.bairro}
      </p>
      <p style={{ fontSize: "12px", margin: 0 }}>
        TEL: {user?.enterprise?.telefone}
      </p>
      {evaluationId && (
        <p style={{ fontSize: "12px", margin: 0 }}>
          ID Avaliação: ${evaluationId}
        </p>
      )}
      <p style={{ fontSize: "10px", margin: 0 }}>
        ---------------------------------------------
      </p>

      {showCashierInfo && (
        <>
          <p style={{ fontSize: "12px", margin: 0 }}>
            Data: {new Date().toLocaleString()}
          </p>
          <p style={{ fontSize: "12px", margin: 0 }}>
            ID Caixa: {cashier?._id}
          </p>
          <p style={{ fontSize: "10px", margin: 0 }}>
            ---------------------------------------------
          </p>
        </>
      )}
    </header>
  );
};

export default PrintHeader;
