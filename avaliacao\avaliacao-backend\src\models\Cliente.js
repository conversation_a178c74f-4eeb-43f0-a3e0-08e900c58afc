const { Schema, model } = require('mongoose')

// Definindo Schema
const ClienteSchema = Schema({
  nome: {
    type: String,
    require: true,
  },
  cpf: {
    type: String,
    require: true,
    index: true,
    unique: true,
  },
  dtNascimento: {
    type: Date,
    require: true,
  },
  celular: {
    type: String,
    require: true,
  },
  rua: {
    type: String,
    require: true,
  },
  bairro: {
    type: String,
    require: true,
  },
  numero: {
    type: Number,
    require: true,
  },
  cep: {
    type: String,
  },
  cod_uf: {
    type: String,
    require: true,
  },
  cod_cidade: {
    type: String,
    require: true,
  },
  estado: {
    type: String,
    require: true,
  },
  cidade: {
    type: String,
    require: true,
  },
  complemento: {
    type: String,
  },
  geo: {
    type: Object,
    require: false,
  },
  image: {
    type: String,
    require: false,
  },
  childrens: {
    type: Array,
    default: null,
  }
})

// Definindo collection
const Cliente = model('cliente', ClienteSchema)
module.exports = Cliente
