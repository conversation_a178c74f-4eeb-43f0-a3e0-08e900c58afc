import { useEffect, useMemo, useRef, useState } from 'react'

import { AiOutlineWarning, AiOutlineDollar } from 'react-icons/ai'
import { MdSystemUpdateAlt } from 'react-icons/md'
import Modal from 'react-modal'
import { toast } from 'react-toastify'

import './styles.scss'
import ApiService from '../../services/ApiService'
import { useAuth } from '../../context/auth'
import { useOffline } from '../../context/offline'
import { verifyPlatform } from '../../utils/platform'
import * as FaIcons from 'react-icons/fa'
import * as BiIcons from 'react-icons/bi'
import { useLocation } from 'react-router-dom'
import { Tooltip as ReactTooltip } from 'react-tooltip'
import { DexieDB } from '../../config/Dexie'
import { decrypt } from '../../utils/crypto'

const Navbar: React.FC = () => {
  const [state, setState] = useState({
    toggle: false,
    allMessages: [],
    showModal: false,
    showProfileModal: false,
  })
  const [userDevice, setUserDevice] = useState('Windows')
  const { pathname } = useLocation()

  const { signOut, user } = useAuth()
  const [screenWidth, setScreenWidth] = useState(window.innerWidth)
  const [isMobile, setIsMobile] = useState(window.innerWidth < 969)
  const [clientInfo, setClientInfo] = useState<any>(null);

  const navRef = useRef(null)
  const unitsRef = useRef(null)
  const exitRef = useRef(null)
  const notificationsModalRef = useRef<any>(null)

  const modalStyles = {
    content: {
      backgroundColor: '#1c1c1c',
      width: screenWidth <= 960 ? '400px' : '600px',
      left: screenWidth <= 960 ? '2%' : screenWidth <= 1300 ? '30%' : '50%',
      bottom: '42%',
      top: '70px',
      borderRadius: '10px',
      border: 0,
    },
    overlay: {
      backgroundColor: 'rgba(0,0,0,0.5)',
    },
  }

  const toggleNav = () => {
    if (!state.toggle) {
      // eslint-disable-next-line @typescript-eslint/no-extra-semi
      ;(navRef?.current ?? ({} as any)).style.paddingBottom = '110px'
      ;(exitRef.current ?? ({} as any)).style.display = 'block'
    } else {
      // eslint-disable-next-line @typescript-eslint/no-extra-semi
      ;(navRef?.current ?? ({} as any)).style.paddingBottom = '1rem'
      ;(exitRef.current ?? ({} as any)).style.display = 'none' as any
    }
    setState({ ...state, toggle: !state.toggle })
  }

  const openNotificationModal = () => {
    setState({ ...state, showModal: !state.showModal })
  }

  const getNotifications = async () => {
    try {
      const { data: response } = await ApiService.getAllMessages(user?.enterprise?.cnpj)
      const data = decrypt(response.data, import.meta.env.VITE_PRIVATE_KEY)
      data?.sort(
        (a: any, b: any) =>
          new Date(b.dataEnvio).getTime() - new Date(a.dataEnvio).getTime()
      )
      setState({ ...state, allMessages: data })
    } catch (e: any) {
      const msgError = 'Erro consultando mensagens'
      toast.error(e && e.message ? `${msgError}: ${e.message}` : msgError, {
        onClose: async () => {
          if (e && e.response && e.response.status === 401) {
            return signOut()
          }
        },
      })
    }
  }

  const returnTypeMessage = (type: any) => {
    if (type === 'atualizacao') {
      return <MdSystemUpdateAlt size={30} />
    } else if (type === 'aviso') {
      return <AiOutlineWarning size={30} />
    } else {
      return <AiOutlineDollar size={30} />
    }
  }

  const evaluationClient: any = useMemo(() => {
    let cliente = ''

    if (!['/lista_niveis', '/avaliacao'].includes(pathname)) return cliente

    cliente = `${clientInfo?.nome} - ${clientInfo?.cpf}`

    return cliente
  }, [pathname, clientInfo])

  useEffect(() => {
    if (user?._id) {
      getNotifications()
      setUserDevice(verifyPlatform())
    }
  }, [user])

  useEffect(() => {
    const getWindowSize = () => {
      setScreenWidth(window.innerWidth)
      setIsMobile(window.innerWidth < 969)
    }

    window.addEventListener('resize', getWindowSize)
    return () => window.removeEventListener('resize', getWindowSize)
  }, [])

  useEffect(() => {
    const getClientInfo = async () => {
      const [offlineEvaluation] = await DexieDB.evaluation.toArray();
      setClientInfo(offlineEvaluation?.cliente)
    }

    if (['/lista_niveis', '/avaliacao'].includes(pathname)) {
      getClientInfo()
    }

  }, [pathname])

  return (
    <div className="hidden-print" id="navbar">
      <ul className="nav" ref={navRef} style={{ height: screenWidth < 600 && evaluationClient?.length ? '70px' : undefined }}>
        <div className="responsive-nav">
          <a id="hamburguer" onClick={toggleNav}>
            <FaIcons.FaBars />
          </a>
          <li id="unidades" ref={unitsRef}>
            <p style={{ margin: 'auto', marginTop: '5px' }}>
              {user?.enterprise?.nomeUnidade}
            </p>
          </li>
          {screenWidth > 968 &&
            <div
            id="client"
            data-tooltip-content="Informações do fornecedor da avaliação"
            data-tooltip-place="top"
            style={{
              margin: 'auto',
              marginLeft: '2.5rem',
              display: evaluationClient ? 'inline-flex' : 'none',
              background: '#262626',
              padding: '4px 10px',
              borderRadius: '4px',
            }}
          >
            {evaluationClient}
          </div>
          }
          <a id="sair" ref={exitRef} onClick={() => signOut()}>
            Sair
            <FaIcons.FaSignOutAlt
              style={{ marginLeft: '0.5rem', marginRight: '0.5rem' }}
            />
          </a>
        </div>
        <div
          className="right-itens"
          style={{ display: 'flex', alignItems: 'center', flex: 1 }}
        >
          <li className="nav-item" style={{ padding: '0 10px' }}>
            <a
              href="https://api.whatsapp.com/send?phone=5519982183223"
              target="blank"
            >
              <FaIcons.FaWhatsapp style={{ fontSize: '20px' }} />
            </a>
            <div
              style={{
                marginTop: !isMobile ? '-5px' : '10px',
                fontSize: '13px',
                width: 'max-content',
              }}
            >
              {!isMobile && 'Suporte'}
            </div>
          </li>
          <li className="nav-item" style={{ padding: '0 10px' }}>
            <a>
              <FaIcons.FaBell
                style={{ fontSize: '20px' }}
                cursor="pointer"
                onClick={openNotificationModal}
              />
            </a>
            <div
              style={{
                marginTop: !isMobile ? '-5px' : '10px',
                fontSize: '13px',
                width: 'max-content',
              }}
            >
              {!isMobile && 'Notificações'}
            </div>
          </li>
          <li className="nav-item" style={{ padding: '0 10px' }}>
            <a>
              {userDevice === 'Windows' ? (
                <BiIcons.BiDesktop size={20} />
              ) : (
                <BiIcons.BiTab size={20} />
              )}
            </a>
            <div
              style={{
                fontSize: '13px',
                width: 'max-content',
                marginTop: !isMobile ? '-5px' : '10px',
                marginLeft: 'auto',
                marginRight: 'auto',
              }}
            >
              {!isMobile && <b> {userDevice === 'Windows' ? 'Computador' : 'Móvel'}</b>}
            </div>
          </li>
          <li className="nav-item" style={{ padding: '0 10px' }}>
            <a>
              <FaIcons.FaUserCircle
                style={{ fontSize: '20px' }}
                cursor="pointer"
              />
            </a>
            <div
              style={{
                marginTop: '-5px',
                fontSize: '13px',
              }}
            >
              {user?.usuario ?? 'Carregando...'}
            </div>
          </li>
          <li className="nav-item" style={{ padding: '0 10px' }}>
            <a id="btn-sair" onClick={() => signOut()} style={{ cursor: 'pointer' }}>
              <FaIcons.FaSignOutAlt style={{ fontSize: '20px' }} />
            </a>
          </li>
        </div>
        {screenWidth < 600 && evaluationClient?.length ?
          <div
            id="client"
            data-tooltip-content="Informações do fornecedor da avaliação"
            data-tooltip-place="top"
            style={{
              margin: 'auto',
              display: evaluationClient ? 'inline-flex' : 'none',
              background: '#262626',
              padding: '4px 10px',
              borderRadius: '4px',
              marginLeft: '80px',
            }}
          >
            {evaluationClient}
          </div>
         : null}
      </ul>
      <Modal
        ariaHideApp={false}
        isOpen={state.showModal}
        style={modalStyles}
        shouldCloseOnOverlayClick
        shouldCloseOnEsc
        onRequestClose={() => setState({ ...state, showModal: false })}
      >
        <div className="modal-container-message" ref={notificationsModalRef}>
          <div className="notificationsTitle">
            <h1>Histórico de Notificações</h1>
          </div>
          <div className="messages-history">
            {state.allMessages.map((message: any) => (
              <div className="message-single" key={message._id}>
                <div className="icon-message">
                  {returnTypeMessage(message.type)}
                </div>
                <div className="message" id="message">
                  <p>{message.message}</p>
                </div>
                <div className="visualized-date">
                  <p>{new Date(message.dataEnvio).toLocaleDateString()}</p>
                  <p>
                    {new Date(
                      new Date(message.dataEnvio).setHours(
                        new Date(message.dataEnvio).getHours() + 3
                      )
                    ).toLocaleTimeString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </Modal>
      <ReactTooltip anchorId="client" />
    </div>
  )
}

export default Navbar
