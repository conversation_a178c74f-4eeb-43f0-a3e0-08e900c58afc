import React, { Component } from 'react'
import '../../pages/avaliacao/impressao/Impressao.css'

class GeneratedPassword extends Component {
  constructor(props) {
    super(props)
    this.state = {}
  }
  render() {
    const { nomeCliente, generatedPassword } = this.props

    return (
      <div id="generatedPassword">
        <div className="impressao">
          <div className="ticket">
            <div className="centered">
              ---------------------------------
              <h1 style={{ fontWeight: 'bold', fontSize: '14px' }}>
                {'>>'} SENHA: {generatedPassword} {'<<'}
              </h1>
            </div>
            <div className="centered">
              ---------------------------------
              <p>Fornecedor: {nomeCliente}</p>
              <p>{new Date().toLocaleString()}</p>
              ---------------------------------
            </div>
          </div>
        </div>
      </div>
    )
  }
}

export default GeneratedPassword
