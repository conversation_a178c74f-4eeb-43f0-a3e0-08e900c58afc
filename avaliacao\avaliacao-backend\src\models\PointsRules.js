const { model, Schema } = require("mongoose");

// Definindo Schema
const PointsRulesSchema = new Schema({
  app: {
    type: String,
  },
  porcentage: {
    type: Number,
  },
  paymentConditionsValue: {
    money: {
      type: Number,
      default: null,
    },
    pix: {
      type: Number,
      default: null,
    },
    giracredit: {
      type: Number,
      default: null,
    },
  },
  valuePoints: {
    type: Number,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
});

const PointsRuleModel =  model("points_rule", PointsRulesSchema);
module.exports = PointsRuleModel;
