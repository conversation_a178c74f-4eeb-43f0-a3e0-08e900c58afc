{"taskDefinitionArn": "arn:aws:ecs:sa-east-1:898268897416:task-definition/ecs-avaliacao-backend-task-definition-prod:72", "containerDefinitions": [{"name": "avaliacao-backend", "image": "898268897416.dkr.ecr.sa-east-1.amazonaws.com/dfcom-avaliacao-prod:5d67ace9d4f7f6d25df27600db0f9eb4f5965af7", "cpu": 0, "portMappings": [{"name": "avaliacao-backend-8080-tcp", "containerPort": 8080, "hostPort": 8080, "protocol": "tcp"}], "essential": true, "environment": [{"name": "NEW_RELIC_LICENSE_KEY", "value": "42385e3d771b67a3b9a67e70cd152a58f2c8NRAL"}], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/avaliacao-backend-prod", "awslogs-region": "sa-east-1", "awslogs-stream-prefix": "ecs"}}}], "family": "ecs-avaliacao-backend-task-definition-prod", "networkMode": "awsvpc", "revision": 72, "volumes": [], "status": "ACTIVE", "requiresAttributes": [{"name": "com.amazonaws.ecs.capability.logging-driver.awslogs"}, {"name": "com.amazonaws.ecs.capability.ecr-auth"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.19"}, {"name": "com.amazonaws.ecs.capability.docker-remote-api.1.18"}, {"name": "ecs.capability.task-eni"}], "placementConstraints": [], "compatibilities": ["EC2"], "cpu": "1024", "memory": "956", "registeredAt": "2023-07-25T16:17:32.546Z", "registeredBy": "arn:aws:iam::898268897416:user/Leo-EasyIT", "tags": []}