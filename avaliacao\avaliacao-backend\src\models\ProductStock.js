const { Schema, model, Types } = require('mongoose')
const { getCurrentTime } = require('../utils')

const currentTime = getCurrentTime()

// Definindo Schema
const ProductStockSchema = Schema({
  productLocation: {
    type: Types.ObjectId,
    default: null,
  },
  cnpj: {
    type: String,
    default: null,
  },
  current: {
    type: Number,
    default: 0,
  },
  total: {
    type: Number,
    default: 0,
  },
  items: {
    type: Array,
    default: []
  },
  createdAt: {
    type: Date,
    default: currentTime,
  },
  updatedAt: {
    type: Date,
    default: null,
  },
})

// Definindo collection
const ProductStock = model('products_stock', ProductStockSchema, 'products_stock')
module.exports = ProductStock
