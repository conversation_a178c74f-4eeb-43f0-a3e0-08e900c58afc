const { Router } = require('express');
const router = Router();

const isAuth = require('../../../middlewares/isAuth');

const KitsClass = require('../../../controllers/kits');
const KitsController = new KitsClass();

router.post('/', isAuth, KitsController.create);
router.get('/', isAuth, KitsController.findAll);
router.delete('/:id', isAuth, KitsController.delete);
router.get('/:id', isAuth, KitsController.getItemFromKit);
router.patch('/addStock/:id', isAuth, KitsController.addStockToKit);

module.exports = router;
