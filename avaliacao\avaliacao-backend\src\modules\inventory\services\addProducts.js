const inventoryApi = require('../config/api')
const { createInventory } = require('./createInventory')
const { getProduct } = require('./getProduct')

const addProducts = async evaluation => {
  const { items, cnpj } = evaluation

  const inventory = await inventoryApi.getInventory(cnpj)

  if (!Object.keys(inventory?.company || {}).length) {
    await createInventory(cnpj)
  }

  const productsPromises = items.map(async item => {
    const product = await getProduct(item.idProduto)

    return {
      _id: product?._id || item?.idProduto,
      code: product.codigo,
      description: product.descricao,
      codeUnity: product.unidade || 'UN',
      quantity: item.qtd,
      salePrice: product.vlrVenda || item.precoBruto,
      costPrice: product.vlrCusto || item.precoBruto,
      barCode: product.codBarras,
      condicao: product.condicao || 'NAO INFORMADO',
      isActive: product.ativo,
    }
  })

  inventoryApi
    .addProduct(cnpj, await Promise.all(productsPromises))
    .then()
    .catch(err => console.log(err))
}

module.exports = { addProducts }
