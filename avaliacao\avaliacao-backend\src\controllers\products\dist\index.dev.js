'use strict'

function _slicedToArray(arr, i) {
  return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _nonIterableRest()
}

function _nonIterableRest() {
  throw new TypeError('Invalid attempt to destructure non-iterable instance')
}

function _iterableToArrayLimit(arr, i) {
  if (
    !(
      Symbol.iterator in Object(arr) || Object.prototype.toString.call(arr) === '[object Arguments]'
    )
  ) {
    return
  }
  var _arr = []
  var _n = true
  var _d = false
  var _e = undefined
  try {
    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {
      _arr.push(_s.value)
      if (i && _arr.length === i) break
    }
  } catch (err) {
    _d = true
    _e = err
  } finally {
    try {
      if (!_n && _i['return'] != null) _i['return']()
    } finally {
      if (_d) throw _e
    }
  }
  return _arr
}

function _arrayWithHoles(arr) {
  if (Array.isArray(arr)) return arr
}

function ownKeys(object, enumerableOnly) {
  var keys = Object.keys(object)
  if (Object.getOwnPropertySymbols) {
    var symbols = Object.getOwnPropertySymbols(object)
    if (enumerableOnly)
      symbols = symbols.filter(function (sym) {
        return Object.getOwnPropertyDescriptor(object, sym).enumerable
      })
    keys.push.apply(keys, symbols)
  }
  return keys
}

function _objectSpread(target) {
  for (var i = 1; i < arguments.length; i++) {
    var source = arguments[i] != null ? arguments[i] : {}
    if (i % 2) {
      ownKeys(source, true).forEach(function (key) {
        _defineProperty(target, key, source[key])
      })
    } else if (Object.getOwnPropertyDescriptors) {
      Object.defineProperties(target, Object.getOwnPropertyDescriptors(source))
    } else {
      ownKeys(source).forEach(function (key) {
        Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key))
      })
    }
  }
  return target
}

function _defineProperty(obj, key, value) {
  if (key in obj) {
    Object.defineProperty(obj, key, {
      value: value,
      enumerable: true,
      configurable: true,
      writable: true,
    })
  } else {
    obj[key] = value
  }
  return obj
}

function _classCallCheck(instance, Constructor) {
  if (!(instance instanceof Constructor)) {
    throw new TypeError('Cannot call a class as a function')
  }
}

function _defineProperties(target, props) {
  for (var i = 0; i < props.length; i++) {
    var descriptor = props[i]
    descriptor.enumerable = descriptor.enumerable || false
    descriptor.configurable = true
    if ('value' in descriptor) descriptor.writable = true
    Object.defineProperty(target, descriptor.key, descriptor)
  }
}

function _createClass(Constructor, protoProps, staticProps) {
  if (protoProps) _defineProperties(Constructor.prototype, protoProps)
  if (staticProps) _defineProperties(Constructor, staticProps)
  return Constructor
}

var _require = require('../products/extensions'),
  geraProduto = _require.geraProduto

var _require2 = require('mongoose'),
  Types = _require2.Types

var moment = require('moment')

var Produto = require('../../models/Produto')

var Nivel1 = require('../../models/Nivel1')

var Nivel2 = require('../../models/Nivel2')

var Nivel3 = require('../../models/Nivel3')

var Nivel4 = require('../../models/Nivel4')

var ProductStock = require('../../models/ProductStock')

var PrinterOrigin = require('../../models/Printer-origin')

var PrinterPipeline = require('../../models/Printer-pipeline')

var _require3 = require('../../services/loc/routes'),
  createProduct = _require3.createProduct

var _require4 = require('../../utils'),
  getCurrentTime = _require4.getCurrentTime

var ProductStockProduct = require('../../models/ProductStockProduct')

var ProductsClass =
  /*#__PURE__*/
  (function () {
    function ProductsClass() {
      _classCallCheck(this, ProductsClass)
    }

    _createClass(ProductsClass, [
      {
        key: 'registerProductFromEvaluation',
        value: function registerProductFromEvaluation(req, res) {
          var _ref,
            nivel,
            descricao,
            valor,
            valorMin,
            valorMax,
            peso,
            pesquisa,
            coeficiente,
            _ref$isVisible,
            isVisible,
            chave,
            _ref$vlrMin,
            vlrMin,
            precificacao_automatica,
            acumulaPontos,
            tipoFixo,
            valorPontosFixos,
            limitValue,
            pesquisaFoto,
            novo,
            visualizar,
            locInfos,
            token,
            _ref2,
            ultimoNvl,
            data,
            NiveisModel,
            acceptedNiveis,
            saveNivel,
            response,
            saveProduct,
            createdLocProduct,
            locProduct,
            createProductResponse

          return regeneratorRuntime.async(
            function registerProductFromEvaluation$(_context) {
              while (1) {
                switch ((_context.prev = _context.next)) {
                  case 0:
                    ;(_ref = req.body || {}),
                      (nivel = _ref.nivel),
                      (descricao = _ref.descricao),
                      (valor = _ref.valor),
                      (valorMin = _ref.valorMin),
                      (valorMax = _ref.valorMax),
                      (peso = _ref.peso),
                      (pesquisa = _ref.pesquisa),
                      (coeficiente = _ref.coeficiente),
                      (_ref$isVisible = _ref.isVisible),
                      (isVisible = _ref$isVisible === void 0 ? null : _ref$isVisible),
                      (chave = _ref.chave),
                      (_ref$vlrMin = _ref.vlrMin),
                      (vlrMin = _ref$vlrMin === void 0 ? null : _ref$vlrMin),
                      (precificacao_automatica = _ref.precificacao_automatica),
                      (acumulaPontos = _ref.acumulaPontos),
                      (tipoFixo = _ref.tipoFixo),
                      (valorPontosFixos = _ref.valorPontosFixos),
                      (limitValue = _ref.limitValue),
                      (pesquisaFoto = _ref.pesquisaFoto),
                      (novo = _ref.novo),
                      (visualizar = _ref.visualizar),
                      (locInfos = _ref.locInfos),
                      (token = _ref.token)
                    ;(_ref2 = req.body || {}), (ultimoNvl = _ref2.ultimoNvl)
                    ultimoNvl = String(ultimoNvl).toLowerCase()
                    data = _objectSpread(
                      {
                        descricao: descricao,
                        ultimoNvl: ultimoNvl,
                        valor: valor,
                        valorMin: valorMin,
                        valorMax: valorMax,
                        peso: Number(peso),
                        pesquisa: pesquisa || 'não',
                        coeficiente: coeficiente,
                        isVisible: isVisible,
                        precificacao_automatica: precificacao_automatica,
                        limitValue: limitValue,
                        novo: novo,
                        visualizar: visualizar,
                        allowLocation: !!locInfos,
                      },
                      locInfos && {
                        locInfos: locInfos,
                      }
                    ) // Format Data by Nivel

                    if (nivel !== 'nivel1') data['chave'] = chave
                    if (nivel === 'nivel4') data['vlrMin'] = vlrMin
                    NiveisModel = {
                      nivel1: Nivel1,
                      nivel2: Nivel2,
                      nivel3: Nivel3,
                      nivel4: Nivel4,
                    }
                    acceptedNiveis = Object.keys(NiveisModel)

                    if (acceptedNiveis.includes(nivel)) {
                      _context.next = 10
                      break
                    }

                    return _context.abrupt(
                      'return',
                      res.status(400).json({
                        status: true,
                        error: 'Nivel não existe',
                      })
                    )

                  case 10:
                    saveNivel = null // let existDescriptionInNivel = false
                    // try {
                    //   existDescriptionInNivel = !!(await NiveisModel[nivel]
                    //     .findOne({
                    //       descricao,
                    //     })
                    //     .lean())
                    // } catch (e) {}
                    // if (existDescriptionInNivel) {
                    //   return res.status(400).json({
                    //     status: true,
                    //     error: 'Já existe uma descrição semelhante neste nivel',
                    //   })
                    // }

                    _context.prev = 11
                    _context.next = 14
                    return regeneratorRuntime.awrap(new NiveisModel[nivel](data).save())

                  case 14:
                    saveNivel = _context.sent
                    _context.next = 19
                    break

                  case 17:
                    _context.prev = 17
                    _context.t0 = _context['catch'](11)

                  case 19:
                    if (saveNivel) {
                      _context.next = 21
                      break
                    }

                    return _context.abrupt(
                      'return',
                      res.status(400).json({
                        status: true,
                        error: 'Falha ao atualizar nivel!',
                      })
                    )

                  case 21:
                    response = {
                      nivel: saveNivel,
                      produto: {},
                    }
                    if (valorPontosFixos) req.body.valorPontosFixos = Number(valorPontosFixos)

                    if (!(ultimoNvl === 'sim' || pesquisaFoto)) {
                      _context.next = 43
                      break
                    }

                    _context.prev = 24
                    _context.next = 27
                    return regeneratorRuntime.awrap(geraProduto(req.body, saveNivel))

                  case 27:
                    saveProduct = _context.sent
                    createdLocProduct = {}

                    if (!(locInfos && token)) {
                      _context.next = 35
                      break
                    }

                    locProduct = {
                      name: locInfos?.name,
                      brand: locInfos?.brand,
                      model: locInfos?.model,
                      cost: Number(locInfos?.costValue),
                      barCode: saveProduct?.codBarras,
                      dynamicPrice: {
                        15: locInfos?.values[14],
                        30: locInfos?.values[28],
                      },
                      stockManagement: true,
                    }
                    _context.next = 33
                    return regeneratorRuntime.awrap(createProduct(locProduct, token))

                  case 33:
                    createProductResponse = _context.sent
                    // const stockData = {
                    //   product: createProductResponse,
                    //   cnpj: req?.usuario?.enterprise?.cnpj,
                    //   stock: {
                    //   }
                    // }
                    // const createProductStockResponse = await createProductLocation(stockData, token);
                    createdLocProduct = createProductResponse

                  case 35:
                    _context.next = 37
                    return regeneratorRuntime.awrap(
                      new Produto(
                        _objectSpread(
                          {},
                          saveProduct,
                          {},
                          createdLocProduct && {
                            locProductId: createdLocProduct?._id,
                          }
                        )
                      ).save()
                    )

                  case 37:
                    response['produto'] = _context.sent
                    _context.next = 43
                    break

                  case 40:
                    _context.prev = 40
                    _context.t1 = _context['catch'](24)
                    console.log(_context.t1)

                  case 43:
                    res.status(200).json(response)

                  case 44:
                  case 'end':
                    return _context.stop()
                }
              }
            },
            null,
            null,
            [
              [11, 17],
              [24, 40],
            ]
          )
        },
      },
      {
        key: 'search',
        value: function search(req, res) {
          var filter, limit, products, array
          return regeneratorRuntime.async(function search$(_context2) {
            while (1) {
              switch ((_context2.prev = _context2.next)) {
                case 0:
                  filter = req.query || {}
                  limit = req.query.limit || 100
                  filter.limit && delete filter.limit
                  filter.ativo = 'true'

                  if (filter?.all === 'false') {
                    filter.$or = [
                      {
                        create_at: {
                          $gte: moment().startOf('day').toDate(),
                        },
                      },
                      {
                        update_at: {
                          $gte: moment().startOf('day').toDate(),
                        },
                      },
                    ]
                  }

                  delete filter?.all
                  filter.version = {
                    $exists: false,
                  }
                  _context2.next = 9
                  return regeneratorRuntime.awrap(Produto.find(filter).limit(Number(limit)).lean())

                case 9:
                  products = _context2.sent

                  if (filter?.descricao) {
                    products = products?.filter(function (product) {
                      return String(product?.descricao)
                        .toLowerCase()
                        .includes(filter?.descricao?.toLowerCase())
                    })
                  }

                  array = []
                  products.forEach(function (produto) {
                    if (produto.codBarras === '1230000000000') {
                      array.push(produto)
                    }
                  })
                  products
                    .map(function (product) {
                      Number(String(product?.vlrVenda).split('.')[0])
                      return product
                    })
                    .sort(function (a, b) {
                      if (a.valorVenda > b.valorVenda) {
                        return 1
                      }

                      if (a.valorVenda < b.valorVenda) {
                        return -1
                      }

                      return 0
                    })
                    .map(function (elem) {
                      return array.push(elem)
                    })
                  return _context2.abrupt('return', res.status(200).json(array))

                case 15:
                case 'end':
                  return _context2.stop()
              }
            }
          })
        },
      },
      {
        key: 'searchById',
        value: function searchById(req, res) {
          var id, products
          return regeneratorRuntime.async(function searchById$(_context3) {
            while (1) {
              switch ((_context3.prev = _context3.next)) {
                case 0:
                  id = req.params.id
                  _context3.next = 3
                  return regeneratorRuntime.awrap(Produto.findById(id).lean())

                case 3:
                  products = _context3.sent
                  return _context3.abrupt('return', res.status(200).json(products))

                case 5:
                case 'end':
                  return _context3.stop()
              }
            }
          })
        },
      },
      {
        key: 'getFavoriteProducts',
        value: function getFavoriteProducts(req, res) {
          var products, array
          return regeneratorRuntime.async(function getFavoriteProducts$(_context4) {
            while (1) {
              switch ((_context4.prev = _context4.next)) {
                case 0:
                  _context4.next = 2
                  return regeneratorRuntime.awrap(
                    Produto.find({
                      favorito: true,
                      version: {
                        $exists: false,
                      },
                    }).lean()
                  )

                case 2:
                  products = _context4.sent
                  array = []
                  products.forEach(function (product) {
                    if (product.codBarras === '1230000000000') {
                      array.push(product)
                    }
                  })
                  products
                    .map(function (product) {
                      Number(String(product?.vlrVenda).split('.')[0])
                      return product
                    })
                    .sort(function (a, b) {
                      if (a.valorVenda > b.valorVenda) {
                        return 1
                      }

                      if (a.valorVenda < b.valorVenda) {
                        return -1
                      }

                      return 0
                    })
                    .map(function (elem) {
                      return array.push(elem)
                    })
                  return _context4.abrupt(
                    'return',
                    res.json({
                      status: true,
                      products: array,
                    })
                  )

                case 7:
                case 'end':
                  return _context4.stop()
              }
            }
          })
        },
      },
      {
        key: 'getIndexingProductsTime',
        value: function getIndexingProductsTime(req, res) {
          var indexingProducts, products
          return regeneratorRuntime.async(function getIndexingProductsTime$(_context5) {
            while (1) {
              switch ((_context5.prev = _context5.next)) {
                case 0:
                  _context5.next = 2
                  return regeneratorRuntime.awrap(
                    Produto.find({
                      indexingTime: {
                        $ne: null,
                      },
                    }).lean()
                  )

                case 2:
                  indexingProducts = _context5.sent
                  products = []
                  indexingProducts.forEach(function (product) {
                    if (product?.nivel4) {
                      products.push({
                        _id: product?.nivel4,
                        indexingTime: product?.indexingTime,
                      })
                      return
                    }

                    if (product?.nivel3) {
                      products.push({
                        _id: product?.nivel3,
                        indexingTime: product?.indexingTime,
                      })
                      return
                    }

                    if (product?.nivel2) {
                      products.push({
                        _id: product?.nivel2,
                        indexingTime: product?.indexingTime,
                      })
                      return
                    }

                    if (product?.nivel1) {
                      products.push({
                        _id: product?.nivel1,
                        indexingTime: product?.indexingTime,
                      })
                      return
                    }
                  })
                  return _context5.abrupt('return', res.status(200).json(products))

                case 6:
                case 'end':
                  return _context5.stop()
              }
            }
          })
        },
      },
      {
        key: 'printLocationProducts',
        value: function printLocationProducts(req, res) {
          var _req$body,
            codunidade,
            Itens,
            cnpj,
            productsToPrint,
            products,
            _iteratorNormalCompletion,
            _didIteratorError,
            _iteratorError,
            _iterator,
            _step,
            product,
            productExistOnStock,
            productToPrint,
            createdStock,
            createdProductInStock,
            newId,
            _createdStock,
            _createdProductInStock,
            _i,
            _Object$entries,
            _Object$entries$_i,
            key,
            value,
            a,
            origin

          return regeneratorRuntime.async(
            function printLocationProducts$(_context6) {
              while (1) {
                switch ((_context6.prev = _context6.next)) {
                  case 0:
                    ;(_req$body = req.body),
                      (codunidade = _req$body.codunidade),
                      (Itens = _req$body.Itens)
                    cnpj = req.usuario.enterprise.cnpj
                    productsToPrint = []
                    products = {}
                    _iteratorNormalCompletion = true
                    _didIteratorError = false
                    _iteratorError = undefined
                    _context6.prev = 7
                    _iterator = Itens[Symbol.iterator]()

                  case 9:
                    if ((_iteratorNormalCompletion = (_step = _iterator.next()).done)) {
                      _context6.next = 48
                      break
                    }

                    product = _step.value
                    _context6.next = 13
                    return regeneratorRuntime.awrap(
                      ProductStock.findOne({
                        productLocation: new Types.ObjectId(product?.locProductId),
                        cnpj: cnpj,
                      }).lean()
                    )

                  case 13:
                    productExistOnStock = _context6.sent
                    _context6.next = 16
                    return regeneratorRuntime.awrap(
                      Produto?.findOne({
                        locProductId: product?.locProductId,
                      }).lean()
                    )

                  case 16:
                    productToPrint = _context6.sent
                    _context6.prev = 17

                    if (!productExistOnStock) {
                      _context6.next = 29
                      break
                    }

                    _context6.next = 21
                    return regeneratorRuntime.awrap(
                      ProductStock.findOneAndUpdate(
                        {
                          _id: productExistOnStock?._id,
                        },
                        {
                          current: productExistOnStock?.current + product?.qtd,
                          total:
                            productExistOnStock?.total < productExistOnStock?.current + product?.qtd
                              ? productExistOnStock?.current + product?.qtd
                              : productExistOnStock?.total,
                          updatedAt: getCurrentTime(),
                        }
                      )
                    )

                  case 21:
                    createdStock = _context6.sent
                    _context6.next = 24
                    return regeneratorRuntime.awrap(
                      ProductStockProduct.create({
                        stock: new Types.ObjectId(createdStock?._id),
                        productLocation: new Types.ObjectId(product?.locProductId),
                        cnpj: cnpj,
                        barcode: productToPrint?.codBarras,
                        status: 'available',
                        images: [],
                      })
                    )

                  case 24:
                    createdProductInStock = _context6.sent

                    if (!(createdStock?._id in products)) {
                      products[createdStock?._id] = []
                    }

                    products[createdStock?._id].push(createdProductInStock?._id)
                    _context6.next = 38
                    break

                  case 29:
                    newId = new Types.ObjectId(product?.locProductId)
                    _context6.next = 32
                    return regeneratorRuntime.awrap(
                      ProductStock.create({
                        current: product?.qtd,
                        total: product?.qtd,
                        cnpj: cnpj,
                        productLocation: newId,
                      })
                    )

                  case 32:
                    _createdStock = _context6.sent
                    _context6.next = 35
                    return regeneratorRuntime.awrap(
                      ProductStockProduct.create({
                        stock: new Types.ObjectId(_createdStock?._id),
                        productLocation: newId,
                        cnpj: cnpj,
                        barcode: productToPrint?.codBarras,
                        status: 'available',
                        images: [],
                      })
                    )

                  case 35:
                    _createdProductInStock = _context6.sent

                    if (!(_createdStock?._id in products)) {
                      products[_createdStock?._id] = []
                    }

                    products[_createdStock?._id].push(_createdProductInStock?._id)

                  case 38:
                    _context6.next = 44
                    break

                  case 40:
                    _context6.prev = 40
                    _context6.t0 = _context6['catch'](17)
                    console.log(_context6.t0)
                    return _context6.abrupt(
                      'return',
                      res.status(400).json({
                        status: false,
                        error: 'Erro ao operar estoque do produto',
                      })
                    )

                  case 44:
                    productsToPrint.push(
                      _objectSpread({}, productToPrint, {
                        qtd: product?.qtd,
                      })
                    )

                  case 45:
                    _iteratorNormalCompletion = true
                    _context6.next = 9
                    break

                  case 48:
                    _context6.next = 54
                    break

                  case 50:
                    _context6.prev = 50
                    _context6.t1 = _context6['catch'](7)
                    _didIteratorError = true
                    _iteratorError = _context6.t1

                  case 54:
                    _context6.prev = 54
                    _context6.prev = 55

                    if (!_iteratorNormalCompletion && _iterator['return'] != null) {
                      _iterator['return']()
                    }

                  case 57:
                    _context6.prev = 57

                    if (!_didIteratorError) {
                      _context6.next = 60
                      break
                    }

                    throw _iteratorError

                  case 60:
                    return _context6.finish(57)

                  case 61:
                    return _context6.finish(54)

                  case 62:
                    ;(_i = 0), (_Object$entries = Object.entries(products))

                  case 63:
                    if (!(_i < _Object$entries.length)) {
                      _context6.next = 73
                      break
                    }

                    ;(_Object$entries$_i = _slicedToArray(_Object$entries[_i], 2)),
                      (key = _Object$entries$_i[0]),
                      (value = _Object$entries$_i[1])
                    console.log('entrou')
                    _context6.next = 68
                    return regeneratorRuntime.awrap(
                      ProductStock.findOneAndUpdate(
                        {
                          _id: key,
                        },
                        {
                          $set: {
                            items: value,
                          },
                        }
                      )
                    )

                  case 68:
                    a = _context6.sent
                    console.log(a)

                  case 70:
                    _i++
                    _context6.next = 63
                    break

                  case 73:
                    _context6.next = 75
                    return regeneratorRuntime.awrap(
                      PrinterOrigin.findOne({
                        description: 'ALUGUEL',
                      })
                    )

                  case 75:
                    origin = _context6.sent
                    _context6.next = 78
                    return regeneratorRuntime.awrap(
                      PrinterPipeline.create({
                        codunidade: codunidade,
                        data: {
                          codunidade: codunidade,
                          Itens: productsToPrint?.map(function (product) {
                            return {
                              descricao: product?.descricao,
                              qtd: product?.qtd,
                              codbarras: product?.codBarras,
                              valor1: product?.locInfos?.values?.[14],
                              valor2: product?.locInfos?.values?.[28],
                              novo: true,
                            }
                          }),
                        },
                        ownerPrinterCnpj: cnpj,
                        typeId: origin?._id,
                      })
                    )

                  case 78:
                    return _context6.abrupt('return', res.status(200).json())

                  case 79:
                  case 'end':
                    return _context6.stop()
                }
              }
            },
            null,
            null,
            [
              [7, 50, 54, 62],
              [17, 40],
              [55, , 57, 61],
            ]
          )
        },
      },
    ])

    return ProductsClass
  })()

module.exports = ProductsClass
