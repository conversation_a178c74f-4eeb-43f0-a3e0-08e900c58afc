import { useCallback, useEffect, useState } from "react";
import { toast } from "react-toastify";
import { useAuth } from "../../context/auth";
import ApiService from "../../services/ApiService";
import * as BiIcons from "react-icons/bi";
import Swal from "sweetalert2";

import "../../pages/lista_avaliacao/Lista_avaliacao.css";
import "./styles.css";
import { decrypt } from "../../utils/crypto";

const InformativeMessage: React.FC = () => {
  const { user } = useAuth();

  const [todayMessage, setTodayMessage] = useState([]);

  const getNotifications = async () => {
    const todayMessages: any[] = [];
    const today = new Date().toLocaleDateString();

    try {
      const { data } = await ApiService.getAllMessages(user?.enterprise?.cnpj);
      const response = decrypt(data.data, import.meta.env.VITE_PRIVATE_KEY)
      response.forEach((message: any) => {
        const alreadyVisualized = message.visualized?.some(
          (cnpjVisualized: any) =>
            cnpjVisualized?.cnpj === user?.enterprise?.cnpj
        );
        if (
          new Date(message.dataEnvio).toLocaleDateString() === today &&
          !alreadyVisualized
        ) {
          todayMessages.push(message);
        }
      });

      setTodayMessage(todayMessages as any);
    } catch (e: any) {
      const msgError = "Erro consultando mensagens";
      toast.error(e && e.message ? `${msgError}: ${e.message}` : msgError);
    }
  };

  const typeMessage = useCallback((type: any) => {
    if (type === "atualizacao") {
      return <BiIcons.BiEdit size={25} className="icon-update" />;
    } else if (type === "aviso") {
      return <BiIcons.BiInfoCircle size={25} className="icon-alert" />;
    } else {
      return <BiIcons.BiDollar size={25} className="icon-pending" />;
    }
  }, []);

  async function handleSubmitAutorization(id: any) {
    let login: any, password: any, req: any;
    const { data: usersResponse } = await ApiService.GetListarUsuariosPdv(
      user?.enterprise?.cnpj
    );

    const users = decrypt(usersResponse.data, import.meta.env.VITE_PRIVATE_KEY)
    const managers: any = [];
    users.forEach((user: any) => {
      if (user.codTipo === 1 || user?.codigo === "Saullo") managers.push(user);
    });

    Swal.fire({
      title: "Contate o gerente para confirmar leitura da mensagem",
      width: 700,
      html:
        '<div style="display: flex; padding: 10px;">' +
        '<div className="swal2-radio" style="display: block; justify-content: space-between; text-align: initial; width: 50%;">' +
        '<span className="swal2-label">Selecione o gerente</span>' +
        '<select id="gerente" required style="margin-left: 0; border:1px solid lightgray; border-radius:5px; width: 95%; height:32px">' +
        `${managers.map((manager: any, i: number) => {
          return `<option key=${i} value=${manager.codigo}>${manager.usuario}</option>`;
        })}` +
        "</select>" +
        "</div>" +
        '<div className="swal2-radio" style="display: block; justify-content: space-between; text-align: initial; width: 50%;">' +
        '<span className="swal2-label">Digite a senha</span>' +
        '<input id="swal-senha" type="password" style="margin-left: 0; border:1px solid lightgray; border-radius:5px; width: 100%; height:30px" type="text" name="senha">' +
        "</div>" +
        "</div>",
      focusConfirm: false,
      showCancelButton: true,
      cancelButtonText: "Cancelar",
      confirmButtonText: "Autenticar",
      confirmButtonColor: "#009900",
      cancelButtonColor: "#ff0000",
      preConfirm: async () => {
        login = (document.getElementById("gerente") || ({} as any))?.value;
        password = (document.getElementById("swal-senha") || ({} as any))
          ?.value;
        req = {
          user: login,
          password: password,
        };
      },
    }).then((result) => {
      if (result.value) {
        if (!login || !password)
          Swal.fire(
            "Erro de autenticação!",
            "Preencha todos os campos.",
            "error"
          );
        ApiService.PostCheckIsManager(req)
          .then(() => {
            ApiService.confirmReadMessage(id, user?.enterprise?.cnpj).then(
              () => {
                setTodayMessage(
                  todayMessage?.filter((message: any) => message._id !== id)
                );
                Swal.close();
              }
            );
          })
          .catch(() => {
            Swal.fire(
              "Erro de autenticação!",
              "Credenciais inválidas.",
              "error"
            );
          });
      }
    });
  }

  useEffect(() => {
    if (user?._id) {
      getNotifications();
    }
  }, [user]);

  return todayMessage ? (
    <div style={{ display: todayMessage.length >= 1 ? "block" : "none" }}>
      <div className="message-popup-overlay" id="excluded-print">
        {todayMessage?.map((message: any) => (
          <div
            key={message?._id}
            className={`message-container ${message.type}-message`}
          >
            <div className="title">
              <span>Mensagem</span>
            </div>
            <div className="text-message">
              {typeMessage(message?.type)}
              <span>{message.message}</span>
            </div>
            <div className="confirm-message-button">
              <button onClick={() => handleSubmitAutorization(message._id)}>
                Li e estou ciente
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  ) : (
    <></>
  );
};

export default InformativeMessage;
