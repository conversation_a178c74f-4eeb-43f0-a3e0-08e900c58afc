{{- if eq .Values.ingress.enabled true }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  {{- if .Values.ingress.annotations }}
  annotations:
    {{ toYaml .Values.ingress.annotations | nindent 4 }}
  {{- end }}
  name: {{ .Release.Name }}
  namespace: {{ .Release.Namespace }}
spec:
  ingressClassName: {{ .Values.ingress.ingressClassName }}
  rules:
  {{- range .Values.ingress.hosts }}
  - host: {{ .host }}
    http:
      paths:
      - backend:
          service:
            name: {{ include "avaliacao-backend.fullname" $ }}
            port:
              number: {{ $.Values.service.port }}
        path: {{ .path }}
        pathType: {{ .pathType }}
  {{- end }}
  {{- if eq .Values.ingress.tls.enabled true }}
  tls:
  - secretName: {{ .Values.ingress.tls.secretName }}
    hosts:
    {{- range .Values.ingress.tls.hosts }}
    - {{- toYaml . | nindent 6 }}
    {{- end }}
  {{- end }}
{{- end }}