const { model, Schema } = require('mongoose')
const { getCurrentTime } = require('../utils')

const currentDate = getCurrentTime();

// Definindo Schema
const KitSchema = Schema({
  name: String,
  qtd: Number,
  productId: String,
  products: Array,
  enterpriseCnpj: String,
  deleted: {
    type: Boolean,
    default: false,
  },
  deleted_at: Date,
  created_at: {
    type: Date,
    default: currentDate
  },
})

// Definindo collection
const Kit = model('kits', KitSchema)
module.exports = Kit
