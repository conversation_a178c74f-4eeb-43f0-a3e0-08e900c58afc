import QRCode from "qrcode.react";
import { IPrintPointsReceiptData } from "../../types";
// import "./styles.css";

interface PrintsReceiptProps {
  data: IPrintPointsReceiptData;
}

const PrintsReceipt: React.FC<PrintsReceiptProps> = ({
  data: { text, title, url, subtitle },
}) => {
  return (
    <div
      id="recibo_vale"
      style={{
        display: "block",
        fontSize: "12px",
        fontFamily: "'Poppins', sans-serif",
      }}
    >
      <div className="header" style={{ fontSize: "10px", textAlign: "center" }}>
        <h1 className="title" style={{ fontSize: "14px" }}>
          {title}
        </h1>
        ---------------------------------------------
      </div>
      <div className="info">
        <p
          style={{
            fontWeight: "bold",
            fontSize: "14px",
            margin: 0,
            maxWidth: "97%",
            textAlign: "center",
          }}
        >
          {subtitle}
        </p>
      </div>
      <br />
      <div className="info">
        <p
          style={{
            fontWeight: "bold",
            fontSize: "12px",
            margin: 0,
            maxWidth: "97%",
            textAlign: "center",
          }}
        >
          {text}
        </p>
      </div>
      <br />
      <br />
      <div
        className="declaration"
        style={{ textAlign: "center", marginTop: "30px" }}
      >
        <QRCode value={String(url)} size={90} />
      </div>
      <div>
        <p
          style={{
            fontSize: "10px",
            marginTop: "10px",
            margin: 0,
            textAlign: "center",
          }}
        >
          {url}
        </p>
      </div>
      <div className="observation" style={{ textAlign: "center" }}>
        <span>.</span>
      </div>
    </div>
  );
};

export default PrintsReceipt;
