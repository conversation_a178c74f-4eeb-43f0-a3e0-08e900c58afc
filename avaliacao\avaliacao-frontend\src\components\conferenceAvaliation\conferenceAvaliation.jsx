import React from 'react'
import { getCpfFormated } from '../../utils/avaliationUtils'
import { getFormatedDate } from '../../utils/getFormatedDate'
import './styles.css'

export function ConferenceAvaliation({ cpf, nomeCliente, telefone, idMongo }) {
  return (
    <div>
      <div className="impressao">
        <div className="conferencePre">
          <div className="item-center">
            ---------------------------------------------
            <h1 className="title-conference">
              {'>>'}CONFERÊNCIA DE AVALIAÇÃO{'<<'}
            </h1>
            ---------------------------------------------
          </div>
          <div className="centered">
            <p>{getFormatedDate()}</p>
            <p style={{ fontWeight: 'bold', fontSize: '12px' }}>CPF: {getCpfFormated(cpf)}</p>

            <p>Cliente: {nomeCliente}</p>
            <p>Telefone: {telefone}</p>
            <p>ID Pré Avaliação: {idMongo}</p>
          </div>
          <br />
          <p>.</p>
        </div>
      </div>
    </div>
  )
}
