const calculateValuePriceItemSearch = (item, porcent = 1) => {
  // const price = item.ePesquisa && item.precoBruto / porcent
  // return price ? redundeValue(price) : 0
  const fixedValue = Math.floor(item.ePesquisa && item.precoBruto / porcent)
  const newValue = Math.ceil(fixedValue / 10) * 10 || 0
  return newValue
}

const redundeValue = price => {
  const decimal = price % 1
  return decimal > 0.5 ? Math.round(Math.ceil(price)) : Math.floor(price)
}

module.exports = {
  calculateValuePriceItemSearch,
}
