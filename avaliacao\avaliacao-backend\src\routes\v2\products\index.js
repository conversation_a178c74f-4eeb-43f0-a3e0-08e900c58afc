const { Router } = require('express')
const router = Router()

const isAuth = require('../../../middlewares/isAuth')

const ProductClass = require('../../../controllers/products')
const ProductsControllers = new ProductClass()

router.get('/search', isAuth, ProductsControllers.search)
router.get('/searchById/:id', isAuth, ProductsControllers.searchById)
router.get('/favoriteProducts', isAuth, ProductsControllers.getFavoriteProducts)
router.get('/indexingProductsTime', isAuth, ProductsControllers.getIndexingProductsTime)
router.post('/print-location', isAuth, ProductsControllers.printLocationProducts)

module.exports = router
