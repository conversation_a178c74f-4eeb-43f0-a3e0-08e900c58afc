import moment from 'moment';
import './styles.css'

interface DatepickerProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  id: string;
}

const Datepicker: React.FC<DatepickerProps> = ({
  label,
  value,
  onChange,
  id
}) => {
  return (
    <div className="datepicker-container">
      <label htmlFor={id}>{label}</label>
      <input
        type="date"
        id={id}
        value={value}
        onChange={(e) => onChange(moment.utc(e.target.value).format('YYYY-MM-DD'))}
      />
    </div>
  )
}

export default Datepicker;
