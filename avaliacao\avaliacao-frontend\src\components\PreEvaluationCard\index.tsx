import { format } from "date-fns";
import { useEvaluation } from "../../context/evaluations";
import { useOffline } from "../../context/offline";
import "../../pages/lista_avaliacao/Lista_avaliacao.css";
import moment from 'moment';

interface PreEvaluationCardProps {
  preEvaluation: any;
  index: number;
  onProceedClick: () => void;
  onRemoveClick: () => void;
  isActiveButton: boolean;
  hasClient: boolean;
}

const PreEvaluationCard: React.FC<PreEvaluationCardProps> = ({
  preEvaluation,
  index,
  onProceedClick,
  onRemoveClick,
  isActiveButton,
  hasClient,
}) => {
  const { isOffline } = useOffline();
  const { hasOpenEvaluation } = useEvaluation();

  let preEvaluationDate: any = moment.utc(preEvaluation?.data)?.format('DD/MM/YYYY HH:mm:ss');
  return (
    <div key={index} className="avaliacoes hidden-print">
      <table
        id="tabela-pre-avaliacao"
        className="hidden-print"
        style={{ background: !preEvaluation?.deletedAt ? "#FFF" : "#ff8080" }}
      >
        <tbody>
          <tr>
            <td className="item-avaliacoes td1">Usuário</td>
            <td className="item-avaliacoes td1">Nome do Fornecedor</td>
            <td className="item-avaliacoes td1">CPF do Fornecedor</td>
            <td className="item-avaliacoes td1">Celular</td>
            <td className="item-avaliacoes td1">Data</td>
            <td className="item-avaliacoes td1">Volume</td>
          </tr>
          <tr>
            <td className="item-avaliacoes corpo td1">
              {preEvaluation.usuario}
            </td>
            {
              // verifica se tem um fornecedor, através da flag
              hasClient ? (
                // se tiver mostra os dados dele
                <>
                  <td className="item-avaliacoes corpo td1">
                    {preEvaluation.cliente[0].nome}
                  </td>
                  <td className="item-avaliacoes corpo td1">
                    {preEvaluation.cliente[0].cpf}
                  </td>
                  <td className="item-avaliacoes corpo td1">
                    {preEvaluation.cliente[0].celular}
                  </td>
                </>
              ) : (
                // se nao tiver mostra que não encontrou o fornecedor
                <>
                  <td className="item-avaliacoes corpo td1">
                    (Fornecedor não encontrado)
                  </td>
                  <td className="item-avaliacoes corpo td1">
                    (Fornecedor não encontrado)
                  </td>
                  <td className="item-avaliacoes corpo td1">
                    (Fornecedor não encontrado)
                  </td>
                </>
              )
            }
            <td className="item-avaliacoes corpo td1">{preEvaluationDate}</td>
            <td className="item-avaliacoes corpo td1">
              {preEvaluation.volume}
            </td>
          </tr>
        </tbody>
      </table>
      <div id="pre-avaliacao-mobile" className="hidden-print">
        <div className="header-mobile">Usuario</div>
        <div className="body-mobile">{preEvaluation?.usuario}</div>
        <div className="header-mobile">Nome do Fornecedor</div>
        <div className="body-mobile">{preEvaluation?.cliente[0]?.nome}</div>
        <div className="header-mobile">CPF do Fornecedor</div>
        <div className="body-mobile">{preEvaluation?.cliente[0]?.cpf}</div>
        <div className="header-mobile">Celular</div>
        <div className="body-mobile">{preEvaluation?.cliente[0]?.celular}</div>
        <div className="header-mobile">Data</div>
        <div className="body-mobile">{preEvaluationDate}</div>
        <div className="header-mobile">Volume</div>
        <div className="body-mobile">{preEvaluation?.volume}</div>
      </div>
      <div
        className="item-avaliacoes hidden-print"
        style={{ display: "flex", justifyContent: "center" }}
      >
        <button
          data-tip={
            isOffline
              ? "Não é possivel prosseguir uma pré-avaliação offline"
              : hasOpenEvaluation
              ? "Existe uma avaliação aberta!"
              : null
          }
          className="item2"
          onClick={onProceedClick}
          style={
            isActiveButton
              ? {
                  width: "210px",
                  cursor: "not-allowed",
                  border: "0",
                  fontSize: "16px",
                }
              : {
                  width: "210px",
                  cursor: "pointer",
                  border: "0",
                  fontSize: "16px",
                }
          }
          type="button"
          disabled={
            isActiveButton ||
            isOffline ||
            hasOpenEvaluation ||
            preEvaluation?.deletedAt
          }
        >
          <span>Prosseguir Avaliação</span>
        </button>
        {/* <button
          data-tip={
            isOffline
              ? "Não é possivel excluir uma pré-avaliação offline"
              : null
          }
          className="item2"
          onClick={onRemoveClick}
          style={
            isActiveButton
              ? {
                  width: "210px",
                  cursor: "not-allowed",
                  border: "0",
                  fontSize: "16px",
                }
              : {
                  width: "210px",
                  cursor: "pointer",
                  border: "0",
                  fontSize: "16px",
                  background: "red",
                }
          }
          type="button"
          disabled={isActiveButton || isOffline || preEvaluation?.deletedAt}
        >
          <span>Excluir Pré-Avaliação</span>
        </button> */}
      </div>
    </div>
  );
};

export default PreEvaluationCard;
