const { model, Schema } = require('mongoose')

// Definindo Schema
const AvaliacaoSchema = Schema({
  items: {
    type: Array,
  },
  pricingType: {
    type: String,
    default: null
  },
  motivo: {
    type: String,
  },
  usuario: {
    type: String,
  },
  idUsuario: {
    type: String,
  },
  cnpj: {
    type: String,
  },
  dataInicio: {
    type: Date,
  },
  dataFinal: {
    type: Date,
  },
  dataPreAvaliacao: {
    type: Date,
    default: null,
  },
  totalValePadrao: {
    type: String,
    default: '0',
  },
  totalValeEfetivado: {
    type: String,
    default: '0',
  },
  totalDinheiroPadrao: {
    type: String,
    default: '0',
  },
  totalDinheiroEfetivado: {
    type: String,
    default: '0',
  },
  totalValorizacaoManual: {
    type: String,
    default: '0',
  },
  totalValorizacaoManual: {
    type: String,
  },
  totalPixPadrao: {
    type: String,
    default: '0',
  },
  totalPixEfetivado: {
    type: String,
    default: '0',
  },
  oferta: {
    type: String,
  },
  tipo: {
    type: String,
  },
  codBarras: {
    type: String,
  },
  finalizado: {
    type: Boolean,
  },
  cancelado: {
    type: Boolean,
  },
  cpf: {
    type: String,
  },
  cliente: {
    type: String,
  },
  uf: {
    type: String,
  },
  telefone: {
    type: String,
  },
  idPreAvaliacao: {
    type: String,
  },
  caixa: {
    type: String,
  },
  manual: {
    type: Boolean,
  },
  eventosFiscais: {
    type: [Object],
  },
  emitiuNfe: {
    type: Boolean,
  },
  itensEstoque: {
    type: Array,
  },
  idUsuarioAvaliacao: {
    type: String,
  },
  rejected: {
    type: Boolean,
    default: false,
    require: false,
  },
  reject_at: {
    type: Date,
    require: false,
    default: null,
  },
  vlrMin: {
    type: Number,
  },
  vlrVenda: {
    type: Number,
  },
  vlrCusto: {
    type: Number,
  },
  offline: {
    type: Boolean,
    default: false,
  },
  canceledAt: {
    type: Date,
    default: null,
  },
  evaluatorName: {
    type: String,
    default: null,
  },
  evaluationPoints: {
    type: Number,
  },
  invoiceAt: {
    type: Date,
  },
  manualReason: {
    type: String,
    default: null,
  },
  higienizationItems: {
    type: Array,
  },
  discardedItems: {
    type: Array,
  },
  images: {
    type: Array,
    default: null,
  },
  clientExpectation: {
    type: Number,
    default: 0,
  },
  evaluationValueDiference: {
    type: Number,
    default: 0,
  },
  ip: {
    type: String,
    default: null,
  },
  batchValue: {
    type: Number,
  }
})

// Definindo collection
const Avaliacao = model('avaliacao', AvaliacaoSchema)
module.exports = Avaliacao
