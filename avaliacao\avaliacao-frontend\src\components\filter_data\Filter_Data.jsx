import React, { Component } from 'react'

import './Filter_Data.css'
import ApiService from '../../services/ApiService'
import { toast } from 'react-toastify'
import ReactModal from 'react-modal'
import moment from 'moment'
import { AuthContext } from '../../context/auth'

class Filter_Data extends Component {
  static contextType = AuthContext
  constructor(props) {
    super(props)
    this.state = {
      toggle: false,
      cnpj: '',
      dateInicio: '',
      dateFinal: '',
      dateMes: moment().format('YYYY-MM'),
      active_select1: false,
      active_select2: false,
      valorNota: 1,
      dataEntrada: false,
      dataXml: false,
    }
  }

  async getDates() {
    const { dateInicio, dateFinal, dateMes } = this.state
    const { user } = this.context
    var primeiroDiaMes = dateMes + '-01'
    var ultimoDiaMes = moment(dateMes).endOf('month').format('YYYY-MM-DD')
    if (this.state.dataEntrada === false && this.state.dataXml === false) {
      toast.error(`Selecione o tipo da data`)
    }
    if (dateMes.length > 0 && this.state.dataEntrada) {
      const infoPeriodo = {
        filtragem: 'Este mês',
        tipoData: 'Entrada',
        inicioPeriodo: primeiroDiaMes,
        fimPeriodo: ultimoDiaMes,
      }
      this.props.infoPeriodo.length = 0
      this.props.infoPeriodo.push(infoPeriodo)
      //Converte data mês
      try {
        const response = await ApiService.GetNotasByCriacao(
          user?.enterprise?.cnpj,
          primeiroDiaMes,
          ultimoDiaMes
        )
        const array = response.data.data
        if (array == 'Not found any invoice note') {
          toast.error(`Nota não encontrada`)
        } else {
          this.props.arrayNota.length = 0
          array.forEach((item) => {
            const notas = [
              {
                _id: item?._id,
                dataCriacao: item.createAt,
                numeroNota: item.infoNota.numero,
                dataNota: item.infoNota?.dataNota,
                fornecedorNota: item.infoEmit?.nomeEmitente,
                valorTotalNota:
                  item.impostoTotal.valorTotalDaNotaFiscalImpostoTotal,
                produtosArray: item?.produtosArray
              },
            ]

            this.props.arrayNota.push(notas)
          })
          this.props.closeModal()
        }
      } catch (error) {
        console.log(error)
      }
    } else if (dateMes.length > 0 && this.state.dataXml) {
      const infoPeriodo = {
        filtragem: 'Este mês',
        tipoData: 'Nota',
        inicioPeriodo: primeiroDiaMes,
        fimPeriodo: ultimoDiaMes,
      }
      this.props.infoPeriodo.length = 0
      this.props.infoPeriodo.push(infoPeriodo)
      try {
        const response = await ApiService.GetNotasEntradaByPeriodo(
          user?.enterprise?.cnpj,
          primeiroDiaMes,
          ultimoDiaMes
        )
        const array = response.data.data

        if (array == 'Not found any invoice note') {
          toast.error(`Nota não encontrada`)
        } else {
          this.props.arrayNota.length = 0
          array.forEach((item) => {
            const notas = [
              {
                dataCriacao: item.createAt,
                numeroNota: item.infoNota.numero,
                dataNota: item.infoNota?.dataNota,
                fornecedorNota: item.infoEmit?.nomeEmitente,
                valorTotalNota:
                  item.impostoTotal.valorTotalDaNotaFiscalImpostoTotal,
                _id: item?._id,
                produtosArray: item?.produtosArray
              },
            ]

            this.props.arrayNota.push(notas)
          })
          this.props.closeModal()
        }
      } catch (error) {
        console.log(error)
      }
    }
    if (dateInicio.length > 0 && this.state.dataEntrada) {
      const infoPeriodo = {
        filtragem: 'Periodo Customizado',
        tipoData: 'Entrada',
        inicioPeriodo: dateInicio,
        fimPeriodo: dateFinal,
      }
      this.props.infoPeriodo.length = 0
      this.props.infoPeriodo.push(infoPeriodo)
      try {
        const response = await ApiService.GetNotasByCriacao(
          user?.enterprise?.cnpj,
          dateInicio,
          dateFinal
        )

        const array = response.data.data
        if (array == 'Not found any invoice note') {
          toast.error(`Nota não encontrada`)
        } else {
          this.props.arrayNota.length = 0

          array.forEach((item) => {
            const notas = [
              {
                dataCriacao: item.createAt,
                numeroNota: item.infoNota.numero,
                dataNota: item.infoNota?.dataNota,
                fornecedorNota: item.infoEmit?.nomeEmitente,
                valorTotalNota:
                  item.impostoTotal.valorTotalDaNotaFiscalImpostoTotal,
                _id: item?._id,
                produtosArray: item?.produtosArray
              },
            ]

            this.props.arrayNota.push(notas)
          })

          this.props.closeModal()
        }
      } catch (error) {
        console.log(error)
      }
    } else if (dateInicio.length > 0 && this.state.dataXml) {
      const infoPeriodo = {
        filtragem: 'Periodo Customizado',
        tipoData: 'Nota',
        inicioPeriodo: dateInicio,
        fimPeriodo: dateFinal,
      }
      this.props.infoPeriodo.length = 0
      this.props.infoPeriodo.push(infoPeriodo)
      try {
        const response = await ApiService.GetNotasEntradaByPeriodo(
          user?.enterprise?.cnpj,
          dateInicio,
          dateFinal
        )

        const array = response.data.data
        if (array == 'Not found any invoice note') {
          toast.error(`Nota não encontrada`)
        } else {
          this.props.arrayNota.length = 0

          array.forEach((item) => {
            const notas = [
              {
                dataCriacao: item.createAt,
                numeroNota: item.infoNota.numero,
                dataNota: item.infoNota?.dataNota,
                fornecedorNota: item.infoEmit?.nomeEmitente,
                valorTotalNota:
                  item.impostoTotal.valorTotalDaNotaFiscalImpostoTotal,
                _id: item?._id,
                produtosArray: item?.produtosArray
              },
            ]

            this.props.arrayNota.push(notas)
          })

          this.props.closeModal()
        }
      } catch (error) {
        console.log(error)
      }
    }
  }

  render() {
    return (
      <ReactModal
        isOpen={this.props.openModal}
        className="container-modal-data"
        overlayClassName="overlay"
      >
        <div className="container-data">
          <div className="container-buttons-filter-data">
            <button
              onClick={() =>
                this.setState({ active_select1: true, active_select2: false })
              }
              className={
                this.state.active_select1
                  ? 'isSelect'
                  : 'container-buttons-filter-data-button '
              }
            >
              Mês
            </button>
            <button
              onClick={() =>
                this.setState({ active_select1: false, active_select2: true })
              }
              className={
                this.state.active_select2
                  ? 'isSelect'
                  : 'container-buttons-filter-data-button '
              }
            >
              {' '}
              Periodo customizado
            </button>
          </div>
          {this.state.active_select1 ? (
            <div className="container-input-date-filter-data">
              <div className="container-checkbox-filter-data">
                <p>Tipo Data:</p>

                <div className="container-checkbox">
                  <input
                    type="checkbox"
                    id="data-entrada"
                    name="data-entrada"
                    onChange={() =>
                      this.setState({ dataEntrada: true, dataXml: false })
                    }
                    checked={this.state.dataEntrada}
                  />
                  <span className="span-data-filter" htmlFor="data-xml">
                    Data Entrada{' '}
                  </span>

                  <input
                    type="checkbox"
                    id="data-xml"
                    name="data-xml"
                    checked={this.state.dataXml}
                    onChange={() =>
                      this.setState({ dataEntrada: false, dataXml: true })
                    }
                  />
                  <span className="span-data-filter" htmlFor="data-xml">
                    Data Nota
                  </span>
                </div>
              </div>

              <div className="container-input-mes">
                <p>Selecione Mês:</p>
                <input
                  type="month"
                  className="date-mes-filter-data"
                  defaultValue={this.state.dateMes}
                  onChange={(event) =>
                    this.setState({ dateMes: event.target.value })
                  }
                />
              </div>
            </div>
          ) : (
            <div className="container-input-date-filter-data">
              <div className="container-checkbox-filter-data">
                <p>Tipo Data:</p>

                <div className="container-checkbox">
                  <input
                    type="checkbox"
                    id="data-entrada"
                    name="data-entrada"
                    checked={this.state.dataEntrada}
                    onChange={() =>
                      this.setState({ dataEntrada: true, dataXml: false })
                    }
                  />
                  <span className="span-data-filter" htmlFor="data-xml">
                    Data Entrada{' '}
                  </span>

                  <input
                    type="checkbox"
                    id="data-xml"
                    name="data-xml"
                    checked={this.state.dataXml}
                    onChange={() =>
                      this.setState({ dataEntrada: false, dataXml: true })
                    }
                  />
                  <span className="span-data-filter" htmlFor="data-xml">
                    Data Nota
                  </span>
                </div>
              </div>

              <div className="container-input-periodo">
                <div>
                  <p>Inicio do período</p>
                  <input
                    type="date"
                    className="date-begin"
                    defaultValue={this.state.dateInicio}
                    onChange={(event) =>
                      this.setState({ dateInicio: event.target.value })
                    }
                  />
                </div>
                <div>
                  <p>Fim do período</p>
                  <input
                    type="date"
                    className="date-end"
                    defaultValue={this.state.dateFinal}
                    onChange={(event) =>
                      this.setState({ dateFinal: event.target.value })
                    }
                  />
                </div>
              </div>
            </div>
          )}
          <div className="footer-modal-filter-data">
            <button
              onClick={() => this.props.closeModal()}
              className="btn-modal-filter-data-cancelar"
            >
              Cancelar
            </button>
            <button
              onClick={() => this.getDates()}
              className="btn-modal-filter-data-salvar"
            >
              Filtrar
            </button>
          </div>
        </div>
      </ReactModal>
    )
  }
}

export default Filter_Data
