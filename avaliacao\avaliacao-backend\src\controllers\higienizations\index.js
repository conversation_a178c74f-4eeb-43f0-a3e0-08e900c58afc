const HigienizationModel = require('../../models/Higienization');
const EvaluationModel = require('../../models/Avaliacao');
const ClientModel = require('../../models/Cliente');
const EnterpriseModel = require('../../models/Empresa');
const { Types } = require('mongoose');
const { removeProduct } = require('../../modules/inventory/services/removeProducts');

class HigienizationClass {
  constructor() { };

  async findAll(req, res) {
    const { page, limit, q } = req?.query;
    let higienizations = [];

    const higienizationList = await HigienizationModel
      .find({ enterpriseCnpj: req?.usuario?.enterprise?.cnpj })
      .lean();

    for (let higienization of higienizationList) {
      const evaluation = await EvaluationModel.findOne({ _id: new Types.ObjectId(higienization?.evaluationId) });
      const client = await ClientModel.findOne({ cpf: higienization.clientCpf });
      const enterprise = await EnterpriseModel.findOne({ cnpj: higienization.enterpriseCnpj });

      higienization?.items?.forEach((item) => {
        higienizations.push({
          ...item,
          client,
          evaluation,
          enterprise,
        })
      })
    }

    if (q) {
      higienizations = higienizations?.filter((higienization) => String(higienization?.description)?.toLowerCase().includes(String(q)?.toLowerCase()))
    };

    let paginatedHigienizations = [];

    higienizations?.forEach((higienization, index) => {
      if (index >= (page - 1) * limit && index < page * limit) {
        paginatedHigienizations?.push(higienization);
      }
    })

    return res.status(200).json({
      higienizations: paginatedHigienizations || [],
      total: higienizations.length,
      totalPages: Math.ceil(higienizations.length / limit),
    });
  }

  async create(req, res) {
    const { evaluationId, clientCpf, enterpriseCnpj, items } = req.body;

    const verifyIfEvaluationExists = await EvaluationModel.findOne({ _id: new Types.ObjectId(evaluationId) }).lean();

    if (!verifyIfEvaluationExists) {
      return res.status(404).json({
        status: false,
        error: "Avaliação não existe!",
      });
    }

    const updatedEvaluationItems = verifyIfEvaluationExists?.items?.map((item) => {
      const currentItem = items?.[0];

      if (currentItem?._id === item?._id && (currentItem?.descPesquisa || null) === (item?.descPesquisa || null)) {
        return ({ ...item, qtd: item?.qtd - currentItem?.qtd })
      }

      return ({ ...item });
    })

    const newHigienizationItemsOfEvaluation = [...(verifyIfEvaluationExists?.higienizationItems || []), { ...items?.[0], higienization: true }];

    const verifyIfExist = await HigienizationModel.findOne({ evaluationId }).lean();

    try {
      const createdHigienization = await HigienizationModel.findOneAndUpdate({
        evaluationId
      }, {
        evaluationId,
        clientCpf,
        enterpriseCnpj,
        items: verifyIfExist?.items?.length ? [...verifyIfExist?.items, ...items] : items,
        finished: false,
      }, { upsert: true })

      await EvaluationModel.findOneAndUpdate({
        _id: new Types.ObjectId(evaluationId),
      }, {
        $set: {
          items: updatedEvaluationItems,
          higienizationItems: newHigienizationItemsOfEvaluation
        }
      });

      return res.status(200).json(createdHigienization);
    } catch (err) {
      return res.status(400).json(err);
    }
  };

  async remove(req, res) {
    const { products } = req.body;

    for (let product of products) {
      const higienization = await HigienizationModel.findOne({ evaluationId: product?.evaluation?._id }).lean();
      const evaluation = await EvaluationModel.findOne({ _id: new Types.ObjectId(product?.evaluation?._id) }).lean();
      const newHigienizationItems = higienization?.items?.map((item) => item?._id === product?._id ? ({ ...item, qtd: item?.qtd - product?.qtdSelected, higienization: true }) : ({ ...item }))?.filter((item) => item?.qtd > 0);
      const newEvaluationItems = evaluation?.items?.map((item) => item?._id === product?._id ? ({ ...item, qtd: item?.qtd + product?.qtdSelected }) : ({ ...item }))

      if (!newHigienizationItems?.length) {
        await HigienizationModel.deleteOne({ evaluationId: product?.evaluation?._id })
        await EvaluationModel.findOneAndUpdate({ _id: new Types.ObjectId(product?.evaluation?._id) }, {
          items: newEvaluationItems,
          higienizationItems: null
        })
      } else {
        await HigienizationModel.findOneAndUpdate({ evaluationId: product?.evaluation?._id }, { items: newHigienizationItems })
        await EvaluationModel.findOneAndUpdate({ _id: new Types.ObjectId(product?.evaluation?._id) }, {
          items: newEvaluationItems,
          higienizationItems: newHigienizationItems
        })
      }
    }

    return res.status(200).json();
  }

  async discard(req, res) {
    const { products } = req.body;
    let productList = [];

    for (let product of products) {
      const higienization = await HigienizationModel.findOne({ evaluationId: product?.evaluation?._id }).lean();
      const evaluation = await EvaluationModel.findOne({ _id: new Types.ObjectId(product?.evaluation?._id) }).lean();
      const newHigienizationItems = higienization?.items?.map((item) => item?._id === product?._id ? ({ ...item, qtd: Number(item?.qtd - product?.qtdSelected), higienization: true }) : ({ ...item })).filter((item) => item?.qtd > 0);

      if (!newHigienizationItems?.length) {
        await HigienizationModel.findOneAndDelete({ evaluationId: product?.evaluation?._id })
        await EvaluationModel.findOneAndUpdate({ _id: new Types.ObjectId(product?.evaluation?._id) }, { higienizationItems: null, discardedItems: [...(evaluation?.discardedItems || []), { ...product, discarded: true, higienization: false, qtd: product?.qtdSelected }] })
      } else {
        await HigienizationModel.findOneAndUpdate({ evaluationId: product?.evaluation?._id }, { items: newHigienizationItems })
        await EvaluationModel.findOneAndUpdate({ _id: new Types.ObjectId(product?.evaluation?._id) }, { higienizationItems: newHigienizationItems, discardedItems: [...(evaluation?.discardedItems || []), { ...product, discarded: true, higienization: false, qtd: product?.qtdSelected }] })
      }

      productList?.push({ ...product, qtd: Number(product.qtdSelected) });
    }

    await removeProduct({
      items: productList,
      cnpj: req?.usuario?.enterprise?.cnpj
    });

    return res.status(200).json();
  }
}

module.exports = HigienizationClass
