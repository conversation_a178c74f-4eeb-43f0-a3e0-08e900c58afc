.title-modal {
  font-size: 32px;
  margin-bottom: 5px;
}

.div-with-scroll.pricing-entry {
  display: block;
  max-height: calc(100% - 170px);
  overflow-y: auto;
  overscroll-behavior-y: contain;

  .div-parent-with-scroll {
    height: auto;
  }
}

.div-parent-with-scroll table {
  position: relative;
  border-collapse: collapse;
}

.div-parent-with-scroll table thead th {
  background: white;
  position: sticky;
  top: -12px;
  z-index: 19;
  padding: 5px;
}

.div-parent-with-scroll table tbody {
  position: relative;
}

.div-parent-with-scroll table tbody td {
  padding: 0px 5px;
}

.div-parent-with-scroll table tbody td,
.div-parent-with-scroll table thead th {
  font-size: 12px;
}

table>thead>tr>th {
  text-align: center;
}

.modal-footer.pricing-entry {
  position: absolute;
  bottom: 30px;
  width: 100%;
  max-width: calc(100% - 30px);
}

.buttons-value.pricing,
.button-print.pricing {
  margin-top: 15px;
  gap: 15px;
  // position: absolute;
  // bottom: 30px;
  // width: 100%;
  // max-width: calc(100% - 30px);
}

.buttons-value.pricing button,
.button-print.pricing button {
  font-size: 12px;
}

.input-percentage {
  text-align: center;
  width: 70px;
}

@media screen and (min-width: 767px) {
  .title-moda {
    font-size: 20px !important;
  }
}

@media screen and (min-width: 1100px) {
  .title-modal {
    font-size: 28px !important;
  }

  .div-with-scroll.pricing-entry {
    display: block;
  }

  .div-parent-with-scroll table tbody td,
  .div-parent-with-scroll table thead th {
    font-size: 13px;
  }
}

@media screen and (min-width: 1400px) {
  .size-modal {
    height: 50vh;
  }

  .div-with-scroll.pricing-entry {
    display: block;
  }

  .div-parent-with-scroll table tbody td,
  .div-parent-with-scroll table thead th {
    font-size: 14px;
  }

  .buttons-value.pricing button,
  .button-print.pricing button {
    font-size: 14px;
  }

  .modal-print-items {
    padding-top: 10px;
  }

  .input-percentage {
    text-align: center;
    width: 70px;
  }
}