replicaCount: 1

image:
  repository: .
  pullPolicy: Always
  tag: ""

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

podAnnotations:
  vault.hashicorp.com/agent-inject: 'true'
  vault.hashicorp.com/role: avaliacao-backend
  vault.hashicorp.com/agent-inject-template-env.sh: |
    {{- with secret "avaliacao-backend/data/homolog" -}}
    {{- range $k, $v := .Data.data }}
    export {{ $k }}='{{ $v }}'
    {{- end }}
    {{- end }}

podCommand: ["/bin/sh", "-c"]
podArgs: [". /vault/secrets/env.sh && pm2-runtime start pm2.json"]

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

serviceAccount:
  create: true
  name: avaliacao-backend
  annotations: {}

service:
  port: 8080

ingress:
  enabled: true
  hosts:
  - host: avaliacaohomologacao.dfcomsoftware.com.br
    path: /
    pathType: Prefix
  ingressClassName: alb
  annotations: 
    alb.ingress.kubernetes.io/certificate-arn: arn:aws:acm:sa-east-1:************:certificate/ee0d86f1-ad9e-4ac2-8c77-8427fcd5dd08
    alb.ingress.kubernetes.io/group.name: homolog
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80, "HTTPS": 443}]'
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/ssl-redirect: "443"
    alb.ingress.kubernetes.io/target-type: ip
  tls:
    enabled: false
    hosts: []
    secretName: ""

hpa:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}
