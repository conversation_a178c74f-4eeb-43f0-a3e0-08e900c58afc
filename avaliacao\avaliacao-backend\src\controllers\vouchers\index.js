const Vale = require('../../models/Vale')
const moment = require('moment-timezone')
class VouchersClass {
  constructor() {}

  async createVoucher(voucher) {
    if (!voucher) {
      return {
        status: false,
        error: 'Vale obrigatório para avaliações do tipo vale',
      }
    }

    const existVoucherForEvaluation = await Vale.findOne({
      idAvaliacao: voucher.idAvaliacao,
    }).lean()

    const voucherDate = moment.tz('America/Sao_Paulo').format('YYYY-MM-DDTHH:mm:ssZ')
    voucher.data = voucherDate

    if (existVoucherForEvaluation) {
      const saveVoucher = await Vale.findOneAndUpdate(
        { _id: existVoucherForEvaluation?._id },
        voucher,
        { new: true }
      )
      return saveVoucher
    }

    try {
      const createdVoucher = await Vale.create(voucher)
      return createdVoucher
    } catch (error) {
      return error
    }
  }
}
module.exports = VouchersClass
