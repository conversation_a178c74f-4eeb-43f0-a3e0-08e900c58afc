const { model, Schema } = require('mongoose')

// Definindo Schema
const AlertSchema = Schema({
  application: {
    type: String, // pdv,avaliação, franquia
  },
  type: {
    type: String, // cpf, dre, edição de valor venda
  },
  cpf: {
    type: String,
    required: false,
    default: null
  },
  cnpj: {
    type: String,
  },
  message: {
    type: String,
  },
  data: {
    type: Object,
  },
  read: {
    type: Boolean,
    default: false,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  readAt: {
    type: Date,
    default: null,
    required: false,
  },
  deletedAt: {
    type: Date,
    required: false,
    default: null
  }
})

// Definindo collection
const Alert = model('alerts', AlertSchema)
module.exports = Alert
