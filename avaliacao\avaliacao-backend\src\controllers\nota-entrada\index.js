const notaEntradaModel = require('../../models/NotaEntrada')
const utils = require('../../utils')
const InvoiceNotesModel = require('../../models/NotaEntrada')
const { isValidObjectId, Types } = require('mongoose')
const ProdutoModel = require('../../models/Produto')
const ProductsProvidersReferencesModel = require('../../models/ProductsProvidersReference')
const inventory = require('../../modules/inventory/services')

class InvoiceNotes {
  constructor() {}

  async deletedInvoiceNote(req, res) {
    try {
      const { invoiceId } = req.params

      if (!isValidObjectId(invoiceId))
        return res.status(200).json({
          status: false,
          data: 'Id of invoice is invalid',
        })

      const _invoiceNote = await notaEntradaModel.findOne({
        _id: invoiceId,
        deleted: false,
      })

      if (!_invoiceNote)
        return res.status(200).json({
          status: false,
          data: 'Not found any invoice note',
        })

      await notaEntradaModel.updateOne(
        { _id: _invoiceNote['_id'] },
        { delete_at: utils.getCurrentTime(), deleted: true }
      )

      const _invoiceNoteDeleted = await notaEntradaModel.findOne({
        _id: _invoiceNote['_id'],
      })

      if (!_invoiceNoteDeleted['deleted'] == true)
        return res.status(200).json({
          status: false,
          data: 'Not deleted, try letter.',
        })

      res.status(200).json({
        status: true,
        invoicenNoteDeleted: _invoiceNoteDeleted,
        data: `Invoice Note deleted`,
      })
    } catch (error) {
      console.error(error)
      res.status(400).json({
        status: error,
        error: 'Bad request, try letter',
      })
    }
  }

  async updateInvoiceNotes(req, res) {
    try {
      const { invoiceId } = req.params
      const updateData = req.body

      const _invoiceNote = await notaEntradaModel.findOne({
        _id: invoiceId,
        deleted: false,
      })

      if (!_invoiceNote)
        return res.status(200).json({
          status: false,
          data: 'Not found any invoice note',
        })

      await notaEntradaModel.updateOne(
        { _id: _invoiceNote['_id'] },
        {
          infoNota: updateData['infoNota'] || _invoiceNote['infoNota'],
          infoEmit: updateData['infoEmit'] || _invoiceNote['_invoiceNote'],
          impostoTotal: updateData['impostoTotal'] || _invoiceNote['impostoTotal'],
          infoTranspor: updateData['infoTranspor'] || _invoiceNote['infoTranspor'],
        }
      )

      const _invoiceNoteUpdate = await notaEntradaModel.findOne({ _id: _invoiceNote['_id'] })

      res.status(200).json({
        status: true,
        data: _invoiceNoteUpdate,
      })
    } catch (error) {
      console.error(error)
      res.status(400).json({
        status: error,
        error: 'Bad request, try letter',
      })
    }
  }

  async getInvoiceNotes(req, res) {
    try {
      let filter = {}
      filter = req.query

      if (filter['_id'] && isValidObjectId(filter['_id'])) {
        filter['_id'] = new Types.ObjectId(filter['_id'])
      } else if (filter['_id'] && !isValidObjectId(filter['_id'])) {
        res.status(400).json({
          status: false,
          data: '_id is incorrectly',
        })
      }

      if (filter['startInvoiceDate']) {
        filter = {
          ...filter,
          'infoNota.dataNota': {
            $gte: new Date(filter['startInvoiceDate']),
          },
        }
        delete filter['startInvoiceDate']
      }

      if (filter['finalInvoiceDate']) {
        filter = {
          ...filter,
          'infoNota.dataNota': {
            ...filter['infoNota.dataNota'],
            $lte: new Date(filter['finalInvoiceDate']),
          },
        }
        delete filter['finalInvoiceDate']
      }

      if (filter['startEntryDate']) {
        filter = {
          ...filter,
          createAt: {
            $gte: new Date(filter['startEntryDate']),
          },
        }
        delete filter['startEntryDate']
      }

      if (filter['finalEntryDate']) {
        filter = {
          ...filter,
          createAt: {
            ...filter['createAt'],
            $lte: new Date(filter['finalEntryDate']),
          },
        }
        delete filter['finalEntryDate']
      }

      const _invoiceNotes = await InvoiceNotesModel.find({ ...filter, deleted: false })
        .limit(20)
        .sort({ createAt: -1 })
        .lean()

      if (!_invoiceNotes.length)
        return res.status(200).json({
          status: false,
          data: 'Not found any invoice note',
        })

      return res.status(200).json({
        status: true,
        data: _invoiceNotes,
      })
    } catch (error) {
      console.error(error)
      res.status(400).json({
        status: error,
        error: 'Bad request, try letter',
      })
    }
  }

  async searchInvoiceNotes(req, res) {
    try {
      let filter = {}
      filter = req.query

      if (filter['_id'] && isValidObjectId(filter['_id'])) {
        filter['_id'] = new Types.ObjectId(filter['_id'])
      } else if (filter['_id'] && !isValidObjectId(filter['_id'])) {
        res.status(400).json({
          status: false,
          data: '_id is incorrectly',
        })
      }

      if (filter['startInvoiceDate']) {
        filter = {
          ...filter,
          'infoNota.dataNota': {
            $gte: new Date(filter['startInvoiceDate']),
          },
        }
        delete filter['startInvoiceDate']
      }

      if (filter['finalInvoiceDate']) {
        filter = {
          ...filter,
          'infoNota.dataNota': {
            ...filter['infoNota.dataNota'],
            $lte: new Date(filter['finalInvoiceDate']),
          },
        }
        delete filter['finalInvoiceDate']
      }

      if (filter['startEntryDate']) {
        filter = {
          ...filter,
          createAt: {
            $gte: new Date(filter['startEntryDate']),
          },
        }
        delete filter['startEntryDate']
      }

      if (filter['finalEntryDate']) {
        filter = {
          ...filter,
          createAt: {
            ...filter['createAt'],
            $lte: new Date(filter['finalEntryDate']),
          },
        }
        delete filter['finalEntryDate']
      }

      const _invoiceNotes = await InvoiceNotesModel.find({ ...filter, deleted: false })
        .limit(20)
        .sort({ createAt: -1 })
        .lean()

      if (!_invoiceNotes.length)
        return res.status(200).json({
          status: false,
          data: 'Not found any invoice note',
        })

      return res.status(200).json({
        status: true,
        data: _invoiceNotes,
      })
    } catch (error) {
      console.error(error)
      res.status(400).json({
        status: error,
        error: 'Bad request, try letter',
      })
    }
  }

  async registerInvoiceNote(req, res) {
    async function buildInventoryProductList(noteProducts, inventoryProductList) {
      for (const item of noteProducts) {
        const product = await ProdutoModel.findOne({
          codigo: item.codigoProdutoInterno,
        })

        if (product) {
          inventoryProductList.push({
            idProduto: product._id,
            qtd: Number(item.qCom),
          })
        }
      }
    }

    try {
      let _invoiceSaved = []
      let invoiceRejections = []
      const { invoiceNotes } = req.body

      for (const note of invoiceNotes) {
        let inventoryProducts = []

        const _haveExistInvoiceNote = await notaEntradaModel.find({
          'infoNota.numero': note['infoNota']['numero'],
          'infoNota.cnpjEmpresa': note['infoNota']['cnpjEmpresa'],
          'infoNota.serie': note['infoNota']['serie'],
          deleted: false,
        })

        if (!_haveExistInvoiceNote.length) {
          const _invoice = await notaEntradaModel.create({
            infoNota: note['infoNota'],
            infoEmit: note['infoEmit'],
            impostoTotal: note['impostoTotal'],
            infoTranspor: note['infoTranspor'],
            produtosArray: note['produtosArray'],
            createAt: utils.getCurrentTime(),
            deleted: false,
            deletedAt: null,
          })

          _invoiceSaved.push(_invoice)
          await buildInventoryProductList(note['produtosArray'], inventoryProducts)
        } else {
          invoiceRejections.push({
            infoReject: 'This invoice note has been registered in database',
            infoNota: note['infoNota'],
          })
        }

        if (inventoryProducts.length) {
          inventory
            .addProducts({ cnpj: note.infoNota.cnpjEmpresa, items: inventoryProducts })
            .then()
        }

        // for (const product of note['produtosArray']) {
        //   const verifyIfExistReferenceForCurrentProduct = await ProductsProvider
        // }
      }

      res.status(200).json({
        status: true,
        acceptNotes: _invoiceSaved,
        rejectionNotes: invoiceRejections,
      })
    } catch (error) {
      console.error(error)
      res.status(400).json({
        status: error,
        error: 'Bad request, try letter',
      })
    }
  }
}

module.exports = InvoiceNotes
