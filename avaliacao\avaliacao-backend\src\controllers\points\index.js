const moment = require('moment-timezone')
const PointsModel = require('../../models/Points')
const { decrypt, encrypt } = require('../../utils/crypto')
const EnterpriseModel = require('../../models/Empresa')

class Points {
  constructor() {}

  async createPoints(req, res) {
    const { cpf, name, points, type, referenceId } =
      decrypt(req?.body?.data, process.env.PRIVATE_KEY) || {}

    const existPoints = await PointsModel.findOne({ referenceId }).lean()

    if (existPoints) {
      return res.status(400).json({
        status: false,
        error: 'Already exist points created for this evaluation.',
      })
    }

    if (type === 'debit') {
      const alreadyExistsPoints = await PointsModel.aggregate([
        {
          $match: {
            cpf,
          },
        },
        {
          $group: {
            _id: '$type',
            totalPoints: { $sum: '$points' },
          },
        },
      ])

      const restPoints = alreadyExistsPoints.reduce((acc, point) => {
        if (point._id === 'credit') {
          acc += point.totalPoints
        } else {
          acc -= point.totalPoints
        }
        return acc
      }, 0)

      if (points > restPoints) {
        return res.status(403).json({ message: 'This client does not have points.' })
      }
    }

    try {
      const company = await EnterpriseModel.findOne({ cnpj: req?.usuario?.enterprise?.cnpj }).lean()
      const isPointsProgram = company?.settings?.pointsProgram || false
      if (isPointsProgram) {
        const currentDate = moment.utc().subtract({ hours: 3 }).toDate()

        let pointsToAdd = {
          cpf,
          name,
          points,
          type,
          createdAt: currentDate,
          updatedAt: currentDate,
          origin: 'Avaliação',
          referenceId,
          cnpj: req?.usuario?.enterprise?.cnpj,
        }

        if (type === 'credit') {
          pointsToAdd.expiredAt = moment.utc().subtract({ hours: 3 }).add({ days: 180 }).toDate()
        }

        const pointsAdded = await PointsModel.create(pointsToAdd)

        return res.status(200).json({
          status: true,
          points: encrypt(pointsAdded, process.env.PRIVATE_KEY).toString(),
        })
      } else {
        return res.status(200).json({
          status: true,
          points: encrypt({}, process.env.PRIVATE_KEY).toString(),
        })
      }
    } catch (error) {
      console.log(error)
      return res.status(400).json({
        status: false,
        error: 'Error on registering points',
      })
    }
  }

  async getAllPointsByClient(req, res) {
    const { cpf } = req.params

    try {
      const points = await PointsModel.find({
        cpf,
        cnpj: req?.usuario?.enterprise?.cnpj,
        isCanceled: false,
        $or: [{ expiredAt: { $gte: moment.utc().toDate() } }, { expiredAt: null }],
      }).lean()

      const totalPoints = points.reduce((acc, point) => {
        if (point?.type === 'credit') {
          return acc + point?.points
        }

        if (point?.type === 'debit') {
          return acc - point?.points
        }
      }, 0)

      return res.status(200).json({
        points: Math.round(Math.ceil(totalPoints)),
      })
    } catch (error) {
      console.log(error)
      return res.status(400).json({
        status: false,
        error: 'Error on get points of this client.',
      })
    }
  }

  async getPointsFromEvaluation(req, res) {
    const { evaluationId } = req.params

    try {
      const evaluationPoints = await PointsModel.findOne({ referenceId: evaluationId })
      return res.status(200).json({ points: evaluationPoints?.points || 0 })
    } catch (error) {
      console.log(error)
      return res.status(400).json({
        status: false,
        error: 'Error on get point from evaluation',
      })
    }
  }
}

module.exports = Points
