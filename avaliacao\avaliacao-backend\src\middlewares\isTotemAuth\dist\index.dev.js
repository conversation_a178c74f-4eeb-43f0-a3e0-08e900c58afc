"use strict";function _slicedToArray(arr, i) {return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _nonIterableRest();}function _nonIterableRest() {throw new TypeError("Invalid attempt to destructure non-iterable instance");}function _iterableToArrayLimit(arr, i) {if (!(Symbol.iterator in Object(arr) || Object.prototype.toString.call(arr) === "[object Arguments]")) {return;}var _arr = [];var _n = true;var _d = false;var _e = undefined;try {for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {_arr.push(_s.value);if (i && _arr.length === i) break;}} catch (err) {_d = true;_e = err;} finally {try {if (!_n && _i["return"] != null) _i["return"]();} finally {if (_d) throw _e;}}return _arr;}function _arrayWithHoles(arr) {if (Array.isArray(arr)) return arr;}var jwt = require('jsonwebtoken');
var routerPaths = require('../validationRoutersConfig/index');
var RefreshTokenModel = require('../../models/RefreshToken');var _require =
require('../../config/jwt'),SECRET_JWT = _require.SECRET_JWT;
var moment = require('moment');

module.exports = function _callee(request, response, next) {var _ref, auth, _auth$split, _auth$split2, token, decodeJWT, refreshToken;return regeneratorRuntime.async(function _callee$(_context) {while (1) {switch (_context.prev = _context.next) {case 0:_ref =
          request.headers || {}, auth = _ref.authorization;if (

          auth) {_context.next = 3;break;}return _context.abrupt("return",
          response.status(401).json({
            code: 130,
            message: 'Token não definido!' }));case 3:_auth$split =



          auth.split(' '), _auth$split2 = _slicedToArray(_auth$split, 2), token = _auth$split2[1];_context.prev = 4;


          decodeJWT = jwt.verify(token, SECRET_JWT);
          request.usuario = decodeJWT;_context.next = 9;return regeneratorRuntime.awrap(

          RefreshTokenModel.findOne({ id_user: decodeJWT?._id }).lean());case 9:refreshToken = _context.sent;if (!(


          !refreshToken ||
          refreshToken?.accessToken?.[0] !== token ||
          moment.utc().isAfter(moment.utc((refreshToken?.expiredAt))))) {_context.next = 12;break;}return _context.abrupt("return",

          response.status(401).json({
            code: 130,
            message: 'Token invalido!' }));case 12:



          next();_context.next = 18;break;case 15:_context.prev = 15;_context.t0 = _context["catch"](4);return _context.abrupt("return",

          response.status(401).json({
            code: 130,
            message: 'Token invalido!' }));case 18:case "end":return _context.stop();}}}, null, null, [[4, 15]]);};