const { model, Schema } = require("mongoose");

const ReviewsSchema = new Schema({
  type: {
    type: String,
  },
  referenceId: {
    type: String,
  },
  points: {
    type: Number,
  },
  cpf: {
    type: String,
    default: null,
  },
  name: {
    type: String,
    default: null,
  },
  evaluator: {
    type: String,
    default: null,
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  observation: {
    type: String,
    default: null,
  },
});

const ReviewsModel = model("review", ReviewsSchema);
module.exports = ReviewsModel
