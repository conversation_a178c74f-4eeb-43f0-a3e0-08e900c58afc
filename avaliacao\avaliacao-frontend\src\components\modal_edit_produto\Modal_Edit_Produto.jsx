import React, { Component } from 'react'
import Modal from 'react-modal'
import { toast } from 'react-toastify'
import ApiService from '../../services/ApiService'
import './Modal_Edit_Produto.css'
import InputMask from 'react-input-mask'
import {
  formatNumber,
  formatNumberForPriceType,
  formatPriceNumber,
} from '../../utils/formatValuesUtils'
import { Tooltip as ReactTooltip } from 'react-tooltip'
import { AuthContext } from '../../context/auth'
import * as BiIcons from 'react-icons/bi'
import { sleep } from '../../utils/sleepUtils'

const enquadIpi = [
  { codigoEnq: 999 },
  { codigoEnq: 601 },
  { codigoEnq: 602 },
  { codigoEnq: 603 },
  { codigoEnq: 604 },
  { codigoEnq: 605 },
  { codigoEnq: 606 },
  { codigoEnq: 607 },
  { codigoEnq: 608 },
]

class Modal_Edit_Produto extends Component {
  static contextType = AuthContext
  constructor(props) {
    super(props)
    this.state = {
      toggleState: 1,
      resultCsosnCtr: [],
      origensMercadorias: [],
      situcaoTributariaIpi: [],
      SitucaoTributariaPis: [],
      produtosPesquisa: [],
      produtoSelecionado: '',
      cnpj: '',
      //infoProduto
      codigoProdutoFornecedor: '',
      descricaoProdutoInterno: '',
      cEanProduto: '',
      codigoProdutoInterno: '',
      cEanTributo: '',
      xProd: '',
      xPed: '',
      qCom: '',
      uCom: '',
      vUnCom: '',
      vProd: 0,
      vDesc: '',
      naturezaDeCompra: '',
      cfop: '',
      ncmProduto: '',
      //Imposto
      icmsOrig: '',
      icmsCST: '',
      icmsModBC: '',
      icmsVBc: '',
      icmsPICMS: '',
      icmsVICMS: '',
      ipiAliq: 0,
      ipiCEnq: 0,
      ipiVBC: 0,
      ipiVIpi: 0,
      ipiCST: 0,
      pisCST: 0,
      pisBc: 0,
      pisvBC: 0,
      pisPCofins: 0,
      pisVPIS: 0,
      aliqPIS: 0,
      valorFixoPIS: 0,
      cofinsCST: 0,
      cofinsVBC: 0,
      cofinsPCOFINS: 0,
      cofinsVCOFINS: 0,
      valorBaseICMS: 0,
      produtosSelecionados: [],
      openMiniModal: false,
      isSelect: false,
      selecionadoCodigo: 0,
      selecionadoProduto: 0,
    }
  }

  async componentDidMount() {
    const { user } = this.context

    const origens = await ApiService.Origens()
    if (origens?.data) {
      this.setState({ origensMercadorias: origens.data.data })
    }
    const situcaoIpi = await ApiService.SitucaoTributariaIpi()
    if (situcaoIpi?.data) {
      this.setState({ situcaoTributariaIpi: situcaoIpi.data.data })
    }

    const situcaoPis = await ApiService.SitucaoTributariaPis()
    if (situcaoPis?.data) {
      this.setState({ SitucaoTributariaPis: situcaoPis.data.data })
    }

    this.setState({ cnpj: user?.enterprise?.cnpj })
    const response = await ApiService.GetItems()
    const produtosFiltrados = []

    if (response?.data && Array.isArray(response?.data)) {
      response.data?.filter((item) => item?.novo || item.descricao?.toLowerCase()?.includes(String('NOVO').toLowerCase())).forEach((item) => {
        const produtos = {
          codigo: item.codigo,
          produto: item.descricao,
        }
        produtosFiltrados.push(produtos)
      })
    }

    // const produtosNovos = produtosFiltrados.filter((item) =>
    //   item.produto?.toLowerCase()?.includes(String('NOVO').toLowerCase())
    // )
    this.setState({ produtosPesquisa: produtosFiltrados })
    this.validaCrt()
    if (this.props.openModal === true) {
      this.infosModal()
    }
  }

  toggleTab = (index) => {
    this.setState({ toggleState: index })
  }

  subToggleTab = (index) => {
    this.setState({ subToggleState: index })
  }

  async infosModal() {
    const { product } = this.props
    const codigoProdutoFornecedor = product.codigoProduto
    const cEanProduto = product.cEanProduto
    const codigoProdutoInterno = product.codigoProdutoInterno
    const xProd = product.xProd
    const xPed = product.xPed
    const qCom = product.qCom
    const uCom = product.uCom
    const vUnCom = product.vUnCom
    const vProd = product.vProd
    const vDesc = product.vDesc
    const naturezaDeCompra = product.naturezaDeCompra
    const cfop = product.cfop
    const ncmProduto = product.ncmProduto
    const cEanTributo = product.cEanTributo
    const icmsOrig = product.imposto.ICMS.orig ? product.imposto.ICMS.orig : 0
    const icmsCST = product.imposto.ICMS.CSOSN ? product.imposto.ICMS.CSOSN : 0
    const icmsModBC = product.imposto.ICMS.modBC
      ? product.imposto.ICMS.modBC
      : 0
    const icmsVBc = product.imposto.ICMS.vBC ? product.imposto.ICMS.vBC : 0
    const icmsPICMS = product.imposto.ICMS.pICMS
      ? product.imposto.ICMS.pICMS
      : 0
    const icmsVICMS = product.imposto.ICMS.vICMS
      ? product.imposto.ICMS.vICMS
      : 0
    const ipiCEnq = product.imposto.IPI.cEnq ? product.imposto.IPI.cEnq : 0
    const ipiVBC = product.imposto.IPI.vBC ? product.imposto.IPI.vBC : 0
    const ipiVIpi = product.imposto.IPI.vIPI ? product.imposto.IPI.vIPI : 0
    const ipiCST = product.imposto.IPI.CST ? product.imposto.IPI.CST : 0
    const pisCST = product.imposto.PIS.CST ? product.imposto.PIS.CST : 0
    const pisvBC = product.imposto.PIS.vBC ? product.imposto.PIS.vBC : 0
    const pisPCofins = product.imposto.PIS.pPIS ? product.imposto.PIS.pPIS : 0
    const pisVPIS = product.imposto.PIS.vPIS ? product.imposto.PIS.vPIS : 0
    const cofinsCST = product.imposto.COFINS.CST
      ? product.imposto.COFINS.CST
      : 0
    const cofinsVBC = product.imposto.COFINS.vBC
      ? product.imposto.COFINS.vBC
      : 0
    const cofinsPCOFINS = product.imposto.COFINS.pCOFINS
      ? product.imposto.COFINS.pCOFINS
      : 0
    const cofinsVCOFINS = product.imposto.COFINS.vCOFINS
      ? product.imposto.COFINS.vCOFINS
      : 0
    this.setState({
      codigoProdutoFornecedor: codigoProdutoFornecedor,
      cEanProduto: cEanProduto,
      cEanTributo: cEanTributo,
      codigoProdutoInterno: codigoProdutoInterno,
      xProd: xProd,
      xPed: xPed,
      qCom: qCom,
      uCom: uCom,
      vUnCom: vUnCom,
      vProd: vProd,
      vDesc: vDesc,
      naturezaDeCompra: naturezaDeCompra,
      cfop: cfop,
      ncmProduto: ncmProduto,
      //imposto
      icmsOrig: icmsOrig,
      icmsCST: icmsCST,
      icmsModBC: icmsModBC,
      icmsPICMS: icmsPICMS,
      icmsVICMS: icmsVICMS,
      ipiCEnq: ipiCEnq,
      ipiVBC: ipiVBC,
      ipiVIpi: ipiVIpi,
      ipiCST: ipiCST,
      pisCST: pisCST,
      pisvBC: pisvBC,
      pisPCofins: pisPCofins,
      pisVPIS: pisVPIS,
      cofinsCST: cofinsCST,
      cofinsVBC: cofinsVBC,
      cofinsPCOFINS: cofinsPCOFINS,
      cofinsVCOFINS: cofinsVCOFINS,
    })

    await sleep(500)
    //codigos fazem  a mesma coisa que a function calculosIcms(), mas aqui ele calcula via importação XML
    const baseXml = icmsVBc * 100
    const resultBaseIcms = baseXml / vProd
    this.setState({ icmsVBc: resultBaseIcms })
    const convertPorcentagem = resultBaseIcms / 100
    const resultValorIcms = vProd * convertPorcentagem
    this.setState({ valorBaseICMS: resultValorIcms })
  }

  async validaCrt() {
    const Csons_1 = await ApiService.CsosnCrt1()
    const Csons_2 = await ApiService.CsosnCrt2()
    if (!Csons_1?.data || !Csons_2?.data) {
      return
    }

    const crtStorage = localStorage.getItem('crt') || ''
    if (String(crtStorage) === '1') {
      this.setState({ resultCsosnCtr: Csons_1.data.data })
    } else {
      this.setState({ resultCsosnCtr: Csons_2.data.data })
    }
  }

  calculosIcms = ({ target }) => {
    const { name, value } = target
    const { vProd, valorBaseICMS } = this.state
    if (value > 100) {
      toast.error('Valor inválido')
    } else {
      if (name === 'base-icms') {
        var convertPorcentagem = value / 100
        var resultBaseIcms = vProd * convertPorcentagem
        this.setState({ valorBaseICMS: resultBaseIcms })
      }
      if (name === 'icms') {
        const convertIcmsPorcentagem = value / 100
        const resultIcms = valorBaseICMS * convertIcmsPorcentagem
        this.setState({ icmsVICMS: resultIcms })
      }
    }
  }

  saveProduct = (event) => {
    const { produtosArray, product } = this.props
    const {
      uCom,
      qCom,
      vUnCom,
      // vProd,
      // vDesc,
      codigoProdutoFornecedor,
      xProd,
      naturezaDeCompra,
      cfop,
      ncmProduto,
      cEanProduto,
      // cEanTributo,
      cofinsVBC,
      cofinsVCOFINS,
      pisvBC,
    } = this.state
    // console.table({
    //   uCom,
    //   qCom,
    //   vUnCom,
    //   codigoProdutoFornecedor,
    //   xProd,
    //   naturezaDeCompra,
    //   cfop,
    //   ncmProduto,
    //   cEanProduto,
    //   cofinsVBC,
    //   cofinsVCOFINS,
    //   pisvBC,
    // })
    let notValidate = []
    const fieldsValidate = {
      uCom,
      qCom,
      vUnCom,
      codigoProdutoFornecedor,
      xProd,
      naturezaDeCompra,
      cfop,
      ncmProduto,
      cEanProduto,
    }

    Object.entries(fieldsValidate).forEach(([key, value]) => {
      if (value) return
      notValidate[key] = value
    })

    const errorToast = {
      uCom: 'Unidade',
      qCom: 'Quantidade',
      vUnCom: 'Valor unitário',
      codigoProdutoFornecedor: 'Código Fornecedor',
      xProd: 'Descrição Fornecedor',
      naturezaDeCompra: 'Natureza da Operação',
      cfop: 'CFOP',
      ncmProduto: 'NCM',
      cEanProduto: 'Código EAN',
    }

    let messageToast = ''

    Object.entries(notValidate).forEach(([key]) => {
      messageToast += ` ${errorToast[key]} /`
    })

    if (Object.keys(notValidate).length) {
      return toast.error(
        `Por favor preencha os campos ${messageToast} corretamente.`,
        {
          position: toast.POSITION.TOP_CENTER,
          autoClose: 2000,
        }
      )
    }

    const findProducts = Object.values(produtosArray).filter(
      (item) => String(item.codigoProduto) === String(product.codigoProduto)
    )

    if (!findProducts.length) {
      return toast.error('Falha ao encontrar o produto, tente novamente.', {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 2000,
      })
    }

    var oldTotal = parseFloat(this.props.product.vProd)
    var newTotal = parseFloat(this.state.qCom * this.state.vUnCom)
    if (this.state.vUnCom > this.props.product.vUnCom) {
      this.props.impostoArray.valorProdutoImpostoTotal -= oldTotal
      this.props.impostoArray.valorTotalDaNotaFiscalImpostoTotal -= oldTotal
      this.props.impostoArray.valorProdutoImpostoTotal += newTotal
      this.props.impostoArray.valorTotalDaNotaFiscalImpostoTotal += newTotal
      this.props.product.vProd -= oldTotal
    } else if (
      this.state.qCom > this.props.product.qCom ||
      this.state.qCom < this.props.product.qCom
    ) {
      this.props.impostoArray.valorProdutoImpostoTotal -= oldTotal
      this.props.impostoArray.valorTotalDaNotaFiscalImpostoTotal -= oldTotal
      this.props.impostoArray.valorProdutoImpostoTotal += newTotal
      this.props.impostoArray.valorTotalDaNotaFiscalImpostoTotal += newTotal
      this.props.product.vProd -= oldTotal
    }

    findProducts.forEach((item) => {
      const formatxPed = this.state.xPed
        ? formatNumberForPriceType(this.state.xPed)
        : ''
      item.xPed = Number(formatxPed)
      item.codigoProduto = this.state.codigoProdutoFornecedor
      item.xProd = this.state.xProd
      item.cEanProduto = Number(this.state.cEanProduto)
      item.cEanTributo = this.state.cEanTributo
      item.qCom = this.state.qCom
      item.uCom = this.state.uCom
      item.vUnCom = parseFloat(this.state.vUnCom)
      item.vProd = parseFloat(this.state.qCom * this.state.vUnCom)
      item.vDesc = this.state.vDesc
      item.naturezaDeCompra = this.state.naturezaDeCompra
      item.cfop = this.state.cfop
      item.ncmProduto = this.state.ncmProduto
      item.codigoProdutoInterno = this.state.codigoProdutoInterno
      item.produtoInternoDescricao = this.state.descricaoProdutoInterno
      item.imposto.ICMS.orig = this.state.icmsOrig
      item.imposto.ICMS.CST = this.state.icmsCST
      item.imposto.ICMS.vBC = this.state.valorBaseICMS
      item.imposto.ICMS.vICMS = this.state.icmsVICMS
      item.imposto.IPI.CST = this.state.ipiCST
      item.imposto.IPI.vIPI = this.state.ipiVIpi
      item.imposto.IPI.vBC = this.state.ipiVBC
      item.imposto.IPI.cEnq = this.state.ipiCEnq
      item.imposto.PIS.CST = this.state.pisCST
      item.imposto.PIS.vBC = this.state.pisBc
      item.imposto.PIS.vPIS = this.state.pisvBC
      item.imposto.COFINS.CST = this.state.cofinsCST
      item.imposto.COFINS.vBC = this.state.cofinsVCOFINS
      item.imposto.COFINS.vCOFINS = this.state.cofinsVBC
    })

    toast.success('Produto Salvo', {
      position: toast.POSITION.TOP_CENTER,
      autoClose: 2000,
    })
    this.props.closeModal()
  }

  handleSelect = (product) => {
    this.setState({ codigoProdutoInterno: product })
  }

  abrirMiniModal() {
    if (this.state.produtosSelecionados.length > 0) {
      this.setState({ openMiniModal: true })
    } else {
      toast.error('Produto não foi encontrado!!', {
        position: toast.POSITION.TOP_CENTER,
        autoClose: 2000,
      })
      this.setState({ openMiniModal: false })
    }
  }

  getProduto(event) {
    const result = this.state.produtosPesquisa
      .sort()
      .filter((e) =>
        e.produto.toString().toLowerCase().includes(event.toLowerCase())
      )
    this.setState({ produtosSelecionados: result })
  }

  salvarCodigoProduct() {
    if (!this.state.selecionadoCodigo) {
      return toast.error('Por favor selecione o produto!!')
    }

    this.setState({
      codigoProdutoInterno: this.state.selecionadoCodigo,
      descricaoProdutoInterno: this.state.selecionadoProduto,
      openMiniModal: false,
    })
    toast.success('Produto Selecionado!!')
  }

  miniModal = () => {
    return (
      <Modal
        isOpen={this.state.openMiniModal}
        className="container-mini-modal"
        overlayClassName="overlay"
        ariaHideApp={false}
      >
        <div className="container-borda-mini-modal">
          <ol className="rounded-list">
            {this.state.produtosSelecionados.map((item, index) => (
              <li key={index} className="produtos-selecion">
                <a
                  href="# "
                  value={item.codigo}
                  style={
                    this.state.isSelect &&
                      String(this.state.selecionadoCodigo) === String(item.codigo)
                      ? { background: '#eea405', color: 'white' }
                      : null
                  }
                  className="product-mini-container"
                  onClick={(e) => {
                    e.preventDefault()
                    this.setState({
                      isSelect: this.state.isSelect === true ? false : true,
                      selecionadoCodigo: item.codigo,
                      selecionadoProduto: item.produto,
                    })
                  }}
                >
                  {item.produto}
                </a>
              </li>
            ))}
          </ol>

          <div className="footer-modal-edit-product">
            <button
              className="button-cancelar-product"
              onClick={() => this.setState({ openMiniModal: false })}
            >
              Cancelar
            </button>

            <button
              className="button-salvar-product"
              onClick={() => this.salvarCodigoProduct()}
            >
              Selecionar
            </button>
          </div>
        </div>
      </Modal>
    )
  }

  render() {
    const {
      resultCsosnCtr,
      origensMercadorias,
      situcaoTributariaIpi,
      SitucaoTributariaPis,
    } = this.state

    return (
      <Modal
        isOpen={this.props.openModal}
        contentLabel="Modal"
        className="container-modal"
        overlayClassName="overlay"
        ariaHideApp={false}
      >
        {this.state.openMiniModal === true ? this.miniModal() : ''}

        <div className="container-modal-edit-produt">
          <div className="container-pesquisa">
            <div className="input-search-modal">
              <input
                className="input-modal"
                onChange={(event) => this.getProduto(event.target.value)}
                placeholder="Pesquise um produto interno..."
              />
              <button
                className="input-search-modal-edit"
                onClick={() => this.abrirMiniModal()}
              >
                <BiIcons.BiSearch size={20} />
              </button>
            </div>
            <button
              onClick={() => this.props.closeModal()}
              className="close-modal-modal-edit-teste"
            >
              X
            </button>
          </div>

          <div className="container-tabs">
            <div className="bloc-tabs">
              <a
                href="# "
                className={
                  this.state.toggleState === 1 ? 'tabs active-tabs' : 'tabs'
                }
                onClick={() => this.toggleTab(1)}
              >
                Produto
              </a>
              <a
                href="# "
                data-tip="Imposto sobre Circulação de Mercadorias e Serviços."
                data-for="notas-modal-tip"
                data-effect="solid"
                className={
                  this.state.toggleState === 2 ? 'tabs active-tabs' : 'tabs'
                }
                onClick={() => this.toggleTab(2)}
              >
                ICMS
              </a>
              <a
                href="# "
                data-tip="Imposto sobre Produtos Industrializados."
                data-for="notas-modal-tip"
                data-effect="solid"
                className={
                  this.state.toggleState === 3 ? 'tabs active-tabs' : 'tabs'
                }
                onClick={() => this.toggleTab(3)}
              >
                IPI
              </a>
              <a
                href="# "
                data-tip="Receita bruta + soma das alíquotas."
                data-for="notas-modal-tip"
                data-effect="solid"
                className={
                  this.state.toggleState === 4 ? 'tabs active-tabs' : 'tabs'
                }
                onClick={() => this.toggleTab(4)}
              >
                PIS/COFINS
              </a>
            </div>

            <div className="content-tabs">
              <div
                className={
                  this.state.toggleState === 1
                    ? 'content  active-content'
                    : 'content'
                }
              >
                <div className="container-btn-tab">
                  <div className="box-input">
                    <span className="titulo_input">Código Fornecedor</span>
                    <input
                      type="text"
                      name="codigo-fornecedor"
                      id="codigo-fornecedor"
                      className="input-modal required"
                      maxLength="120"
                      defaultValue={this.state.codigoProdutoFornecedor}
                      onChange={(e) =>
                        this.setState({
                          codigoProdutoFornecedor: e.target.value,
                        })
                      }
                      style={{ width: '200px' }}
                    />
                  </div>

                  <div className="box-input">
                    <span className="titulo_input">Descrição Fornecedor</span>
                    <input
                      type="text"
                      name="descricao-fornecedor"
                      id="descricao-fornecedor"
                      className="input-modal required"
                      defaultValue={this.state.xProd}
                      onChange={(e) => this.setState({ xProd: e.target.value })}
                      maxLength="120"
                      style={{ width: '200px' }}
                    />
                  </div>

                  <div className="box-input">
                    <span className="titulo_input">Quantidade</span>
                    <input
                      type="text"
                      name="quantidade"
                      defaultValue={this.state.qCom}
                      id="quantidade"
                      className="input-modal required"
                      onKeyUp={(e) =>
                        (e.target.value = formatNumber(e.target.value))
                      }
                      onChange={(e) =>
                        this.setState({
                          qCom: Number(formatNumber(e.target.value)),
                        })
                      }
                      maxLength="120"
                      style={{ width: '200px' }}
                    />
                  </div>

                  <div className="box-input">
                    <span className="titulo_input">Unidade</span>
                    <input
                      type="text"
                      name="unidade"
                      defaultValue={this.state.uCom}
                      onChange={(e) => this.setState({ uCom: e.target.value })}
                      id="unidade"
                      className="input-modal required"
                      maxLength="120"
                      style={{ width: '200px' }}
                    />
                  </div>

                  <div className="box-input">
                    <span className="titulo_input">Valor Unitário</span>
                    <div className="modal-edit-input-formated">
                      <span>R$</span>
                      <input
                        type="text"
                        name="valor-unitario"
                        id="valor-unitario"
                        className="input-modal required"
                        defaultValue={this.state.vUnCom}
                        onBlur={(event) => {
                          const formatedValue = formatNumberForPriceType(
                            event.target.value
                          )
                          this.setState({ vUnCom: Number(formatedValue) })
                        }}
                        onChange={(event) =>
                          this.setState({ vUnCom: event.target.value })
                        }
                        maxLength="120"
                        style={{ width: '200px' }}
                      />
                    </div>
                  </div>

                  <div className="box-input">
                    <span className="titulo_input">Valor Desconto</span>
                    <div className="modal-edit-input-formated">
                      <span>R$</span>
                      <input
                        type="text"
                        name="valor-desconto"
                        id="valor-desconto"
                        defaultValue={this.state.vDesc}
                        onBlur={(event) => {
                          const formatedValue = formatNumberForPriceType(
                            event.target.value
                          )
                          this.setState({ vDesc: Number(formatedValue) })
                        }}
                        onChange={(event) =>
                          this.setState({ vDesc: event.target.value })
                        }
                        className="input-modal"
                        maxLength="120"
                        style={{ width: '200px' }}
                      />
                    </div>
                  </div>

                  <div className="box-input">
                    <span className="titulo_input">Natureza da Operação</span>
                    <input
                      type="text"
                      name="natureza-de-operacao"
                      id="natureza-de-opercao"
                      className="input-modal required"
                      defaultValue={this.state.naturezaDeCompra}
                      onChange={(e) =>
                        this.setState({ naturezaDeCompra: e.target.value })
                      }
                      maxLength="120"
                      style={{ width: '200px' }}
                    />
                  </div>

                  <div className="box-input">
                    <span className="titulo_input">CFOP</span>
                    <input
                      type="text"
                      name="cfop"
                      id="cfop"
                      defaultValue={this.state.cfop}
                      onChange={(e) => this.setState({ cfop: e.target.value })}
                      className="input-modal required"
                      maxLength="120"
                      style={{ width: '200px' }}
                    />
                  </div>

                  <div className="box-input">
                    <span className="titulo_input">NCM</span>
                    <input
                      type="text"
                      name="ncm"
                      id="ncm"
                      className="input-modal required"
                      defaultValue={this.state.ncmProduto}
                      onChange={(e) =>
                        this.setState({ ncmProduto: e.target.value })
                      }
                      maxLength="120"
                      style={{ width: '200px' }}
                    />
                  </div>
                  <div className="box-input">
                    <span className="titulo_input">Código EAN</span>
                    <input
                      type="text"
                      name="ncm"
                      id="ncm"
                      className="input-modal required"
                      defaultValue={this.state.cEanProduto}
                      onKeyUp={(e) =>
                        (e.target.value = formatNumber(e.target.value))
                      }
                      onChange={(e) => {
                        this.setState({
                          cEanProduto: Number(formatNumber(e.target.value)),
                        })
                      }}
                      maxLength="120"
                      style={{ width: '200px' }}
                    />
                  </div>
                  <div className="box-input">
                    <span className="titulo_input">
                      Descrição Produto Interno
                    </span>
                    <input
                      type="text"
                      name="ncm"
                      id="ncm"
                      className="input-modal required"
                      defaultValue={this.state.descricaoProdutoInterno}
                      disabled
                      style={{ width: '200px' }}
                    />
                  </div>
                  <div className="box-input">
                    <span className="titulo_input">Código Produto Interno</span>
                    <input
                      type="text"
                      name="ncm"
                      id="ncm"
                      className="input-modal required"
                      disabled
                      value={this.state.codigoProdutoInterno}
                      style={{ width: '200px' }}
                    />
                  </div>
                </div>
              </div>

              <div
                className={
                  this.state.toggleState === 2
                    ? 'content  active-content'
                    : 'content'
                }
              >
                <div className="box-input">
                  <span className="titulo_input">
                    Código de Situação da Operação – Simples Nacional (CSOSN)
                  </span>
                  <select className="input-modal" value={this.state.icmsCST}>
                    {resultCsosnCtr.map((item, index) => {
                      return (
                        <option key={index} value={item.codigo}>
                          {item.descricao}
                        </option>
                      )
                    })}
                  </select>
                </div>

                <div className="box-input">
                  <span className="titulo_input">Origem</span>
                  <select
                    className="input-modal"
                    value={Number(this.state.icmsOrig)}
                  >
                    {origensMercadorias.map((item, index) => {
                      return (
                        <option key={index} value={item.codigo}>
                          {item.descricao}
                        </option>
                      )
                    })}
                  </select>
                </div>
                <div className="container-btn-tab">
                  <div className="box-input">
                    <span className="titulo_input">Valor Base ICMS</span>
                    <div className="modal-edit-input-formated">
                      <span>R$</span>
                      <input
                        type="text"
                        name="valor-base-icms"
                        id="valor-base-icms"
                        disabled
                        defaultValue={this.state.valorBaseICMS}
                        className="input-modal"
                        maxLength="120"
                        style={{ width: '200px' }}
                      />
                    </div>
                  </div>

                  <div className="box-input">
                    <span className="titulo_input">% ICMS</span>
                    <input
                      type="text"
                      name="icms"
                      id="icms"
                      onChange={this.calculosIcms}
                      className="input-modal"
                      maxLength="120"
                      style={{ width: '200px' }}
                    />
                  </div>

                  <div className="box-input">
                    <span className="titulo_input">Valor ICMS</span>
                    <div className="modal-edit-input-formated">
                      <span>R$</span>
                      <input
                        type="text"
                        name="valor-icms"
                        disabled
                        defaultValue={this.state.icmsVICMS}
                        id="valor-icms"
                        className="input-modal"
                        maxLength="120"
                        style={{ width: '200px' }}
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div
                className={
                  this.state.toggleState === 3
                    ? 'content  active-content'
                    : 'content'
                }
              >
                <div className="box-input">
                  <span className="titulo_input">
                    Situação tributária do IPI
                  </span>

                  <select
                    onChange={(e) => this.setState({ ipiCST: e.target.value })}
                    className="input-modal"
                    value={this.state.ipiCST}
                  >
                    {situcaoTributariaIpi.map((item, index) => {
                      return (
                        <option key={index} value={item.codigo}>
                          {item.descricao}
                        </option>
                      )
                    })}
                  </select>
                </div>

                <div className="container-btn-tab">
                  <div className="box-input">
                    <span className="titulo_input">Valor IPI</span>
                    <div className="modal-edit-input-formated">
                      <span>R$</span>
                      <input
                        type="text"
                        name="codigo-interno"
                        id="valor-ipi"
                        className="input-modal"
                        value={this.state.ipiVIpi}
                        onKeyUp={(e) =>
                          (e.target.value = formatPriceNumber(e.target.value))
                        }
                        onBlur={(e) =>
                          (e.target.value = formatPriceNumber(e.target.value))
                        }
                        onChange={(event) =>
                          this.setState({
                            ipiVIpi: Number(
                              formatPriceNumber(event.target.value)
                            ),
                          })
                        }
                        maxLength="120"
                        style={{ width: '250px' }}
                      />
                    </div>
                  </div>

                  <div className="box-input">
                    <span className="titulo_input">Valor Base IPI</span>
                    <div className="modal-edit-input-formated">
                      <span>R$</span>
                      <input
                        type="text"
                        name="codigo-interno"
                        id="valor-ipi"
                        className="input-modal"
                        value={this.state.ipiVBC}
                        onKeyUp={(e) =>
                          (e.target.value = formatPriceNumber(e.target.value))
                        }
                        onBlur={(e) =>
                          (e.target.value = formatPriceNumber(e.target.value))
                        }
                        onChange={(event) =>
                          this.setState({
                            ipiVBC: Number(
                              formatPriceNumber(event.target.value)
                            ),
                          })
                        }
                        maxLength="120"
                        style={{ width: '100px' }}
                      />
                    </div>
                  </div>

                  <div className="box-input">
                    <span className="titulo_input">Cód Enquad. IPI</span>
                    <select
                      className="input-modal"
                      value={this.state.ipiCEnq}
                      style={{ width: '250' }}
                      onChange={(e) =>
                        this.setState({ ipiCEnq: e.target.value })
                      }
                    >
                      {enquadIpi.map((item, index) => {
                        return (
                          <option key={index} value={item.codigoEnq}>
                            {item.codigoEnq}
                          </option>
                        )
                      })}
                    </select>
                  </div>
                </div>
              </div>
              <div
                className={
                  this.state.toggleState === 4
                    ? 'content  active-content'
                    : 'content'
                }
              >
                <div className="box-input">
                  <span className="titulo_input">
                    Situação tributária do PIS
                  </span>
                  <select
                    value={this.state.pisCST}
                    style={{ width: '100%' }}
                    className="input-modal"
                    onChange={(e) => this.setState({ pisCST: e.target.value })}
                  >
                    {SitucaoTributariaPis.map((item, index) => {
                      return (
                        <option key={index} value={item.codigo}>
                          {item.descricao}
                        </option>
                      )
                    })}
                  </select>
                </div>

                <div className="container-btn-tab-pis">
                  <div className="box-input">
                    <span className="titulo_input"> Base PIS</span>
                    <div className="modal-edit-input-formated">
                      <span>R$</span>
                      <input
                        type="text"
                        name="valor-pis"
                        id="valor-pis"
                        className="input-modal"
                        maxLength="120"
                        value={this.state.pisBc}
                        onBlur={(event) => {
                          const formatedValue = formatNumberForPriceType(
                            event.target.value
                          )
                          this.setState({ pisBc: Number(formatedValue) })
                        }}
                        onChange={(event) =>
                          this.setState({ pisBc: event.target.value })
                        }
                        style={{ width: '100px' }}
                      />
                    </div>
                  </div>

                  <div className="box-input">
                    <span className="titulo_input">Valor Base PIS</span>
                    <div className="modal-edit-input-formated">
                      <span>R$</span>
                      <input
                        type="text"
                        name="valor-pis"
                        id="valor-pis"
                        className="input-modal"
                        maxLength="120"
                        value={this.state.pisvBC}
                        onBlur={(event) => {
                          const formatedValue = formatNumberForPriceType(
                            event.target.value
                          )
                          this.setState({ pisvBC: Number(formatedValue) })
                        }}
                        onChange={(event) =>
                          this.setState({ pisvBC: event.target.value })
                        }
                        style={{ width: '100px' }}
                      />
                    </div>
                  </div>

                  <div className="box-input">
                    <span className="titulo_input">Valor PIS</span>
                    <div className="modal-edit-input-formated">
                      <span>R$</span>
                      <input
                        type="text"
                        name="valor-pis"
                        id="valor-pis"
                        className="input-modal"
                        maxLength="120"
                        value={this.state.pisvBC}
                        onBlur={(event) => {
                          const formatedValue = formatNumberForPriceType(
                            event.target.value
                          )
                          this.setState({ pisvBC: Number(formatedValue) })
                        }}
                        onChange={(event) =>
                          this.setState({ pisvBC: event.target.value })
                        }
                        style={{ width: '100px' }}
                      />
                    </div>
                  </div>
                </div>

                <div className="box-input">
                  <span className="titulo_input">
                    Situação tributária do COFINS
                  </span>
                  <select
                    style={{ width: '100%' }}
                    className="input-modal"
                    value={this.state.cofinsCST}
                    onChange={(e) =>
                      this.setState({ cofinsCST: e.target.value })
                    }
                  >
                    {SitucaoTributariaPis.map((item, index) => {
                      return (
                        <option key={index} value={item.codigo}>
                          {item.descricao}
                        </option>
                      )
                    })}
                  </select>
                </div>

                <div className="container-btn-tab-pis-confis">
                  <div className="box-input">
                    <span className="titulo_input">Valor COFINS</span>
                    <div className="modal-edit-input-formated">
                      <span>R$</span>
                      <input
                        type="text"
                        name="valor-cofins"
                        id="valor-cofins"
                        className="input-modal"
                        value={this.state.cofinsVCOFINS}
                        onBlur={(event) => {
                          const formatedValue = formatNumberForPriceType(
                            event.target.value
                          )
                          this.setState({
                            cofinsVCOFINS: Number(formatedValue),
                          })
                        }}
                        onChange={(event) =>
                          this.setState({ cofinsVCOFINS: event.target.value })
                        }
                        maxLength="120"
                        style={{ width: '150px' }}
                      />
                    </div>
                  </div>

                  <div className="box-input">
                    <span className="titulo_input">Valor Base COFINS</span>
                    <div className="modal-edit-input-formated">
                      <span>R$</span>
                      <input
                        type="text"
                        name="valor-cofins"
                        id="valor-cofins"
                        className="input-modal"
                        value={this.state.cofinsVBC}
                        onBlur={(event) => {
                          const formatedValue = formatNumberForPriceType(
                            event.target.value
                          )
                          this.setState({ cofinsVBC: Number(formatedValue) })
                        }}
                        onChange={(event) =>
                          this.setState({ cofinsVBC: event.target.value })
                        }
                        maxLength="120"
                        style={{ width: '150px' }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div className="footer-modal-edit-product">
            <button
              className="button-cancelar-product"
              onClick={() => this.props.closeModal()}
            >
              Cancelar
            </button>
            <button
              type="button"
              onClick={(event) => this.saveProduct(event)}
              className="button-salvar-product"
            >
              Salvar
            </button>
          </div>
        </div>
        <ReactTooltip
          id="notas-modal-tip"
          place="top"
          type="dark"
          effect="float"
          data-html="true"
        />
      </Modal>
    )
  }
}

export default Modal_Edit_Produto
