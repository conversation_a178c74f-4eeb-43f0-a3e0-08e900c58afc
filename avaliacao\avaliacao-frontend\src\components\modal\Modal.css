.header {
  font-size: 20px;
  font-weight: 600;
}

.body {
  padding-top: 15px;
  height: 525px;
  overflow-y: scroll;
  overflow-x: hidden;
  scrollbar-width: thin;
}

.body::-webkit-scrollbar {
  width: 5px;
}

.body::-webkit-scrollbar-track {
  box-shadow: inset 0 0 0 rgba(0, 0, 0, 0.3);
}

.body::-webkit-scrollbar-thumb {
  background-color: darkgrey;
}

.linha-descricao {
  display: flex;
  margin-bottom: 1rem;
}

.linha-descricao {
  display: flex;
  margin-bottom: 1rem;
}

.linha-valores {
  display: flex;
}

.body .linha-descricao .descricao {
  width: 90%;
}

.body .linha-valores .descricao {
  display: block;
  width: 109px;
}

.body .linha-descricao .checkbox {
  margin: 0 auto;
}

.footer {
  text-align: right;
}

.footer button {
  margin: auto 14px;
  padding: 8px 10px;
  background-color: #28d;
  color: white;
  border-radius: 5px;
  border: transparent;
  cursor: pointer;
  font-size: initial;
}

#btn-modal-cd-nvs {
  background-color: black;
}

#btn-modal-cd-nvs:hover {
  background-color: mediumseagreen;
}
