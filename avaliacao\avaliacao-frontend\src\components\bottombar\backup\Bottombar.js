import { Component } from 'react'

import './Bottombar.css'

import * as FontAwesomeIcon from 'react-icons/fa'
import ApiService from '../../services/ApiService'
import Swal from 'sweetalert2'
import { AuthContext } from '../../context/auth'
import { parseCookies } from 'nookies'
class Bottombar extends Component {
  static contextType = AuthContext
  constructor(props) {
    super(props)
    this.state = {
      tipoUsuario: null,
    }
  }

  componentDidMount = () => {
    this.setState({ tipoUsuario: tipoUsuario })
  }

  verificaAvaliacaoEmAberto = async () => {
    const { user } = this.context
    const { token } = parseCookies()
    try {
      const avaluation = await ApiService.GetOpenAvaliation(
        user?.enterprise?.cnpj,
        user?._id,
        token
      )
      return avaluation
    } catch (e) {}
  }

  async handleNavigation(pathUrl) {
    const chamouReforco = Boolean(localStorage.getItem('chamouReforco'))
    const verifyUrlAvaliation =
      window.location.pathname === '/lista_niveis' ||
      window.location.pathname === '/avaliacao' ||
      chamouReforco
    if (verifyUrlAvaliation) {
      if ((await this.verificaAvaliacaoEmAberto()) !== null) {
        Swal.fire(
          'Erro',
          'Não é possível mudar de página sem antes terminar a avaliação.',
          'error'
        )
        return
      }
    }
    this.props.history.push(pathUrl)
  }

  render() {
    const { tipoUsuario } = this.state
    let ehAdm = false
    if (tipoUsuario === 'administrador') ehAdm = true

    return (
      <div id="sb" className="hidden-print">
        <a onClick={() => this.handleNavigation('/')} style={{}}>
          <FontAwesomeIcon.FaTachometerAlt
            style={{ color: '#b3b3b3', fontSize: '1.5rem' }}
          />
        </a>
        <a onClick={() => this.handleNavigation('/avaliacoes_em_aberto')}>
          <FontAwesomeIcon.FaCopy
            style={{ color: '#b3b3b3', fontSize: '1.5rem' }}
          />
        </a>
        <a onClick={() => this.handleNavigation('/operacoes')}>
          <FontAwesomeIcon.FaCogs
            style={{ color: '#b3b3b3', fontSize: '1.5rem' }}
          />
        </a>
        <a onClick={() => this.handleNavigation('/etiquetas')}>
          <FontAwesomeIcon.FaPrint
            style={{ color: '#b3b3b3', fontSize: '1.5rem' }}
          />
        </a>
        <a onClick={() => this.handleNavigation('/caixas')}>
          <FontAwesomeIcon.FaCashRegister
            style={{ color: '#b3b3b3', fontSize: '1.5rem' }}
          />
        </a>
        {ehAdm && (
          <>
            <a onClick={() => this.handleNavigation('/cadastra_clientes')}>
              <FontAwesomeIcon.FaUserPlus
                style={{ color: '#b3b3b3', fontSize: '1.5rem' }}
              />
            </a>
            <a onClick={() => this.handleNavigation('/cadastra_niveis')}>
              <FontAwesomeIcon.FaLayerGroup
                style={{ color: '#b3b3b3', fontSize: '1.5rem' }}
              />
            </a>
          </>
        )}
      </div>
    )
  }
}

export default Bottombar
