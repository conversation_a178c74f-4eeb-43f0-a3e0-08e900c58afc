const { isValidObjectId } = require('mongoose')
const CidadesMunicipiosModel = require('./../../models/Cidades_Municipios')

class AddressClass {
  constructor() {}

  async getCities(req, res) {
    const cities = await CidadesMunicipiosModel.find(
      {},
      { _id: true, uf: true, sigla_uf: true, nome_uf: true }
    )

    return res.status(200).json({
      status: true,
      cities: cities,
    })
  }

  async getStateByCity(req, res) {
    const { cityId } = req.params

    if (!isValidObjectId(cityId))
      return res.status(400).json({
        status: true,
        error: 'ID of cities is invalid.',
      })

    const cityStates = await CidadesMunicipiosModel.findById({
      _id: cityId,
    })

    return res.status(200).json({
      status: true,
      cityStates: cityStates,
    })
  }

  async getAllStates(req, res) {
    const states = await CidadesMunicipiosModel.find(
      {},
      { _id: false, uf: 1, sigla: '$sigla_uf', nome: '$nome_uf' }
    )

    return res.status(200).json({
      status: true,
      states,
    })
  }

  async getAllCities(req, res) {
    const cities = await CidadesMunicipiosModel.aggregate([
      {
        $unwind: {
          path: '$cidades',
        },
      },
      {
        $project: {
          _id: 0,
          codigo_ibge: '$cidades.codigo_ibge',
          uf: 1,
          nome_municipio: '$cidades.nome_municipio',
        },
      },
    ])

    return res.status(200).json(cities)
  }

  async getCitiesByState(req, res) {
    const { stateId } = req.params

    if (!stateId)
      return res.status(400).json({
        status: true,
        error: 'ID of state is invalid.',
      })

    const cities = await CidadesMunicipiosModel.aggregate([
      {
        $match: { sigla_uf: stateId },
      },
      {
        $unwind: {
          path: '$cidades',
        },
      },
      {
        $project: {
          _id: 0,
          codigo_ibge: '$cidades.codigo_ibge',
          uf: 1,
          nome_municipio: '$cidades.nome_municipio',
        },
      },
    ])

    return res.status(200).json(cities)
  }
}

module.exports = AddressClass
