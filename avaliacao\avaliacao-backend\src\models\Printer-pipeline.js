const { model, Schema } = require('mongoose')

const printerPipelineSchema = new Schema({
  ownerPrinterCnpj: {
    type: String,
  },
  code: {
    type: String,
  },
  typeId: {
    type: String,
  },
  description: {
    type: String,
  },
  dataId: {
    type: String,
  },
  data: {
    type: Object,
  },
  boldValue: {
    type: Boolean,
  },
  boldDescriptionValue: {
    type: Boolean,
  },
  copyValues: {
    type: Number,
    default: 1,
  },
  create_at: {
    type: Date,
    default: Date.now,
  },
  update_at: {
    type: Date,
    default: null,
  },
  delete_at: {
    type: Date,
    default: null,
  },
})

module.exports = model('printer_pipeline', printerPipelineSchema)
