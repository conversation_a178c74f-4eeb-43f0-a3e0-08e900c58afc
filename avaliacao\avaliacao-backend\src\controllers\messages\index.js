const messageModel = require('../../models/Mensagem')
const getCurrentTime = require('../../utils/getCurrentTime')
const { encrypt } = require('../../utils/crypto');

class MessagesClass {
  constructor() { }

  async showMessages(req, res) {
    const { cnpj } = req.params

    if (!cnpj) {
      return res.status(400).json({ status: false, message: 'Cnpj inválido' })
    }

    try {
      const messages = await messageModel
        .find({
          companys: cnpj,
          destiny: 'Avaliacao',
        })
        .sort({ dataEnvio: -1 })
        .select({ companys: 0, __v: 0 })
        .limit(10)
        .lean()

      return res.status(200).json({
        data: encrypt(messages, process.env.PRIVATE_KEY).toString()
      })
    } catch (error) {
      return res.status(400).json(error)
    }
  }

  async confirmReadMessage(req, res) {
    const { cnpj } = req.body
    const { id } = req.params

    try {
      const visualizedCnpj = {
        cnpj,
        visualized_at: getCurrentTime(),
      }

      const messageDataUpdate = {
        visualized: visualizedCnpj,
      }

      const messageUpdated = await messageModel.findOneAndUpdate(
        { _id: id },
        { $push: messageDataUpdate },
        { upsert: true, new: true }
      )

      return res.status(200).json(messageUpdated)
    } catch (e) {
      return res.status(400).json(e)
    }
  }
}

module.exports = MessagesClass
