const { Router } = require('express')
const router = Router()

const isAuth = require('../../../middlewares/isAuth')

const CashiersClass = require('../../../controllers/cashier')
const CashiersController = new CashiersClass()

router.post('/reinforcement', isA<PERSON>, CashiersController.makeReinforcement)
router.post('/close', isAuth, CashiersController.makeClose)
router.get('/check/:id', isAuth, CashiersController.check)

module.exports = router
