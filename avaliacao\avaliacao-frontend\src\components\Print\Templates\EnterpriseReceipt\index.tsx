import extenso from "extenso";
import moment from "moment";
import { IPrintEnterpriseReceiptData } from "../../types";
// import "./styles.css";

interface EnterpriseReceiptProps {
  data: IPrintEnterpriseReceiptData;
}

const EnterpriseReceipt: React.FC<EnterpriseReceiptProps> = ({
  data: {
    client,
    generatedPoints,
    totalPoints,
    totalValue,
    type,
    evaluationDate,
    expirationDate,
  },
}) => {
  let ext;

  let x = 10;
  if (typeof totalValue !== "undefined") {
    if (totalValue !== null) {
      x = Number(totalValue);
    }
  }
  ext =
    String(x) === "(Não informado)"
      ? "Valor não informado"
      : extenso(x, { mode: "currency" });

  return (
    <div
      id="recibo_cliente"
      style={{
        display: "block",
        fontSize: "12px",
        fontFamily: "'Poppins', sans-serif",
      }}
    >
      <div
        className="header"
        style={{
          textAlign: "center",
          fontSize: "10px",
          fontWeight: "bold",
          margin: 0,
        }}
      >
        <h1 className="title" style={{ fontSize: "16px" }}>
          {">>"}RECIBO/
          {String(
            type === "Vale" ? import.meta.env.VITE_GIRACREDITO : type
          )?.toUpperCase()}
          {"<<"}
        </h1>
        --------------------------------------------
      </div>
      <div className="date">
        <p style={{ fontSize: "10px", margin: 0 }}>{evaluationDate}</p>
      </div>
      <div className="declaration">
        <p style={{ fontSize: "12px", margin: 0 }}>
          Eu, {client?.name}, CPF{" "}
          {client?.cpf?.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4")}{" "}
          declaro que, por ter vendido produtos seminovos para a CRESCI E PERDI,
          recebi a quantia de{" "}
          {totalValue === "(Não informado)"
            ? "Não informado"
            : `R$ ${totalValue},00 (${ext})`}
          , EM{" "}
          {String(
            type === "Vale" ? import.meta.env.VITE_GIRACREDITO : type
          ).toUpperCase()}
          . Declaro que os itens vendidos à CRESCI E PERDI são de minha
          propriedade e estou ciente que serão por ela comercializados a outrem,
          sem que eu tenha participação no negócio. Declaro por fim, que tive
          tempo hábil para avaliar o negócio que fiz, e tomei minha decisão de
          forma absolutamente consciente, não podendo dela legalmente me
          arrepender.
        </p>
      </div>
      <div className="info">
        <p style={{ fontSize: "12px", margin: 0 }}>
          ENDEREÇO: {client?.street}, {client?.neighborhood}
        </p>
        <p style={{ fontSize: "12px", margin: 0 }}>
          Data de Nascimento:{" "}
          {moment(client?.birthDate, "YYYY/MM/DD").format("DD/MM/YYYY")}
        </p>
        <p style={{ fontSize: "12px", margin: 0 }}>Fone: {client?.phone}</p>
        {totalPoints ? (
          <>
            <p style={{ fontSize: "12px", textAlign: "left", margin: 0 }}>
              Pontos gerados: {generatedPoints}
            </p>
            <p style={{ fontSize: "12px", textAlign: "left", margin: 0 }}>
              Total de pontos: {totalPoints}
            </p>
          </>
        ) : null}
        {expirationDate ? (
          <>
            <p style={{ fontSize: "10px", textAlign: "left", margin: 0 }}>
              <span className="mb-2"> Giracrédito valido até a data: {expirationDate}</span>
            </p>
            <br />
            <strong style={{ fontSize: '10px' }}>*GIRACRÉDITO VÁLIDO APENAS NESTA UNIDADE*</strong>
          </>
        ) : null}
      </div>
      <div
        className="signature"
        style={{ textAlign: "center", fontSize: "10px", width: "100%" }}
      >
        <p>_______________________</p>
        <p>{client?.name}</p>
      </div>
      <div className="footer1" style={{ textAlign: "right", fontSize: "10px" }}>
        <p>Via Empresa</p>
      </div>
    </div>
  );
};

export default EnterpriseReceipt;
