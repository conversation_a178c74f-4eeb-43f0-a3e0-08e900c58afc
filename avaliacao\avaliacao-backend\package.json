{"name": "avaliacao-backend", "version": "1.0.0", "description": "", "main": "src/index.js", "prettier": "dfcom-prettier-config", "engines": {"node": ">18.14.*"}, "scripts": {"build": "babel src --out-dir dist", "client": "cd ../frontend && npm start", "dev": "nodemon .", "sentry:sourcemaps": "sentry-cli sourcemaps inject --org dfcom-0z --project avaliacao-bk ./dist && sentry-cli sourcemaps upload --org dfcom-0z --project avaliacao-bk ./dist", "start": "pm2 start ./dist/index.js --name avaliacao --watch -i 0", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "DF Com Softwares", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.378.0", "@aws-sdk/client-secrets-manager": "^3.658.1", "@sentry/aws-serverless": "^8.32.0", "@sentry/cli": "^2.36.5", "@sentry/node": "^8.9.2", "@sentry/profiling-node": "^8.32.0", "aws-sdk": "^2.992.0", "axios": "^0.19.2", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "cors": "^2.8.5", "crypto-js": "^4.1.1", "dotenv": "^9.0.0", "exceljs": "^4.3.0", "express": "^4.17.1", "fs-extra": "^11.1.0", "jsonwebtoken": "^8.5.1", "moment": "^2.29.4", "moment-timezone": "^0.5.40", "mongoose": "^8.6.2", "mongoose-auto-increment": "^5.0.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "newrelic": "^9.7.4", "node-geocoder": "^4.2.0", "nodemon": "^2.0.7", "qs": "^6.10.1", "randomstring": "^1.1.5", "saslprep": "^1.0.3", "serverless-http": "^3.2.0", "sst": "^3.2.70", "uuid": "^10.0.0"}, "devDependencies": {"@babel/cli": "^7.19.3", "@babel/core": "^7.19.3", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-transform-class-properties": "^7.22.0", "@babel/preset-env": "^7.19.3", "@types/aws-lambda": "8.10.141", "@types/dotenv": "^8.2.0", "@types/express": "^4.17.21", "@types/ip": "^1.1.2", "@types/node": "^20.14.10", "babel-preset-minify": "^0.5.2", "dfcom-prettier-config": "0.0.1", "eslint": "^8.8.0", "typescript": "^5.5.3"}}