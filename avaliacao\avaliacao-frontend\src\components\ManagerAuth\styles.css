.manager-container {
  background-color: #00000050;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 5;
}

.manager-content {
  width: 600px;
  height: 300px;
  background-color: #FFF;
  border-radius: 5px;
  box-shadow: 0px 0px 10px -5px;
  padding: 1rem 3rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  justify-content: center;
  align-items: center;
}

.manager-input {
  padding: .5rem;
  border-radius: 5px;
  outline: none;
  border: 1px solid #DDD;
}

.buttons-container button {
  width: 100%;
  border: none;
  padding: .5rem;
  transition: 0.3s;
  font-size: 18px;
  cursor: pointer;
  border-radius: 5px;
}

.buttons-container button:hover {
  opacity: 0.8;
}

.primary {
  background: peru;
  color: #FFF;
}

.secondary {
  background: #555;
  color: #FFF;
}
