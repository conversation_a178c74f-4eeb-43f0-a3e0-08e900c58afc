name: Deploy to AWS Lambda - Homolog

on:
  pull_request:
    types:
      - closed
    branches:
      - homolog
  push:
    branches:
      - homolog

jobs:
  cancel_previous:
    runs-on: ubuntu-latest
    steps:
      - name: Cancel Previous Runs
        uses: styfle/cancel-workflow-action@0.11.0
        with:
          access_token: ${{ secrets.GITHUB_TOKEN }}

  deploy:
    runs-on: ubuntu-latest
    needs: cancel_previous

    steps:
      - name: Check out the repository
        uses: actions/checkout@v3

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install dependencies
        run: npm install --legacy-peer-deps

      - name: Build project
        run: npm run build

      - name: SST Deploy
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: sa-east-1
          SST_STAGE: homolog
          AWS_SECRET_NAME: test/avaliacao-homolog
          AWS_BUCKET: crescieperdi.dfcom
          NODE_ENV: homolog
        run: npx sst deploy --stage=$SST_STAGE

      - name: Send success notification to Discord
        if: success()
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
        uses: Ilshidur/action-discord@master
        with:
          args: |
            Deploy Lambda efetuado com sucesso (avaliacao-backend / homolog)
            PR: ${{ github.event.pull_request.title || 'Direct Push - No PR' }}
            PR_AUTHOR: ${{ github.event.pull_request.user.login }}

  error:
    runs-on: ubuntu-latest
    needs: deploy
    if: failure()
    steps:
      - name: Notify Discord on failure
        env:
          DISCORD_WEBHOOK: ${{ secrets.DISCORD_WEBHOOK }}
        uses: Ilshidur/action-discord@master
        with:
          args: |
            Deploy Lambda falhou (avaliacao-backend / homolog)
            PR: ${{ github.event.pull_request.title || 'Direct Push - No PR' }}
            PR_AUTHOR: ${{ github.event.pull_request.user.login }}
