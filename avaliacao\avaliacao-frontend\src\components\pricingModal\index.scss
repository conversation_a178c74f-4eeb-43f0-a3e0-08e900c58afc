.title-modal {
  font-size: 32px;
  margin-bottom: 5px;
}

.swal2-icon {
  font-size: 18px !important;
}

.div-with-scroll.pricing {
  display: block;
  max-height: calc(100% - 220px);
  overflow-y: auto;
  overscroll-behavior-y: contain;

  .div-parent-with-scroll {
    height: auto;
  }
}

.div-parent-with-scroll table {
  position: relative;
  border-collapse: collapse;
}

.div-parent-with-scroll table thead th {
  background: white;
  position: sticky;
  top: -12px;
  z-index: 19;
  padding: 5px;
}

.div-parent-with-scroll table tbody {
  position: relative;
}

.div-parent-with-scroll table tbody td {
  padding: 0px 5px;
}

.div-parent-with-scroll table tbody td,
.div-parent-with-scroll table thead th {
  font-size: 12px;
}

#btn-higienization {
  background-color: #237bff;
  color: #FFF;
  gap: 15px;
  font-size: 12px;
}

table>thead>tr>th {
  text-align: center;
}

.buttons-value.pricing,
.button-print.pricing {
  margin-top: 15px;
  gap: 15px;
}

.buttons-value {
  display: flex;
  width: 100%;
  justify-content: center;
}

.modal-footer.pricing {
  position: absolute;
  bottom: 30px;
  width: 100%;
  max-width: calc(100% - 30px);
}

.buttons-value.pricing button,
.button-print.pricing button {
  font-size: 12px;
}

.concatenate-button {
  padding: .5rem;
  border-radius: 5px;

  background-color: peru;
  color: #FFF;
  cursor: pointer;
  border: none;
  transition: 0.3s;
  margin-top: 10px;
}

.concatenate-button:hover {
  opacity: 0.8;
}

.concatenate-button:disabled {
  background: #999;
  cursor: not-allowed;
}

.exlude-items-concat {
  background-color: red;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 5px;
}

@media screen and (min-width: 767px) {
  .title-modal {
    font-size: 20px !important;
  }
}

@media screen and (min-width: 1100px) {
  .title-modal {
    font-size: 28px !important;
  }

  .div-with-scroll.pricing {
    display: block;
    // max-height: calc(100% - 180px);
  }

  .div-parent-with-scroll table tbody td,
  .div-parent-with-scroll table thead th {
    font-size: 13px;
  }
}

@media screen and (min-width: 1400px) {
  .size-modal {
    height: 50vh;
  }

  .div-with-scroll.pricing {
    display: block;
    // max-height: calc(100% - 190px);
  }

  .div-parent-with-scroll table tbody td,
  .div-parent-with-scroll table thead th {
    font-size: 14px;
  }

  .buttons-value.pricing button,
  .button-print.pricing button {
    font-size: 14px;
  }

  .modal-print-items {
    padding-top: 10px;
  }

  .input-percentage {
    text-align: center;
    width: 70px;
  }
}