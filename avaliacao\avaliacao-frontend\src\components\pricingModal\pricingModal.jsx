import React from "react";
import Modal from "react-modal";
import ApiService from "../../services/ApiService";
import Swal from "sweetalert2";

import * as FontAwesomeIcon from "react-icons/fa";
import * as BsIcons from "react-icons/bs";
import { toast } from "react-toastify";
import "./index.scss";
import { generateBarCodeItem } from "../../utils/avaliationUtils";
import { PrintUrl } from "../../services/PrintRoute";
import { calculateDefValueUtils } from "../../utils/calculateDefValueUtils";
import { formatDefValueUtils } from "../../utils/formatDefValueUtils";
import { checksIfValuesAreIntegers } from "../../utils/checksIfValuesAreIntegers";
import { AuthContext } from "../../context/auth";
import { verifyPlatform } from "../../utils/platform";
import GroupItemsModal from "./GroupItemsModal";
import { Tooltip as ReactTooltip } from "react-tooltip";
import { removeDuplicatesFromArray } from "../../utils/removeDuplicatesFromArray";
import { invalidNumbers } from "../../utils/invalidNumbersUtils";
import HigienizationModal from "./HigienizationModal";
import { handleWithRoundingNumbersGreaterThan100 } from "../../utils/handleWithRoundingNumbersGreaterThan100Utils";
import { DexieDB } from "../../config/Dexie";
import { calcSellValuePrecification } from "../../utils/productPricing";
import { calcGiracreditValue } from "../../utils/calcGiracreditValue";

const modalStyles = {
  content: {
    width: "900px",
    top: "50%",
    left: "50%",
    right: "50%",
    bottom: "auto",
    marginRight: "-50%",
    transform: "translate(-50%, -50%)",
    borderRadius: "10px",
    height: "50em",
    maxHeight: "90%",
    maxWidth: "80%",
    zIndex: 1000,
  },
  overlay: {
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    zIndex: 999,
  },
};

class PricingModal extends React.Component {
  static contextType = AuthContext;

  constructor(props) {
    super(props);
    this.state = {
      show: false,
      printProducts: [],
      defValueProduct: "",
      isAllPrecified: false,
      defaultValuesOfEvaluationProducts: [],
      productItems: [],
      concatItemsModalIsOpen: false,
      allMarkedToPrint: false,
    };
  }

  async componentDidUpdate(prevProps) {
    if (prevProps.showModal !== this.props.showModal && this.props.showModal) {
      this.setState({ show: this.props.showModal });
      this.buildProductsOfEvaluation();
    }
  }

  handleTogglePrintForAllItems() {
    const { printProducts } = this.state;

    let productsItems = [];
    for (const product of printProducts) {
      productsItems.push({
        ...product,
        precificado:
          this.state.allMarkedToPrint ||
            product?.higienization ||
            product?.discarded
            ? false
            : true,
        percentage: 0,
      });
    }

    this.setState({
      allMarkedToPrint: !this.state.allMarkedToPrint,
      printProducts: productsItems,
      defaultValuesOfEvaluationProducts: productsItems,
    });
  }

  handleExcludeItemsConcat(index) {
    const product = this.state.printProducts[index];
    let newProductsArray = this.state.printProducts.filter(
      (product, idx) => idx !== index
    );

    const productQtd = product?.qtd;

    for (let i = 0; i < productQtd; i++) {
      newProductsArray.push({
        ...product,
        qtd: 1,
      });
    }

    this.setState({
      printProducts: newProductsArray.sort((a, b) =>
        String(a.descricao).localeCompare(b.descricao)
      ),
      defaultValuesOfEvaluationProducts: newProductsArray.sort((a, b) =>
        String(a.descricao).localeCompare(b.descricao)
      ),
    });
  }

  handleConcatItems(selectedProduct, qtd) {
    let productsArray = [...this.state.printProducts];

    let newProductsArray = [];

    const selectedProductsArray = productsArray.filter(
      (product) =>
        String(product.description).toLowerCase() === selectedProduct &&
        product?.qtd === 1
    );
    const unselectedProductsArray = productsArray.filter(
      (product) =>
        String(product.description).toLowerCase() !== selectedProduct ||
        product?.qtd !== 1
    );

    if (selectedProductsArray?.length < qtd) {
      return toast.error(
        "Quantidade a agrupar não pode ser maior que a quantidade de produtos."
      );
    }

    const qtdRest = selectedProductsArray?.length - qtd;

    newProductsArray.push({
      ...selectedProductsArray[0],
      valorMin: Number(selectedProductsArray[0]?.valorMin),
      valorMax: Number(selectedProductsArray[0]?.valorMax),
      qtd,
      percentage: 0,
      isEnabledPercentage: true,
      giraCreditDiscount: 0,
    });

    for (let i = 0; i < qtdRest; i++) {
      newProductsArray.push({
        ...selectedProductsArray[0],
        valorMin: Number(selectedProductsArray[0]?.valorMin),
        valorMax: Number(selectedProductsArray[0]?.valorMax),
        qtd: 1,
        percentage: 0,
        isEnabledPercentage: true,
        giraCreditDiscount: 0,
      });
    }

    newProductsArray.push(...unselectedProductsArray);

    this.setState({
      printProducts: newProductsArray.sort((a, b) =>
        String(a.descricao).localeCompare(b.descricao)
      ),
      defaultValuesOfEvaluationProducts: newProductsArray.sort((a, b) =>
        String(a.descricao).localeCompare(b.descricao)
      ),
      concatItemsModalIsOpen: false,
    });
  }

  async buildProductsOfEvaluation() {
    const { data } = this.props;

    let productsItems = [];
    for (let product of data.products) {
      if (!Number(product?.qtd)) continue;

      if (this.props.type === "default") {
        if (product.qtd > 1) {
          let isSearch = false;

          if (typeof product?.pesquisa === 'boolean') {
            isSearch = product?.pesquisa
          } else {
            isSearch = product?.pesquisa === 'sim' ? true : false;
          }

          let maxValue = calcSellValuePrecification({ ...product, ePesquisa: isSearch })

          while (invalidNumbers.includes(maxValue)) {
            maxValue += 1;
          }

          for (let i = 0; i < product.qtd; i++) {
            productsItems.push({
              ...product,
              valorMin: Number(product.valorMin),
              valorMax: maxValue,
              qtd: 1,
              percentage: 0,
              isEnabledPercentage: false,
              giraCreditDiscount: 0,
              valorVenda: Number(product.vlrVenda),
              valorGiracredito: Number(product.vlrGiracredit),
              locacao: false,
              precificado: false
            });
          }
        } else {
          let isSearch = false;

          if (typeof product?.pesquisa === 'boolean') {
            isSearch = product?.pesquisa
          } else {
            isSearch = product?.pesquisa === 'sim' ? true : false;
          }

          let maxValue = calcSellValuePrecification({ ...product, ePesquisa: isSearch })

          while (invalidNumbers.includes(maxValue)) {
            maxValue += 1;
          }

          productsItems.push({
            ...product,
            valorMin: Number(product.valorMin),
            valorMax: maxValue,
            percentage: 0,
            qtd: 1,
            isEnabledPercentage: false,
            giraCreditDiscount: 0,
            precificado: false,
            valorVenda: Number(product.vlrVenda),
            valorGiracredito: Number(product.vlrGiracredit),
            locacao: false,
          });
        }
      } else {
        productsItems.push({
          ...product,
          valorMin: Number(product.valorMin),
          valorMax: Number(product.valorMax),
          qtd: product?.qtd,
          percentage: 0,
          isEnabledPercentage: false,
          giraCreditDiscount: 0,
          valorVenda: Number(product?.valorVenda || product.vlrVenda),
          valorGiracredito: Number(product.vlrGiracredit),
          locacao: false,
        });
      }
    }

    this.setState({
      defaultValuesOfEvaluationProducts: productsItems,
      printProducts: productsItems,
    });
  }

  printItems = async () => {
    const printProducts = this.state.printProducts.filter(
      (product) => product.precificado || product?.locacao
    );

    let printProductsArray = [];
    let hasEqualValues = false;

    for (const product of printProducts) {
      if (!['NOVO', 'NOVA', 'NOVOS', 'NOVAS'].some((value) => String(product?.description).includes(value) || String(product?.descricao).includes(value)) && Number(product?.defValueProduct) !== 0 && Number(product?.defValueProduct) > 10) {
        if (Number(product?.defValueProduct) === Number(product?.giraCreditDiscount) && Number(product?.defValueProduct) !== 3) {
          hasEqualValues = true;
        }
      };

      if (product?.qtd > 1) {
        for (let i = 0; i < product?.qtd; i++) {
          printProductsArray.push({
            ...product,
            qtd: 1,
          });
        }
      } else {
        printProductsArray.push(product);
      }
    }

    if (hasEqualValues) {
      return toast.error('O valor de giracrédito não pode ser o mesmo que o valor de venda para itens seminovos')
    }

    if (this.props.type === 'default') {
      // if (printProductsArray?.some(product => !product?.giraCreditDiscount === undefined || (Number(product?.defValueProduct) > 0 && product?.giraCreditDiscount === 0))) {
      //   return toast.error('Todos os produtos precisam ter o valor giracrédito informado.')
      // };

      if (printProductsArray?.some(product => Number(product?.giraCreditDiscount) > Number(product?.defValueProduct))) {
        return toast.error('O valor de giracrédito dos produtos não podem ser maiores que os valores definidos.')
      };
    };

    if (printProductsArray.length === 0) {
      toast.error("Nenhum Produto selecionado para impressão");
      return;
    }

    if (
      this.props.type === "default" &&
      printProductsArray.some((product) => !product.defValueProduct)
    ) {
      toast.error("Por favor, preencha todos os valores");
      return;
    }

    const { user } = this.context;
    const operationSystem = verifyPlatform();
    const [printPayload, printPayloadWithDiscount, printLocationPayload] =
      this.printItensPayload(printProductsArray);

    if (
      printPayload?.Itens?.some((product) => product?.codbarras?.length < 25)
    ) {
      toast.error("Falha ao imprimir produtos. Faça logout e tente novamente!");
      return;
    }

    if (printLocationPayload?.Itens?.length) {
      try {
        await ApiService.PrintLocationProducts(printLocationPayload)
      } catch (error) {
        toast.error('Ocorreu um erro ao imprimir os itens selecionados para locação.')
        console.log(error);
      }
    }

    let withoutValueItens = printPayload.Itens.filter(
      (product) => product?.valor === "0.00"
    );

    if (withoutValueItens?.length) {
      if (operationSystem !== "Windows") {
        const printerOrigins = await DexieDB.printerOrigins.toArray();

        const origin = printerOrigins
          .filter((item) => item.description === "ETIQUETA_GRATIS_V2")
          .shift();

        const dataPrint = {
          ownerPrinterCnpj: user?.enterprise?.cnpj,
          typeId: origin?._id,
          data: withoutValueItens,
        };

        await ApiService.CreatePipeline(dataPrint);
      } else {
        try {
          await PrintUrl.PrintFreeV2({
            ...printPayload,
            Itens: withoutValueItens,
          });
        } catch (error) {
          console.log(error);
        }
      }
    }

    if (
      withoutValueItens?.length !== printPayload.Itens.length ||
      withoutValueItens?.length !== printPayloadWithDiscount.Itens.length
    ) {
      const payloadToPrint = {
        ...printPayload,
        Itens: printPayload.Itens.filter(
          (product) => product?.valor !== "0.00"
        ).map((product) => ({
          ...product,
          verificationCode: this.props.evaluation?._id,
        })),
      };

      const giracreditPayloadToPrint = {
        ...printPayloadWithDiscount,
        Itens: printPayloadWithDiscount.Itens.filter(
          (product) => product?.valor !== "0.00"
        ),
      };

      if (
        payloadToPrint?.Itens?.length
      ) {
        if (operationSystem !== "Windows") {
          const printerOrigins = await DexieDB.printerOrigins.toArray();

          const origin = printerOrigins
            .filter((item) => item.description === "ETIQUETA_ENTRADA_V2")
            .shift();

          if (payloadToPrint.Itens.length) {
            const dataPrint = {
              ownerPrinterCnpj: user?.enterprise?.cnpj,
              typeId: origin._id,
              data: payloadToPrint,
            };

            if (payloadToPrint.Itens.length) {
              const dataPrint = {
                ownerPrinterCnpj: user?.enterprise?.cnpj,
                typeId: origin._id,
                data: payloadToPrint,
              };

              try {
                await ApiService.CreatePipeline(dataPrint);
              } catch (err) {
                console.log(err);
              }
            }
          }
        } else {
          try {
            await PrintUrl.PrintItemCupomV2(payloadToPrint);
          } catch (err) {
            console.log(err);
          }
        }
      }

      if (giracreditPayloadToPrint?.Itens?.length) {
        if (operationSystem !== "Windows") {
          const printerOrigins = await DexieDB.printerOrigins.toArray();

          const originGiracredito = printerOrigins
            .filter((item) => item.description === "ETIQUETA_GIRA_CREDITO_V2")
            .shift();

          const printItens = giracreditPayloadToPrint.Itens.map((product) => ({
            descricao: product.descricao,
            qtd: product.qtd,
            codbarras: product.codbarras,
            valor: product.valor.replace(".", "").replace(",", "."),
            valor_giracredito: product.giraCreditDiscount
              .replace(".", "")
              .replace(",", "."),
            verificationCode: this.props.evaluation?._id
          }));

          const dataPayload = {
            ownerPrinterCnpj: user?.enterprise?.cnpj,
            typeId: originGiracredito._id,
            codunidade: giracreditPayloadToPrint.codunidade,
            data: {
              codunidade: giracreditPayloadToPrint.codunidade,
              Itens: printItens,
            },
          };

          try {
            await ApiService.CreatePipeline(dataPayload);
          } catch (err) {
            console.log(err);
          }
        } else {
          const printItens = giracreditPayloadToPrint.Itens.map((product) => ({
            descricao: product.descricao,
            qtd: product.qtd,
            codbarras: product.codbarras,
            valor: product.valor.replace(".", "").replace(",", "."),
            valor_giracredito: product.giraCreditDiscount
              .replace(".", "")
              .replace(",", "."),
            verificationCode: this.props.evaluation?._id
          }));

          await PrintUrl.PrintGiraCreditoV2({
            codunidade: giracreditPayloadToPrint.codunidade,
            Itens: printItens,
          });
        }
      };
    }

    toast.success(`Impressão realizada com sucesso`);
    this.props.onAfterPrint?.();
  };

  async handleSendProductToHigienization(product, qtd) {
    let productsArray = [...this.state.printProducts];

    let newProductsArray = [];

    const selectedProduct = productsArray.find(
      (item) =>
        String(item.description).toLowerCase() === String(product).trim() &&
        !item?.higienization
    );
    const unselectedProducts = productsArray.filter(
      (item) =>
        String(item.description).toLowerCase() !== String(product).trim()
    );

    if (Number(selectedProduct?.qtd) < qtd) {
      return toast.error(
        "Quantidade a higienizar não pode ser maior que a quantidade de produtos disponiveis."
      );
    }

    const qtdRest = Number(selectedProduct?.qtd) - qtd;

    const verifyIfExistProductInHigienization = productsArray.find(
      (item) =>
        String(item.description).toLowerCase() === String(product).trim() &&
        item?.higienization
    );

    if (verifyIfExistProductInHigienization) {
      newProductsArray.push({
        ...selectedProduct,
        valorMin: Number(selectedProduct?.valorMin || 0),
        valorMax: Number(selectedProduct?.valorMax || 0),
        qtd: (Number(verifyIfExistProductInHigienization?.qtd) || 0) + qtd,
        percentage: 0,
        isEnabledPercentage: false,
        giraCreditDiscount: 0,
        higienization: true,
        precificado: false,
      });
    } else {
      newProductsArray.push({
        ...selectedProduct,
        valorMin: Number(selectedProduct?.valorMin || 0),
        valorMax: Number(selectedProduct?.valorMax || 0),
        qtd,
        percentage: 0,
        isEnabledPercentage: false,
        giraCreditDiscount: 0,
        higienization: true,
        precificado: false,
      });
    }

    if (qtdRest > 0) {
      newProductsArray.push({
        ...selectedProduct,
        valorMin: Number(selectedProduct?.valorMin || 0),
        valorMax: Number(selectedProduct?.valorMax || 0),
        qtd: qtdRest,
        percentage: 0,
        isEnabledPercentage: false,
        giraCreditDiscount: 0,
        higienization: false,
        precificado: false,
      });
    }

    newProductsArray.push(...unselectedProducts);

    const req = {
      evaluationId: this.props?.evaluation?._id,
      clientCpf: this.props?.evaluation?.cpf,
      enterpriseCnpj: this.props?.evaluation?.cnpj,
      items: [{ ...selectedProduct, qtd }],
    };

    await ApiService.createHigienization(req);

    this.setState({
      printProducts: newProductsArray.sort((a, b) =>
        String(a.descricao).localeCompare(b.descricao)
      ),
      defaultValuesOfEvaluationProducts: newProductsArray.sort((a, b) =>
        String(a.descricao).localeCompare(b.descricao)
      ),
      higienizationModalIsOpen: false,
    });

    toast.success("Produto adicionado para higienização.");
  }

  buildUnityCode(product) {
    const { user } = this.context;
    let finalValueProcuct = "";
    let codUnd = user?.enterprise?.codUnd;
    let unityCode = String(codUnd).padStart(4, '0');

    const valorDef = String(
      Math.floor(
        this.props.type === "default"
          ? product.defValueProduct
          : product?.vlrVenda
      )
    );

    if (valorDef.length === 1) {
      finalValueProcuct = `000${valorDef.replace(".", "")}00`;
    } else if (valorDef.length === 2) {
      finalValueProcuct = `00${valorDef.replace(".", "")}00`;
    } else if (valorDef.length === 3) {
      finalValueProcuct = `0${valorDef.replace(".", "")}00`;
    } else {
      finalValueProcuct = `${valorDef.replace(".", "")}00`;
    }

    // if (String(codUnd).length === 1) {
    //   unityCode = `000${codUnd}`;
    // } else if (String(codUnd).length === 2) {
    //   unityCode = `00${codUnd}`;
    // } else {
    //   unityCode = `0${codUnd}`;
    // }

    let giraCreditDiscountCode = "";
    const giraCreditDiscount = String(
      Number(
        this.props.type === "default"
          ? product.giraCreditDiscount
          : product.vlrGiracredit
      ).toFixed(0)
    );

    if (giraCreditDiscount.length === 1) {
      giraCreditDiscountCode = `000${giraCreditDiscount.replace(".", "")}00`;
    } else if (giraCreditDiscount.length === 2) {
      giraCreditDiscountCode = `00${giraCreditDiscount.replace(".", "")}00`;
    } else if (giraCreditDiscount.length === 3) {
      giraCreditDiscountCode = `0${giraCreditDiscount.replace(".", "")}00`;
    } else {
      giraCreditDiscountCode = `${giraCreditDiscount.replace(".", "")}00`;
    }

    let giraPercentageCode = "";
    const giraPercentage = String(product.percentage);
    if (giraPercentage.length === 1) {
      giraPercentageCode = `00${giraPercentage.replace(".", "")}`;
    } else if (giraPercentage.length === 2) {
      giraPercentageCode = `0${giraPercentage.replace(".", "")}`;
    } else {
      giraPercentageCode = `${giraPercentage.replace(".", "")}`;
    }

    const searchableDesc = product.descPesquisa
      ? `/DP-${product.descPesquisa}`
      : "";
    const productCode = generateBarCodeItem(product);

    return `${unityCode}${productCode}${finalValueProcuct}${giraCreditDiscountCode}${giraPercentageCode}${searchableDesc}`;
  }

  printItensPayload = (itemsToPrint = this.state.printProducts) => {
    const { user } = this.context;

    const items = itemsToPrint
      .filter((product) => product.precificado)
      .filter((product) => !product?.locacao)
      .filter((product) => {
        if (this.props.type === 'default') {
          if (Number(product.giraCreditDiscount) === Number(product?.defValueProduct)) {
            return product;
          }
        } else {
          if (Number(product.vlrGiracredit) === Number(product?.vlrVenda) || Number(product?.vlrGiracredit) === 0) {
            return product;
          }
        }

        return null
      })
      .filter((product) => !!Number(product.qtd))
      .filter((product) =>
        this.props.type === "default"
          ? !isNaN(Number(product.defValueProduct))
          : !isNaN(Number(product.vlrVenda))
      )
      .map((product) => {
        product = {
          ...product,
          valor: String(
            Number(
              this.props.type === "default"
                ? product.defValueProduct || 0
                : product?.vlrVenda || 0
            ).toFixed(2)
          ).replace(",", "."),
          giraCreditDiscount:
            this.props.type === "default"
              ? String(product?.giraCreditDiscount)
              : String(product?.vlrGiracredit),
        };

        return {
          _id: product._id,
          codbarras: this.buildUnityCode(product),
          descricao:
            product.descricao ||
            product?.description +
            `${product?.descPesquisa ? ` - ${product?.descPesquisa}` : ""}`,
          codunidade: user?.enterprise?.codUnd,
          qtd: product.qtd,
          valor: product.valor,
          percentage: Number(product.percentage),
          giraCreditDiscount: Number(product.giraCreditDiscount),
          ...(product?.descPesquisa && {
            descricao: `${product?.descricao || product?.description} - ${product?.descPesquisa
              }`,
          }),
        };
      });

    const itemsWithDiscount = itemsToPrint
      .filter((product) => product.precificado)
      .filter((product) => !product?.locacao)
      .filter((product) => !!Number(product.qtd))
      .filter((product) => this.props.type === 'default'
        ? Number(product?.giraCreditDiscount) !== Number(product?.defValueProduct) || (Number(product?.defValueProduct) > 10 && Number(product?.giraCreditDiscount) === 0)
        : Number(product?.vlrVenda) !== Number(product?.vlrGiracredit))
      .filter((product) =>
        this.props.type === "default"
          ? !isNaN(Number(product.defValueProduct))
          : !isNaN(Number(product.vlrVenda))
      )
      .map((product) => ({
        _id: product._id,
        codbarras: this.buildUnityCode(product),
        descricao: product.description || product?.descricao,
        ...(product?.descPesquisa && {
          descricao: `${product?.descricao || product?.description} - ${product?.descPesquisa
            }`,
        }),
        valor: String(
          Number(
            this.props.type === "default"
              ? product.defValueProduct
              : product.vlrVenda
          ).toFixed(2)
        ).replace(".", ","),
        codunidade: user?.enterprise?.codUnd,
        qtd: product.qtd,
        percentage: Number(product.percentage),
        giraCreditDiscount: Number(
          this.props.type === "default"
            ? product.giraCreditDiscount
            : product?.vlrGiracredit
        )
          .toFixed(2)
          .toString()
          .replace(".", ","),
      }));

    const itemsForLocation = itemsToPrint
      .filter((product) => !product.precificado)
      .filter((product) => product?.locacao)
      .filter((product) => !!Number(product.qtd))
      .filter((product) =>
        this.props.type === "default"
          ? !isNaN(Number(product.defValueProduct))
          : !isNaN(Number(product.vlrVenda))
      )
      .map((product) => ({
        _id: product._id,
        codbarras: this.buildUnityCode(product),
        descricao: product.description || product?.descricao,
        ...(product?.descPesquisa && {
          descricao: `${product?.descricao || product?.description} - ${product?.descPesquisa
            }`,
        }),
        valor: String(
          Number(
            this.props.type === "default"
              ? product.defValueProduct
              : product.vlrVenda
          ).toFixed(2)
        ).replace(".", ","),
        codunidade: user?.enterprise?.codUnd,
        qtd: product.qtd,
        percentage: Number(product.percentage),
        giraCreditDiscount: Number(
          this.props.type === "default"
            ? product.giraCreditDiscount
            : product?.vlrGiracredit
        )
          .toFixed(2)
          .toString()
          .replace(".", ","),
        locProductId: product?.locProductId
      }));

    return [
      {
        codunidade: user?.enterprise?.codUnd,
        Itens: items,
      },
      {
        codunidade: user?.enterprise?.codUnd,
        Itens: itemsWithDiscount,
      },
      {
        codunidade: user?.enterprise?.codUnd,
        Itens: itemsForLocation,
      }
    ];
  };

  handleDefValue(isMax) {
    let productsItems = [];
    for (let product of this.state.defaultValuesOfEvaluationProducts) {
      if (product.qtd > 1) {
        for (let i = 0; i < product.qtd; i++) {
          let productValue = isMax ? product.valorMax : product.valorMin;

          while (invalidNumbers.includes(productValue)) {
            productValue += 1;
          }

          productsItems.push({
            ...product,
            qtd: 1,
            valorMax: product.valorMax,
            valorMin: product.valorMin,
            defValueProduct: Number(productValue),
            giraCreditDiscount: 0,
            percentage: 0,
            isEnabledPercentage: true,
          });
        }
      } else {
        let productValue = isMax ? product.valorMax : product.valorMin;

        while (invalidNumbers.includes(productValue)) {
          productValue += 1;
        }

        productsItems.push({
          ...product,
          valorMax: product.valorMax,
          valorMin: product.valorMin,
          defValueProduct: Number(productValue),
          giraCreditDiscount: 0,
          percentage: 0,
          isEnabledPercentage: true,
        });
      }
    }

    const newProducts = productsItems.map((product) => ({
      ...product,
      precificado: !product?.higienization ? true : false,
      isEnabledPercentage: !product?.higienization ? true : false,
    }));

    this.setState({
      ...this.state,
      defaultValuesOfEvaluationProducts: newProducts,
      printProducts: newProducts,
    });
  }

  setIsEditProduct = async (index) => {
    this.setState({ isEditProduct: !this.state.isEditProduct, defValueProduct: 0 });
    document.getElementById(`td-${index}`).style.display = "none";
    document.getElementById(`edit-product${index}`).style.display = "block";
    document.getElementById(`edit-product${index}`).focus();
    document.getElementById(`pen-${index}`).style.display = "none";
    document.getElementById(`check-${index}`).style.display = "block";

    this.state.printProducts.forEach((_, idx) => {
      if (idx !== index) {
        document.getElementById(`td-${idx}`).style.display = "";
        document.getElementById(`edit-product${idx}`).style.display = "none";
        document.getElementById(`pen-${idx}`).style.display = "";
        document.getElementById(`check-${idx}`).style.display = "none";
      }
    })
  };

  setNewDefValueProduct = async (product, index, event) => {
    const { user } = this.context;
    const defValueProduct = formatDefValueUtils(this.state.defValueProduct);
    const printProducts = this.state.printProducts;

    let valueToSet = defValueProduct;
    let unityCode = "";
    let codUnd = user?.enterprise?.codUnd;

    let finalValueProcuct = "";
    let finalDiscountProduct = "";

    valueToSet = Number(defValueProduct);

    if (!checksIfValuesAreIntegers(valueToSet)) return;

    this.setState({
      ...this.state,
      defValueProduct: defValueProduct,
    });

    printProducts[index].defValueProduct = String(`${valueToSet}`);
    this.state.defaultValuesOfEvaluationProducts[index].defValueProduct =
      Number(valueToSet).toFixed(2);
    printProducts[index].isEnabledPercentage = true;

    if (String(valueToSet).length === 1) {
      finalValueProcuct = `00${valueToSet}00`;
    } else if (String(valueToSet).length === 2) {
      finalValueProcuct = `0${valueToSet}00`;
    } else if (String(valueToSet).length === 3) {
      finalValueProcuct = `${valueToSet}0`;
    } else {
      finalValueProcuct = `${valueToSet}`;
    }

    if (String(codUnd).length === 1) {
      unityCode = `000${codUnd}`;
    } else if (String(codUnd).length === 2) {
      unityCode = `00${codUnd}`;
    } else {
      unityCode = `0${codUnd}`;
    }

    this.state.defaultValuesOfEvaluationProducts.forEach((itemFromList) => {
      if (
        itemFromList.descricao === product.description &&
        itemFromList.ativo
      ) {
        const productValue = `0${finalValueProcuct}`;
        const productDiscount = `0${finalDiscountProduct}`;
        const productCode = generateBarCodeItem(itemFromList);
        const finalBarCodeWithDiscount = `${unityCode}${productCode}${productValue}${productDiscount}`;
        product.codBarras = finalBarCodeWithDiscount;
      }
    });

    this.state.defaultValuesOfEvaluationProducts[index].precificado = true;

    const value = this.handleCalculateDefValueWithPercentage(this.state.defaultValuesOfEvaluationProducts[index], index, event)
    let giracreditValue = calcGiracreditValue(Number(value));

    if (this.props.evaluation && Number(value) !== Number(this.state.defaultValuesOfEvaluationProducts[index].valorMax)) {
      const maxValue = Number(this.state.defaultValuesOfEvaluationProducts[index].valorMax);
      const alertData = {
        type: 'precificacao',
        cpf: this.props?.evaluation?.cpf,
        message: '',
        data: {
          evaluationId: this.props.evaluation?._id,
          evaluationDate: this.props.evaluation?.dataFinal,
          clientCpf: this.props.evaluation?.cpf,
          clientName: this.props.evaluation?.cliente,
          product: this.state.defaultValuesOfEvaluationProducts[index]?.descricao,
          qtd: this.state.defaultValuesOfEvaluationProducts[index].qtd,
          normalPrice: this.state.defaultValuesOfEvaluationProducts[index].valorMax,
          definedValue: Number(value),
          difference: Number(value) - Number(maxValue),
          porcentageDifference: ((Number(value) - Number(maxValue)) / maxValue) * 100,
        },
      }
      await ApiService.SendAlert(alertData);
    }

    this.state.defaultValuesOfEvaluationProducts[index].giraCreditDiscount = giracreditValue < 0 ? 0 : giracreditValue;

    this.setState({
      printProducts: printProducts,
      defaultValuesOfEvaluationProducts:
        this.state.defaultValuesOfEvaluationProducts,
      defValueProduct: "",
    });

    document.getElementById(`td-${index}`).style.display = "";
    document.getElementById(`edit-product${index}`).style.display = "none";
    document.getElementById(`pen-${index}`).style.display = "";
    document.getElementById(`check-${index}`).style.display = "none";
  };

  setProductPrint(index) {
    const { defaultValuesOfEvaluationProducts, printProducts } = this.state;
    defaultValuesOfEvaluationProducts[index].precificado =
      !defaultValuesOfEvaluationProducts[index].precificado;

    // printProducts[index].precificado = !printProducts[index].precificado

    this.setState({ defaultValuesOfEvaluationProducts, printProducts });
  }

  setProductToLocation(index) {
    const { defaultValuesOfEvaluationProducts, printProducts } = this.state;
    defaultValuesOfEvaluationProducts[index].locacao =
      !defaultValuesOfEvaluationProducts[index].locacao;

    // printProducts[index].precificado = !printProducts[index].precificado

    this.setState({ defaultValuesOfEvaluationProducts, printProducts });
  }

  handleSetPercentage(product, index, value) {
    const { printProducts } = this.state;

    printProducts[index].percentage = value;

    this.setState({
      ...this.state,
      printProducts: printProducts,
    });
  }

  handleGiraCreditDiscount(product, index, value) {
    const { printProducts } = this.state;

    printProducts[index].giraCreditDiscount = value;

    this.setState({
      ...this.state,
      printProducts: printProducts,
    });
  }

  handleCalculateDefValueWithPercentage(product, index, event) {
    const { printProducts, defaultValuesOfEvaluationProducts } = this.state;
    const productSelected = defaultValuesOfEvaluationProducts?.[index]

    const formatPrintProducts = calculateDefValueUtils(
      productSelected,
      printProducts,
      index,
      event
    );

    this.setState({
      printProducts: formatPrintProducts,
    });

    return Number(productSelected?.defValueProduct)
  }

  render() {
    return (
      <Modal
        isOpen={this.props.showModal}
        style={modalStyles}
        ariaHideApp={false}
      >
        <div style={{ textAlign: "center" }}>
          <h1 className="title-modal">Definição de Valores</h1>
        </div>
        <hr />
        <div className="div-with-scroll pricing">
          <div className="div-parent-with-scroll">
            <table>
              <thead>
                <tr>
                  <th></th>
                  <th>Descrição</th>
                  <th>Qtd</th>
                  {this.props.type === "default" && (
                    <>
                      {/* <th>Valor Min</th> */}
                      {/* <th>Valor Max</th> */}
                      <th>Preço normal</th>
                    </>
                  )}
                  <th>Preço Def</th>
                  {this.props.type === "default" && (
                    <>
                      <th></th>
                    </>
                  )}
                  <th>
                    Preço <br />
                    {import.meta.env.VITE_GIRACREDITO}
                  </th>
                  {/* {this.props.type === "default" && (
                    <>
                      <th>{import.meta.env.VITE_GIRACREDITO} (%)</th>
                    </>
                  )} */}
                  <th />
                  {this.context.user?.permission?.locacao &&
                    <th>Locação</th>
                  }
                  <th>Selecionar</th>
                </tr>
              </thead>
              <tbody>
                {this.state.printProducts.map((product, index) => {
                  if (product.pesquisa) {
                    product.valorMax = Number(
                      formatDefValueUtils(product.valorMax)
                    );
                    product.valorMin = Number(
                      formatDefValueUtils(product.valorMin)
                    );
                  }

                  return (
                    <tr key={index}>
                      <td>
                        {this.props.type === "default" && product?.qtd > 1 ? (
                          <>
                            <button
                              id={`exclude-items-concat-${index}`}
                              className="exlude-items-concat"
                              data-tooltip-content="Desfazer agrupamento de itens"
                              onClick={() =>
                                this.handleExcludeItemsConcat(index)
                              }
                            >
                              <BsIcons.BsX size={20} color="#FFF" />
                            </button>
                            <ReactTooltip
                              anchorId={`exclude-items-concat-${index}`}
                            />
                          </>
                        ) : null}
                      </td>
                      <td>
                        {product?.descricao || product?.description}{" "}
                        {product?.descPesquisa && `- ${product?.descPesquisa}`}
                        {product?.discarded && (
                          <>
                            -
                            <span style={{ color: "#fc1818" }}>Descartado</span>
                          </>
                        )}
                        {product?.higienization && (
                          <>
                            -
                            <span style={{ color: "#237bff" }}>Higienizar</span>
                          </>
                        )}
                      </td>
                      <td>{product.qtd}</td>
                      {this.props.type === "default" && (
                        <>
                          {/* <td>
                            {Number(product.valorMin).toLocaleString("pt-br", {
                              style: "currency",
                              currency: "BRL",
                            })}
                          </td> */}
                          <td>
                            {Number(product.valorMax).toLocaleString("pt-br", {
                              style: "currency",
                              currency: "BRL",
                            })}
                          </td>
                        </>
                      )}
                      <td id={`td-${index}`}>
                        {Number(
                          this.props.type === "default"
                            ? product?.defValueProduct || 0
                            : product.vlrVenda || 0
                        ).toLocaleString("pt-br", {
                          style: "currency",
                          currency: "BRL",
                        })}
                      </td>
                      <td
                        id={`edit-product${index}`}
                        style={{ display: "none" }}
                      >
                        <input
                          type="text"
                          inputMode="numeric"
                          value={this.state.defValueProduct}
                          onChange={(e) => {
                            if (!checksIfValuesAreIntegers(e.target.value))
                              return;

                            this.setState({ defValueProduct: e.target.value });
                          }}
                        />
                      </td>
                      {this.props.type === "default" && (
                        <td>
                          <div className="item-modal-edit">
                            <FontAwesomeIcon.FaPencilAlt
                              id={`pen-${index}`}
                              color="black"
                              cursor="pointer"
                              onClick={() => this.setIsEditProduct(index)}
                            />
                            <FontAwesomeIcon.FaCheck
                              id={`check-${index}`}
                              style={{ display: "none" }}
                              color="green"
                              cursor="pointer"
                              onClick={(e) =>
                                this.setNewDefValueProduct(product, index, e)
                              }
                            />
                          </div>
                        </td>
                      )}

                      <td id={`td-${index}`} style={{}}>
                        {this.props.type === "default" ? (
                          <input
                            className="input-percentage"
                            type="text"
                            id="input-gira-value"
                            placeholder="(R$)"
                            value={product.giraCreditDiscount}
                            inputMode="numeric"
                            onChange={(e) => {
                              if (!checksIfValuesAreIntegers(e.target.value))
                                return;

                              this.handleGiraCreditDiscount(
                                product,
                                index,
                                e.target.value
                              );
                            }}
                            disabled={!product.isEnabledPercentage}
                            onBlur={(event) =>
                              this.handleCalculateDefValueWithPercentage(
                                product,
                                index,
                                event
                              )
                            }
                          />
                        ) : (
                          Intl.NumberFormat("pt-BR", {
                            style: "currency",
                            currency: "BRL",
                          }).format(product?.vlrGiracredit)
                        )}
                      </td>
                      {/* {this.props.type === "default" && (
                        <td id={`edit-product${index}`}>
                          <input
                            className="input-percentage"
                            id="input-percentage"
                            type="text"
                            placeholder="(%)"
                            inputMode="numeric"
                            value={product.percentage}
                            onChange={(e) => {
                              if (!checksIfValuesAreIntegers(e.target.value))
                                return;

                              this.handleSetPercentage(
                                product,
                                index,
                                e.target.value
                              );
                            }}
                            disabled={!product.isEnabledPercentage}
                            onBlur={(event) =>
                              this.handleCalculateDefValueWithPercentage(
                                product,
                                index,
                                event
                              )
                            }
                          />
                        </td>
                      )} */}
                      <td></td>
                      {this.context.user.permission?.locacao &&
                        <td>
                          <input
                            type="checkbox"
                            value={product.locacao}
                            checked={product.locacao}
                            onChange={() => this.setProductToLocation(index)}
                            style={{ cursor: "pointer" }}
                            disabled={!product?.allowLocation || product?.precificado}
                          />
                        </td>
                      }
                      <td>
                        <input
                          type="checkbox"
                          value={product.precificado}
                          checked={product.precificado}
                          onChange={() => this.setProductPrint(index)}
                          style={{ cursor: "pointer" }}
                          disabled={
                            product?.higienization || product?.discarded || product?.locacao
                          }
                        />
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
        <div className="modal-footer pricing">
          <div style={{ marginTop: '20px', width: '100%', background: "#FFF" }}>
            {this.props.modalType !== 'automatic' &&
              <button
                className="concatenate-button"
                onClick={() => this.setState({ concatItemsModalIsOpen: true })}
              >
                Agrupar itens
              </button>
            }
          </div>
          <div className="buttons-value pricing">
            {this.props.type === "default" ? (
              <>
                {/* <button onClick={() => this.handleDefValue(false)}>
                  Selecionar valor minimo
                </button>
                <button onClick={() => this.handleDefValue(true)}>
                  Selecionar valor máximo
                </button> */}
              </>
            ) : (
              <button onClick={() => this.handleTogglePrintForAllItems()}>
                {!this.state.allMarkedToPrint
                  ? "Selecionar todos"
                  : "Desselecionar todos"}
              </button>
            )}
          </div>
          <div className="button-print pricing">
            {this.props?.allowHigienization && (
              <button
                onClick={() =>
                  this.setState({ higienizationModalIsOpen: true })
                }
                id="btn-higienization"
              >
                Higienizar
              </button>
            )}
            <button onClick={this.printItems} id="button-print-itens">
              Imprimir
            </button>
            <button
              id="button-save-itens"
              style={{
                cursor: "pointer",
                backgroundColor: "red",
              }}
              onClick={() => {
                Swal.fire({
                  title: "Deseja realmente sair da precificação?",
                  icon: "question",
                  showCancelButton: true,
                  cancelButtonText: "Não",
                  confirmButtonColor: "#3085d6",
                  cancelButtonColor: "#d33",
                  confirmButtonText: "Sim",
                  allowEscapeKey: false,
                  allowEnterKey: false,
                  allowOutsideClick: false,
                  stopKeydownPropagation: false,
                }).then((result) => {
                  if (result.isConfirmed) {
                    this.props.handleOpenClosePricingModal(false);
                    this.setState({
                      ...this.state,
                      isEnabledPercentage: false,
                      allMarkedToPrint: false,
                    });
                  }
                });
              }}
            >
              Sair
            </button>
          </div>
          <GroupItemsModal
            isOpen={this.state.concatItemsModalIsOpen}
            onClose={() => this.setState({ concatItemsModalIsOpen: false })}
            products={removeDuplicatesFromArray(
              this.state.printProducts,
              "description"
            )?.map((product) => {
              return ({
                label: product?.descricao || product?.description,
                value: String(product.description).toLowerCase(),
                qtd:
                  this.state.printProducts?.filter(
                    (p) => {
                      return p?.qtd === 1 && p?.description === product?.description
                    }
                  ).length || 0,
              })
            })}
            onConcatClick={(selectedProduct, qtd) =>
              this.handleConcatItems(selectedProduct, qtd)
            }
          />
          <HigienizationModal
            isOpen={this.state.higienizationModalIsOpen}
            onClose={() => this.setState({ higienizationModalIsOpen: false })}
            products={this.state.printProducts
              ?.filter((product) => !product?.higienization)
              ?.map((product) => ({
                label: `${product?.descricao || product?.description} ${!!product?.descPesquisa ? `- ${product?.descPesquisa}` : ""
                  }`,
                value: String(
                  `${product?.descricao || product?.description} ${!!product?.descPesquisa ? `- ${product?.descPesquisa}` : ""
                  }`
                ).toLowerCase(),
                qtd: Number(product?.qtd),
              }))}
            onSaveClick={(selectedProduct, qtd) =>
              this.handleSendProductToHigienization(selectedProduct, qtd)
            }
          />
        </div>
      </Modal>
    );
  }
}

export default PricingModal;
