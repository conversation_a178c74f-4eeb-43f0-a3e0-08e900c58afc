# FROM node:14.18.2
FROM node:18.19.1

WORKDIR /app

COPY . .

RUN rm -rf node_modules package-lock.json yarn.lock

RUN npm install --legacy-peer-deps
RUN npm i -g pm2
RUN npm run build

ADD package.json pm2.json /app/

ENV NODE_ENV=production
ENV PRODUCTION=true
ENV ENVIRONMENT=production
ENV PORT=8080
ENV VERSION="0.2"
ENV INVENTORY_API_URL=https://inventarioprod2.dfcomsoftware.com.br

ENV TECNOSPEED_BASE_URL=https://managersaas.tecnospeed.com.br:8081
ENV TECNOSPEED_PORT=8081

ENV MONGO_BASE_URI="mongodb+srv://dfcom:<EMAIL>/crescieperdi?retryWrites=true&w=majority"

ENV AWS_ACCESS_KEY_ID=********************
ENV AWS_SECRET_ACCESS_KEY=FbkeSbSelJv5Uq4Uv2f/ZwcV/4gDFq3nI7HHcjpb
ENV AWS_REGION=sa-east-1
ENV AWS_S3_BUCKET=crescieperdi.dfcom

ENV GOOGLE_API_KEY=AIzaSyC9yvEFbyi_eOMoZPmJNgHMHCINRgmlDMM

ENV NEWRELIC_LICENSE_KEY="7bce89285df6a73e06e20751c53e3290FFFFNRAL"

ENV PRIVATE_KEY="REZDT00gZ29kIGlzIGxpZmU="

# ENTRYPOINT [ "npm", "start" ]

CMD ["pm2-runtime", "start", "pm2.json"]
