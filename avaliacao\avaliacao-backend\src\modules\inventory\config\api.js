const axios = require('axios')

// eslint-disable-next-line no-undef
const inventoryApiUrl = process.env.INVENTORY_API_URL

const apiService = axios.create({
  baseURL: inventoryApiUrl,
  timeout: 5000,
  headers: {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  },
})

apiService.interceptors.request.use(config => {
  const { headers } = config
  const { authorization } = headers
  if (authorization) {
    const token = authorization.split(' ')[1]
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
  }
  return config
})

apiService.interceptors.response.use(
  response => response.data,
  error => {
    if (error.response) {
      const { status, data } = error.response
      if (status === 401) {
        return Promise.reject(data)
      }
      return Promise.reject(data)
    }
    return Promise.reject(error)
  }
)

module.exports = {
  addProduct: async (cnpj, product) => {
    let sendProduct = []

    try {
      sendProduct = await apiService.put(`/inventory/${cnpj}`, product)
    } catch (error) {
      console.log(error)
    }

    return sendProduct
  },

  getInventory: async cnpj => {
    let findByCnpj = {}

    try {
      findByCnpj = await apiService.get(`/inventory/${cnpj}`)
    } catch (error) {
      console.log(error)
    }

    return findByCnpj
  },

  createInventory: async inventory => {
    let create = []

    try {
      create = await apiService.post('/inventory', inventory)
    } catch (error) {
      console.log(error)
    }

    return create
  },

  getProduct: async (cnpj, productId) => {
    let product = {};

    try {
      product = await apiService.get(`/inventory/${cnpj}/${productId}`)
    } catch (error) {
      console.log(error)
    }

    return product;
  }
}
