const { Schema, model } = require('mongoose')

// Definindo Schema
const ValeSchema = Schema({
  cpf: {
    type: String,
    required: true,
  },
  vlrAberto: {
    type: Number,
    required: true,
  },
  vlrTotal: {
    type: Number,
    required: true,
  },
  idAvaliacao: {
    type: String,
    unique: true
  },
  codBarras: {
    type: String,
    required: true,
  },
  nomeCliente: {
    type: String,
  },
  cnpj: {
    type: String,
  },
  data: {
    type: Date,
  },
  dataExpiracao: {
    type: Date,
  },
  codCidade: {
    type: String,
  },
})

// Definindo collection
const Vale = model('vale', ValeSchema)
module.exports = Vale
