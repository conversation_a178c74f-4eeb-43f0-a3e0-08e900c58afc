.export-evaluations-container {
  display: flex;
  flex-flow: column;
  gap: 1.5rem;
  padding: 2rem;
  background-color: #FFF;
  width: 40%;
  border-radius: 5px;
}

.btn {
  padding: .5rem 1rem;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  font-weight: bold;
  font-size: 16px;
  color: #FFF;
  transition: 0.3s;
}

.btn:hover {
  opacity: 0.8;
}

.cancel-btn {
  background-color: #555;
}

.export-btn {
  background-color: peru;
}

.export-content {
  display: flex;
  justify-content: space-between;
}

.export-section {
  display: flex;
  flex-flow: column;
  align-items: center;
  width: 100%;
  gap: .5rem;
}

.separator {
  width: 1px;
  height: auto;
  border-right: 1px solid #333;
}

@media screen and (max-width: 968px) {
  .export-evaluations-container {
    width: 95%;
  }
}
