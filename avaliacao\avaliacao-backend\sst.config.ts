/// <reference path="./.sst/platform/config.d.ts" />
import config from 'dotenv'

config.config()

export default $config({
  app(input) {
    const retain = ['beta', 'production', 'test'].includes(input?.stage || '')
    return {
      name: 'crescieperdi-avaliacao',
      removal: retain ? 'retain' : 'remove',
      home: 'aws',
      runtime: 'nodejs18.x',
    }
  },

  async run() {
    if (!process.env.SST_STAGE) {
      throw new Error('STAGE is not set')
    }

    if (!['beta', 'production', 'test', 'dev'].includes(process.env.SST_STAGE)) {
      throw new Error('STAGE is not valid')
    }

    const apiGateway = new sst.aws.ApiGatewayV2(`API_${process.env.SST_STAGE}`, {
      domain: {
        name: `cep-api-avaliacao-${process.env.SST_STAGE}.dfcomsoftware.com.br`,
      },
      cors: {
        allowOrigins: ['*'],
        allowMethods: ['*'],
        allowHeaders: ['*'],
        exposeHeaders: ['refresh-token'],
      },
    })

    const vpc: { privateSubnets: string[]; securityGroups: string[] } = {
      privateSubnets: [],
      securityGroups: [],
    }
    const isProduction = ['production', 'beta'].includes(process.env.SST_STAGE)

    if (isProduction) {
      vpc.privateSubnets = ['subnet-0a890ba72fa1af526', 'subnet-098baeeeaa2b62f50']
      vpc.securityGroups = [
        'sg-020080c715e916b37',
        'sg-0d9a899bf2451b5c6',
        'sg-09194fa02d86e33fd',
        'sg-081013af3cc87cb60',
      ]
    } else {
      vpc.privateSubnets = ['subnet-0ba8ab98f952f6b32', 'subnet-0a883a0566dbd3499']
      vpc.securityGroups = [
        'sg-0011ef602a11fcf30',
        'sg-0cfe2b96b71723de3',
        'sg-0500252e4cf92e86a',
        'sg-0f8bb695b7894f0f3',
        'sg-0c1fecbe07ab89be1',
      ]
    }

    apiGateway.route('ANY /{proxy+}', {
      logging: {
        retention: '1 day',
        // logGroup: `AVALIACAO_BK_${process.env.SST_STAGE}_api`,
      },
      environment: {
        AWS_SECRET_NAME: process.env.AWS_SECRET_NAME || 'test/avaliacao-test',
        AWS_BUCKET: process.env.AWS_BUCKET || 'crescieperdi.dfcom',
        NODE_ENV: process.env.NODE_ENV || 'dev',
        PRODUCTION: isProduction.toString(),
        TECNOSPEED_BASE_URL: isProduction
          ? 'https://managersaas.tecnospeed.com.br:8081'
          : process.env.TECNOSPEED_BASE_URL || '',
        TECNOSPEED_PORT: isProduction ? '8081' : process.env.TECNOSPEED_PORT || '8081',
        SENTRY_AUTH_TOKEN:
          '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',
      },
      handler: 'src/lambda.run',
      runtime: 'nodejs18.x',
      url: true,
      timeout: '3 minutes',
      link: [apiGateway],
      permissions: [
        {
          actions: ['secretsmanager:GetSecretValue'],
          resources: ['*'],
        },
        {
          actions: ['s3:*'],
          resources: ['*'],
        },
      ],
      vpc: {
        privateSubnets: vpc.privateSubnets,
        securityGroups: vpc.securityGroups,
      },
      nodejs: {
        install: ['@sentry/profiling-node'],
      },
    })

    return {
      url: apiGateway.url,
      gateway_url: apiGateway.nodes.domainName,
    }
  },
})
