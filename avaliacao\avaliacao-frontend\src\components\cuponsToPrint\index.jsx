import { Component } from 'react'
import { AuthContext } from '../../context/auth'
import ReciboCliente from '../recibo_cliente/Recibo_cliente'
import ReciboVale from '../recibo_vale/Recibo_vale'
import Sugestion from '../sugestionSale/Sugestion'
import ReturnTerm from '../termoDevolucao/returnTerm'

export class CuponsToPrint extends Component {
  static contextType = AuthContext
  constructor(props) {
    super(props)
  }

  render() {
    const {
      nomeReduzido,
      cidade,
      nomeCliente,
      cpfCliente,
      itensReimpressao,
      valorTotalVale,
      totalPassado,
      cnpj,
      bairro,
      endereco,
      telefone,
      data,
      cpf,
      foneCliente,
      codBarras,
      tipo,
      idAvaliacao,
      isClient,
      ruaCliente,
      dtNascCliente,
      bairroCliente,
      canceledReason,
      rejectedAt,
      dataExpiracao,
      totalPoints,
      generatedPoints,
    } = this.props

    const { user } = this.context

    return (
      <div>
        <Sugestion
          nomeReduzido={nomeReduzido}
          cidade={cidade}
          nomeCliente={nomeCliente}
          cpfCliente={cpfCliente}
          itensReimpressao={itensReimpressao}
          usuarioAvaliacao={user?.usuario}
        />
        <ReciboVale
          valor={valorTotalVale}
          nomeReduzido={nomeReduzido}
          cnpj={cnpj}
          bairro={bairro}
          endereco={endereco}
          cidade={cidade}
          telefone={telefone}
          data={data}
          dataExpiracao={dataExpiracao}
          nomeCliente={nomeCliente}
          cpf={cpf}
          foneCliente={foneCliente}
          codBarras={codBarras}
          tipo={tipo}
          idAvaliacao={idAvaliacao}
        />
        <ReciboCliente
          nomeReduzido={nomeReduzido}
          cnpj={cnpj}
          bairro={bairro}
          endereco={endereco}
          cidade={cidade}
          telefone={telefone}
          data={data}
          nomeCliente={nomeCliente}
          cpf={cpf}
          ruaCliente={ruaCliente}
          bairroCliente={bairroCliente}
          dtNascCliente={dtNascCliente}
          foneCliente={foneCliente}
          valor={totalPassado}
          tipo={tipo}
          idAvaliacao={idAvaliacao}
          isClient={isClient}
          totalPoints={totalPoints}
          generatedPoints={generatedPoints}
        />
        <ReturnTerm
          date={rejectedAt}
          motivo={canceledReason}
          cpf={cpf}
          nomeCliente={nomeCliente}
          cidade={cidade}
          canceledReason={canceledReason}
          rejectedAt={rejectedAt}
        />
      </div>
    )
  }
}
