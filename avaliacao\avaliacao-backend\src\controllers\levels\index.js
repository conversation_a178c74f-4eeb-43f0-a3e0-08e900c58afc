const Nivel1 = require('../../models/Nivel1')
const Nivel2 = require('../../models/Nivel2')
const Nivel3 = require('../../models/Nivel3')
const Nivel4 = require('../../models/Nivel4')
const moment = require('moment-timezone');
const ProductModel = require('../../models/Produto')
const { getCurrentTime } = require('../../utils');
const { updateProduct, deleteProduct } = require('../../services/loc/routes');

class LevelsClass {
  constructor() { }

  async getAllLevels(req, res) {
    try {
      let filter = req.query || {}
      const level = Number(req?.query?.level);

      if (filter?.all === 'false') {
        filter.$or = [
          { create_at: { $gte: moment().startOf('day').toDate() } },
          { update_at: { $gte: moment().startOf('day').toDate() } },
        ]
      }

      filter['$or'] = [
        ...(filter?.$or || []),
        {
          deleted: { $ne: true },
          unused: false
        },
        {
          deleted: { $exists: false },
          unused: { $exists: false },
        },
        {
          deleted: { $ne: true },
          unused: { $exists: false },
        },
      ]

      delete filter?.all
      delete filter.level;
      delete filter.page;
      delete filter.limit;

      let levels = [];

      switch(level) {
        case 1:
          levels = await Nivel1
            .find(filter)
            .sort({ descricao: 1 })
            .lean();
            break;
        case 2:
          levels = await Nivel2
            .find(filter)
            .sort({ descricao: 1 })
            .lean();
            break;
        case 3:
          levels = await Nivel3
            .find(filter)
            .sort({ descricao: 1 })
            .lean();
            break;
        case 4:
          levels = await Nivel4
            .find(filter)
            .sort({ descricao: 1 })
            .lean();
            break;
      }

      const formatLevel = item => {
        return {
          _id: item._id,
          valor: item.valor,
          chave: item.chave,
          ultimoNvl: item.ultimoNvl,
          pesquisa: item.pesquisa,
          isVisible: item.isVisible,
          descricao: item.descricao,
          productDescription: item.productDescription,
          image: item.image,
          deleted: item.deleted,
          valorMin: item.valorMin,
          valorMax: item.valorMax,
          peso: item.peso,
          coeficiente: item.coeficiente,
          unused: item.unused,
          favItem: item.favItem,
          visualizar: item.visualizar,
          limitValue: item?.limitValue,
          ncm: item?.ncm,
          unmissable: item?.unmissable,
          additionalInformation: item.additionalInformation,
        }
      }

      return res.status(200).json({
        levels: levels?.map(formatLevel)
      })
    } catch (error) {
      console.log(error)
    }
  }

  async updateLastLevel(req, res) {
    const updateData = req.body

    let produtos
    let levels = []
    let updatedLevel = ''
    let updatedProduct = {}

    switch (updateData.nvl) {
      case 1:
        updatedLevel = await updateLevel(Nivel1, updateData)
        produtos = await ProductModel.find({ nivel1: updateData.id })
        levels = await Nivel2.find({ chave: updateData.id })
        break
      case 2:
        updatedLevel = await updateLevel(Nivel2, updateData)
        produtos = await ProductModel.find({ nivel2: updateData.id })
        // res.status(200).json({ updatedLevel, produtos })
        levels = await Nivel3.find({ chave: updateData.id })
        break
      case 3:
        updatedLevel = await updateLevel(Nivel3, updateData)
        produtos = await ProductModel.find({ nivel3: updateData.id })
        // res.status(200).json({ updatedLevel, produtos })
        levels = await Nivel4.find({ chave: updateData.id })
        break
      case 4:
        updatedLevel = await updateLevel(Nivel4, updateData)
        produtos = await ProductModel.find({ nivel4: updateData.id })
        // res.status(200).json({ updatedLevel, produtos })
        break
      default:
        // res.status(200).json({ error: 'Não foi possível atualizar o nível' })
        break
    }

    const levelsToUpdate = levels?.map((level) => level?._id);

    if (levelsToUpdate?.length) {
      switch (updateData.nvl) {
        case 1:
          await Nivel2.updateMany({ _id: { $in: levelsToUpdate } }, { $set: { unused: updateData?.ultimoNvl === 'sim' ? true : false } })
          break;
        case 2:
          await Nivel3.updateMany({ _id: { $in: levelsToUpdate } }, { $set: { unused: updateData?.ultimoNvl === 'sim' ? true : false } })
          break;
        case 3:
          await Nivel4.updateMany({ _id: { $in: levelsToUpdate } }, { $set: { unused: updateData?.ultimoNvl === 'sim' ? true : false } })
          break;
      }
    }

    let nivel1 = ''
    let nivel2 = ''
    let nivel3 = ''
    let nivel4 = ''
    let desc = ''

    for (let i = 0; i < produtos.length; i++) {
      if (produtos[i].nivel1) {
        nivel1 = await Nivel1.findById(produtos[i].nivel1)
        desc = `${updateData.nvl === 1 ? updateData.descricao : nivel1.descricao}`
      }

      if (produtos[i].nivel2) {
        nivel2 = await Nivel2.findById(produtos[i].nivel2)
        desc = `${nivel1.descricao} - ${updateData.nvl === 2 ? updateData.descricao : nivel2.descricao
          }`
      }

      if (produtos[i].nivel3) {
        nivel3 = await Nivel3.findById(produtos[i].nivel3)
        desc = `${nivel1.descricao} - ${nivel2.descricao} - ${updateData.nvl === 3 ? updateData.descricao : nivel3.descricao
          }`
      }

      if (produtos[i].nivel4) {
        nivel4 = await Nivel4.findById(produtos[i].nivel4)
        desc = `${nivel1.descricao} - ${nivel2.descricao} - ${nivel3.descricao} - ${updateData.nvl === 4 ? updateData.descricao : nivel4.descricao
          }`
      }

      if (!updateData.pesquisa || updateData.ultimoNvl === 'não') {
        updatedProduct = await ProductModel.findOneAndUpdate({ _id: produtos[i]._id }, {
          descricao: desc,
          // pesquisaFoto: updateData?.pesquisaFoto
        }, { new: true })
      } else {
        if (!updateData.pesquisa || updateData.pesquisa === 'não') {
          updatedProduct = await ProductModel.findOneAndUpdate(
            { _id: produtos[i]._id, ativo: true },
            {
              descricao: desc,
              vlrCusto: updateData.valor,
              vlrMin: Number(updateData.valorMin),
              vlrVenda: updateData.valorMax,
              precificacao_automatica: updateData.precificacao_automatica,
              acumulaPontos: updateData.acumulaPontos,
              tipoFixo: updateData.tipoFixo,
              valorPontosFixos: updateData.valorPontosFixos,
              update_at: moment.utc().toISOString(),
              novo: updateData?.novo,
              visualizar: updateData?.visualizar,
              unused: produtos[i].descricao !== updateData?.descricao && updateData?.ultimoNvl === 'sim' ? true : false,
              allowLocation: !!updateData?.locInfos,
              ...(updateData?.locInfos ? ({
                locInfos: updateData?.locInfos,
              }) : ({
                locInfos: {}
              }))
              // pesquisaFoto: updateData?.pesquisaFoto
            },
            { new: true }
          )

          if (updateData?.locInfos) {
            const productInfo = await ProductModel.findOne({ _id: produtos[i]._id });

            const locProduct = {
              _id: productInfo?.locProductId,
              name: updateData?.locInfos?.name,
              brand: updateData?.locInfos?.brand,
              model: updateData?.locInfos?.model,
              cost: Number(updateData?.locInfos?.costValue),
              dynamicPrice: {
                15: updateData?.locInfos?.values[14],
                30: updateData?.locInfos?.values[28],
              },
              stockManagement: true,
            }

            await updateProduct(locProduct, updateData.token);
          }
        } else
          updatedProduct = await ProductModel.findOneAndUpdate(
            { _id: produtos[i]._id },
            {
              descricao: desc,
              coeficiente: updateData.coeficiente,
              limitValue: updateData?.limitValue,
              update_at: moment.utc().toISOString(),
              novo: updateData?.novo,
              visualizar: updateData?.visualizar,
              unused: produtos[i].descricao !== updateData?.descricao && updateData?.ultimoNvl === 'sim' ? true : false
            },
            { new: true }
          )
      }
    }

    return res.status(200).json(updatedProduct)
  }

  async deleteProductLevel(req, res) {
    const { levelsDescription, levelId, token } = req.body
    let deleteLevel = {},
      productsDeleted = [],
      productsDeletedLevelAbove = []
    try {
      switch (levelsDescription) {
        case 'nivel1':
          deleteLevel = await Nivel1.findOneAndUpdate(
            { _id: levelId },
            { deleted: true, deleted_at: getCurrentTime() },
            { new: true }
          )
          productsDeleted = await disabledProducts(levelsDescription, levelId, token)

          if (deleteLevel?.ultimoNvl === 'não') {
            productsDeletedLevelAbove = await deleteLevelAbove(levelId, 'nivel2', token)
          }
          break
        case 'nivel2':
          deleteLevel = await Nivel2.findOneAndUpdate(
            { _id: levelId },
            { deleted: true, deleted_at: getCurrentTime() },
            { new: true }
          )
          productsDeleted = await disabledProducts(levelsDescription, levelId, token)

          if (deleteLevel?.ultimoNvl === 'não') {
            productsDeletedLevelAbove = await deleteLevelAbove(levelId, 'nivel3', token)
          }
          break
        case 'nivel3':
          deleteLevel = await Nivel3.findOneAndUpdate(
            { _id: levelId },
            { deleted: true, deleted_at: getCurrentTime() },
            { new: true }
          )
          productsDeleted = await disabledProducts(levelsDescription, levelId, token)

          if (deleteLevel?.ultimoNvl === 'não') {
            productsDeletedLevelAbove = await deleteLevelAbove(levelId, 'nivel4', token)
          }
          break
        case 'nivel4':
          deleteLevel = await Nivel4.findOneAndUpdate(
            { _id: levelId },
            { deleted: true, deleted_at: getCurrentTime() },
            { new: true }
          )
          productsDeleted = await disabledProducts(levelsDescription, levelId, token)
          productsDeletedLevelAbove = await deleteLevelAbove(levelId, levelsDescription, token)
          break
        default:
          break
      }

      res.status(200).json({
        status: true,
        deleteLevel,
        productsDeleted,
        productsDeletedLevelAbove,
      })
    } catch (error) {
      console.log(error)
      res.status(400).json(error)
    }
  }
}

async function deleteLevelAbove(levelId, levelsDescription, token) {
  let levelAbove = {}
  try {
    switch (levelsDescription) {
      case 'nivel2':
        levelAbove = await Nivel2.updateMany(
          { chave: levelId },
          { deleted: true, deleted_at: getCurrentTime() },
          { new: true }
        )

        await disabledProducts(levelsDescription, levelId, token)
        if (levelAbove && levelAbove.ultimoNvl === 'não') {
          await deleteLevelAbove(levelId, 'nivel3', token)
        }
        break
      case 'nivel3':
        levelAbove = await Nivel3.updateMany(
          { chave: levelId },
          { deleted: true, deleted_at: getCurrentTime() },
          { new: true }
        )

        await disabledProducts(levelsDescription, levelId, token)
        if (levelAbove && levelAbove.ultimoNvl === 'não') {
          await deleteLevelAbove(levelId, 'nivel4', token)
        }

        break
      case 'nivel4':
        levelAbove = await Nivel4.updateMany(
          { chave: levelId },
          { deleted: true, deleted_at: getCurrentTime() },
          { new: true }
        )

        await disabledProducts(levelsDescription, levelId, token)
        break
      default:
        break
    }
    return levelAbove
  } catch (error) {
    throw new Error(error)
  }
}

async function disabledProducts(levelsDescription, levelId, token) {
  try {
    const productsToDisable = await ProductModel?.find({ [levelsDescription]: levelId }).lean() || [];

    for (const product of productsToDisable) {
      await ProductModel.update(
        { _id: product?._id },
        {
          ativo: false,
          deleted: true,
          deleted_at: getCurrentTime(),
        },
        { new: true }
      )

      if (product?.locProductId && token) {
        await deleteProduct(product?.locProductId, token)
      }
    }
  } catch (error) {
    console.log(error)
    throw error
  }
}

async function updateLevel(levelModel, data) {
  return await levelModel.findOneAndUpdate(
    { _id: data.id },
    {
      coeficiente: data.coeficiente,
      valor: data.valor,
      valorMin: data.valorMin,
      valorMax: data.valorMax,
      peso: data.peso,
      descricao: data.descricao,
      ultimoNvl: data.ultimoNvl,
      pesquisa: data.pesquisa,
      precificacao_automatica: data.precificacao_automatica,
      limitValue: data?.limitValue,
      novo: data?.novo,
      visualizar: data?.visualizar,
      allowLocation: !!data?.locInfos,
      ...(data?.locInfos ? ({
        locInfos: data?.locInfos,
      }) : {})
      // pesquisaFoto: data?.pesquisaFoto || false,
    },
    { new: true }
  )
}

module.exports = LevelsClass
