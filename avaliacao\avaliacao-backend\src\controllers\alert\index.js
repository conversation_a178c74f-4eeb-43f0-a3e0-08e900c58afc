const AlertModel = require('../../models/Alert');

class AlertController {
  constructor() { };

  async sendAlert(req, res) {
    const {
      type,
      cpf,
      message,
      data,
    } = req.body;

    const dataToInsert = {
      application: 'avaliacao',
      type,
      cpf,
      message,
      data,
      cnpj: req?.usuario?.enterprise?.cnpj,
    }

    try {
      await AlertModel.create(dataToInsert);
      return res.status(200).json();
    } catch (error) {
      console.log(error);
      return res.status(500).json({
        error
      })
    }
  }
}

module.exports = AlertController;
