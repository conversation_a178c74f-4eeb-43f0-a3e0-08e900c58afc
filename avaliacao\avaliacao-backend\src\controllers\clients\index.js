const ClientModel = require('../../models/Cliente')
const EvaluationModel = require('../../models/Avaliacao')
const EnterpriseModel = require('../../models/Empresa')
const ReviewsModel = require('../../models/Reviews')
const { getLatLongByAddress } = require('../../helpers/getLatLongByAddress')
const sendFileToS3 = require('../../utils/send-file-s3')
const moment = require('moment')
const { encrypt, decrypt } = require('../../utils/crypto')
class ClientsClass {
  constructor() {}

  async getClientsByCpf(req, res) {
    const { cpf } = decrypt(req.body.data, process.env.PRIVATE_KEY)

    if (!cpf || cpf?.length < 3) {
      return res.status(400).json({
        status: false,
        error: 'CPF inválido',
      })
    }

    const client = await ClientModel.findOne({ cpf }).lean()
    return res.status(200).json({
      data: encrypt(client, process.env.PRIVATE_KEY).toString(),
    })
  }

  async getTotemClientsByCpf(req, res) {
    const { cpf } = req.body || {}

    if (!cpf || cpf?.length < 3) {
      return res.status(400).json({
        status: false,
        error: 'CPF inválido',
      })
    }

    const client = await ClientModel.findOne({ cpf }).lean()
    return res.status(200).json(client)
  }

  async getClientHistory(req, res) {
    const { search } = req.query

    let filter = {}

    if (!isNaN(Number(search))) {
      filter = {
        cpf: { $regex: diacriticSensitiveRegex(search) }
      }
    } else {
      filter = {
        cliente: { $regex: new RegExp(diacriticSensitiveRegex(search), 'i') }
      }
    }

    let clientEvaluations = await EvaluationModel.aggregate([
      {
        $match: {
          ...filter,
          finalizado: true,
        },
      },
      {
        $sort: {
          dataInicio: -1,
        },
      },
      {
        $limit: 50,
      },
      {
        $lookup: {
          as: 'empresa',
          from: 'empresas',
          localField: 'cnpj',
          foreignField: 'cnpj',
        },
      },
      {
        $project: {
          nomeUnidade: '$empresa.nomeUnidade',
          dataFinal: 1,
          status: 1,
          motivo: 1,
          cancelado: 1,
          reject_at: 1,
          cliente: 1,
          idPreAvaliacao: 1,
          finalizado: 1,
          cpf: 1,
        },
      },
    ])

    const clientEvaluationsHistory = await Promise.all(
      clientEvaluations?.map(async evaluation => {
        let evaluationData = { ...evaluation }

        const evaluationHistory = await ReviewsModel.findOne({ referenceId: evaluation?._id })

        if (evaluationHistory) {
          evaluationData.points = evaluationHistory?.points
          evaluationData.observation = evaluationHistory?.observation
        }

        return evaluationData
      })
    )

    return res.status(200).json({
      data: encrypt(
        {
          status: true,
          evaluations: clientEvaluationsHistory,
        },
        process.env.PRIVATE_KEY
      ).toString(),
    })
  }

  async createClient(req, res) {
    try {
      const { file, ...cliente } = req.body

      const clientAlreadyExists = await ClientModel.findOne({ cpf: cliente.cpf })

      if (clientAlreadyExists) {
        return res.status(400).json({
          status: false,
          error: 'Cliente já cadastrado',
        })
      }

      const addressFormatted = [
        cliente.rua,
        cliente.numero,
        cliente.bairro,
        cliente.cidade,
        cliente.estado,
      ].join(', ')

      const geo = await getLatLongByAddress(addressFormatted)

      if (geo.latitude) {
        cliente.geo = {
          latitude: geo.latitude,
          longitude: geo.longitude,
        }
      }

      let client = null

      if (!clientAlreadyExists) {
        client = await ClientModel.create(cliente)
      } else {
        client = clientAlreadyExists
      }

      if (file) {
        const fileURL = await uploadBase64ToS3(
          file,
          `${process.env.ENVIRONMENT}/avaliacao/clientes/`
        )
        client.image = fileURL
        await client.save()
      }

      return res.status(200).json(client)
    } catch (error) {
      console.log(error)
      return res.status(400).json({
        status: false,
        error: 'Erro ao cadastrar cliente',
      })
    }
  }

  async updateClient(req, res) {
    try {
      const {
        nome,
        cpf,
        dtNascimento,
        celular,
        rua,
        bairro,
        numero,
        cep,
        cod_uf,
        cod_cidade,
        estado,
        cidade,
        complemento,
        image,
        file,
      } = req.body || {}

      if (!cpf || cpf.length < 4) {
        return res.json({
          status: false,
          message: 'Cliente não encontrado!',
        })
      }

      const findCliente = await ClientModel.findOne({ cpf }).lean()

      if (!findCliente) {
        return res.json({
          status: false,
          message: 'Cliente não encontrado!',
        })
      }

      const clienteRequest = {
        nome,
        cpf,
        dtNascimento,
        celular,
        rua,
        bairro,
        numero,
        cep,
        cod_uf,
        cod_cidade,
        estado,
        cidade,
        complemento,
        image,
      }

      // Se for a mesma rua ou número ou o usuário não tiver geo, consulta e atualiza
      if (
        clienteRequest.rua != findCliente.rua ||
        Number(clienteRequest.numero) !== findCliente.numero ||
        !findCliente?.['geo']?.['latitude']
      ) {
        const addressFormatted = [
          clienteRequest.rua,
          clienteRequest.numero,
          clienteRequest.bairro,
          clienteRequest.cidade,
          clienteRequest.estado,
        ].join(', ')

        const geo = await getLatLongByAddress(addressFormatted)

        if (geo?.latitude) {
          clienteRequest.geo = {
            latitude: geo.latitude,
            longitude: geo.longitude,
          }
        }
      }

      if (file)
        clienteRequest.image = await uploadBase64ToS3(
          file,
          `${process.env.ENVIRONMENT}/avaliacao/clientes/`
        )

      const update = await ClientModel.findOneAndUpdate({ cpf }, clienteRequest, {
        new: true,
      }).lean()
      res.json(update)
    } catch (erro) {
      res.json(erro)
    }
  }

  async getAllClients(req, res) {
    // salvar clientes do redis e recuperar, gerar uma rota dinamica e por na cron
    // verificar se existem dados no redis antes de realizar a query abaixo
    // verificar se consumirá mt processamento durante a semana
    // const clients = await ClientModel.find({
    //   cod_cidade: req.usuario.enterprise.codCidade
    // }).lean();
    const clients = []
    return res.status(200).json({
      data: encrypt(clients, process.env.PRIVATE_KEY).toString(),
    })
  }

  async getAllClientsTotem(req, res) {
    // salvar clientes do redis e recuperar, gerar uma rota dinamica e por na cron
    // verificar se existem dados no redis antes de realizar a query abaixo
    // verificar se consumirá mt processamento durante a semana
    const clients = await ClientModel.find({
      cod_cidade: req.usuario.enterprise.codCidade
    }).lean();

    return res.status(200).json(clients)
  }
}

async function uploadBase64ToS3(file, path) {
  const fileBuffer = Buffer.from(file.split(';base64,').pop(), 'base64')
  const fileName = `${Date.now()}.png`

  const fileFormmat = {
    name: fileName,
    data: fileBuffer,
  }
  const fileData = await sendFileToS3(fileFormmat, path)

  if (!fileData && !fileData.fileUrl) return null

  return fileData.fileUrl
}

function diacriticSensitiveRegex(string = '') {
  return string
    .replace(/a/g, '[a,á,à,ä,â]')
    .replace(/A/g, '[A,a,á,à,ä,â]')
    .replace(/e/g, '[e,é,ë,è]')
    .replace(/E/g, '[E,e,é,ë,è]')
    .replace(/i/g, '[i,í,ï,ì]')
    .replace(/I/g, '[I,i,í,ï,ì]')
    .replace(/o/g, '[o,ó,ö,ò]')
    .replace(/O/g, '[O,o,ó,ö,ò]')
    .replace(/u/g, '[u,ü,ú,ù]')
    .replace(/U/g, '[U,u,ü,ú,ù]')
    .replace(/^a-z/g, '[]')
}

module.exports = ClientsClass
