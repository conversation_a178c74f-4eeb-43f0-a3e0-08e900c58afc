env:
  node: true
extends:
  - plugin:@typescript-eslint/recommended
  - plugin:prettier/recommended
parser: '@typescript-eslint/parser'
parserOptions:
  ecmaVersion: 9
  project: ./tsconfig.json
plugins:
  - '@typescript-eslint'
rules:
  '@typescript-eslint/explicit-module-boundary-types': 'off'
  '@typescript-eslint/no-explicit-any': 'off'
  '@typescript-eslint/no-namespace': 'off'
  'no-console': 1
  'object-curly-newline': 'off'
  'eslint-disable-next-line': 'off'
  '@typescript-eslint/no-empty-function': 'off'
  '@typescript-eslint/ban-types': 'off'
