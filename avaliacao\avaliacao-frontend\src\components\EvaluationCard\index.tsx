import { memo, useMemo } from "react";
import { useAuth } from "../../context/auth";
import { Tooltip as ReactTooltip } from "react-tooltip";
import "../../pages/lista_avaliacao/Lista_avaliacao.css";
import moment from "../../helpers/moment";
import { useOffline } from "../../context/offline";
import ApiService from "../../services/ApiService";
import Swal from "sweetalert2";

interface EvaluationCardProps {
  evaluation: any;
  index: number;
  onClick: (item: any) => void;
  onItemClick: (item: any) => void;
  onViewImagesClick?: () => void;
  onReprintClick: (item: any) => void;
  onPrecifyClick: (item: any) => void;
  onGenerateNfeClick: (item: any) => void;
  onCancelClick: (item: any) => void;
  onChangeTypeClick: () => void;
  stateNFeRef: any;
  messageForNFe: any;
  typeMessage: any;
  statusNfe: string;
}

const EvaluationCard: React.FC<EvaluationCardProps> = ({
  evaluation,
  index,
  onClick,
  onItemClick,
  onReprintClick,
  onPrecifyClick,
  onGenerateNfeClick,
  onCancelClick,
  stateNFeRef,
  messageForNFe,
  typeMessage,
  statusNfe,
  onChangeTypeClick,
  onViewImagesClick,
}) => {
  const { user } = useAuth();
  const { isOffline } = useOffline();

  const nfeButton = useMemo(() => {
    if (!evaluation?.eventosFiscais?.length) {
      return 'generate'
    } else if (statusNfe === "Emitida") {
      return 'disabled';
    } else if (statusNfe === 'Não emitido') {
      return 'generate'
    } else {
      return 'query'
    }
  }, [statusNfe, evaluation]);

  function transformPointsAvaliationToText(evaluationPoints: number) {
    if (evaluationPoints === 10) {
      return "Ótimo";
    }

    if (evaluationPoints === 5) {
      return "Médio";
    }

    if (evaluationPoints === 0) {
      return "Ruim";
    }

    return "-";
  }

  const handleChangeTypeClick = async () => {
    const {
      data: { isClosed },
    } = await ApiService.VerifyIfCashierIsClosed(evaluation?.caixa);

    if (isClosed) {
      return Swal.fire(
        "Caixa fechado",
        "Você não pode completar essa ação, o caixa utilizado para realizar a avaliação foi fechado!",
        "error"
      );
    }
    onChangeTypeClick();
  };

  return (
    <div className="avaliacoes hidden-print">
      <table
        id="tabela-avaliacao"
        onClick={onClick}
        className={`table-linha hidden-print ${evaluation.classe}`}
      >
        <tbody>
          <tr>
            <td className="item-avaliacoes td1">Data Inicial</td>
            <td className="item-avaliacoes td1">Data Final</td>
            <td className="item-avaliacoes td1">Fornecedor</td>
            <td className="item-avaliacoes td1">Total Efetivado</td>
            <td className="item-avaliacoes td1">Tipo</td>
          </tr>
          <tr>
            <td className="item-avaliacoes corpo td1">
              {moment(evaluation?.dataInicio).format("DD/MM/YYYY HH:mm:ss")}
            </td>
            <td className="item-avaliacoes corpo td1">
              {evaluation?.dataFinal !== null
                ? moment
                    (evaluation?.dataFinal)
                    .format("DD/MM/YYYY HH:mm:ss")
                : "(Não informado)"}
            </td>
            <td className="item-avaliacoes corpo td1">{evaluation.cliente}</td>
            <td className="item-avaliacoes corpo td1">
              {evaluation?.statusAvaliation !== "Em aberto"
                ? Intl.NumberFormat("pt-BR", {
                    currency: "BRL",
                    style: "currency",
                  }).format(
                    Number(
                      evaluation.tipo !== "Pix"
                        ? evaluation.totalEfetivado || 0
                        : evaluation.totalPixEfetivado
                    )
                  )
                : "R$ 0.00"}
            </td>
            <td className="item-avaliacoes corpo td1">
              {evaluation.tipo === "Vale"
                ? import.meta.env.VITE_GIRACREDITO
                : evaluation?.tipo === "Pix"
                ? "Pix"
                : "Dinheiro"}
            </td>
          </tr>
          <tr>
            <td className="item-avaliacoes">Unidade:</td>
            <td className="item-avaliacoes">NFe:</td>
            <td className="item-avaliacoes">Status:</td>
            <td className="item-avaliacoes">Motivo:</td>
            <td className="item-avaliacoes td2">Usuário</td>
          </tr>
          <tr>
            <td className="item-avaliacoes corpo">
              {evaluation.unidade || user?.enterprise?.nomeUnidade}
            </td>
            <td
              ref={(ref) => (stateNFeRef[index] = ref)}
              className="item-avaliacoes corpo"
            >
              {evaluation.nfeStatus}
            </td>
            <td className="item-avaliacoes corpo">
              {evaluation?.statusAvaliation || "-"}
              <span
                id={`offline-tip-${index}`}
                data-tooltip-content="Avaliação realizada offline"
              >
                {evaluation?.offline ? "(Offline)" : ""}
              </span>
            </td>
            <td className="item-avaliacoes corpo">
              {evaluation?.motivo || ""}
            </td>
            <td className="item-avaliacoes corpo td2">
              {evaluation.usuario || ""}
            </td>
          </tr>
          <tr id={`escondido${index}`} className="escondido">
            <td className="item-avaliacoes td2">CPF</td>
            <td className="item-avaliacoes td2">Oferta</td>
            <td className="item-avaliacoes td2">Total Padrão</td>
            <td className="item-avaliacoes td2">Telefone:</td>
            <td className="item-avaliacoes td2">Data Pré Avaliação:</td>
            {/* <td className='item-avaliacoes td3'>Total Vale Efetivado</td>
                                                                  <td className='item-avaliacoes td3'>Total Vale Padrão</td> */}
          </tr>
          <tr id={`escondido${index}`} className="escondido">
            <td className="item-avaliacoes corpo td2">{evaluation.cpf}</td>
            <td className="item-avaliacoes corpo td2">{evaluation.oferta}</td>
            <td className="item-avaliacoes corpo td2">
              {isNaN(
                Number(
                  evaluation?.tipo !== "Pix"
                    ? evaluation.totalPadrao
                    : evaluation.totalPixPadrao
                )
              )
                ? evaluation?.tipo !== "Pix"
                  ? evaluation.totalPadrao
                  : evaluation?.totalPixPadrao
                : `R$ ${Number(
                    evaluation?.tipo !== "Pix"
                      ? evaluation.totalPadrao
                      : evaluation.totalPixPadrao
                  ).toFixed(2)}`}
            </td>
            <td className="item-avaliacoes corpo td2">
              {evaluation.telefone || "-"}
            </td>
            <td className="item-avaliacoes corpo td2">
              {evaluation?.dataPreAvaliacao
                ? moment
                    (evaluation?.dataPreAvaliacao)
                    .format("DD/MM/YYYY HH:mm:ss")
                : ""}
            </td>
          </tr>
          <tr id={`escondido${index}`} className="escondido">
            <td className="item-avaliacoes td3">Avaliador</td>
            <td className="item-avaliacoes td3">Avaliação do fornecedor</td>
            <td className="item-avaliacoes td3">Avaliação</td>
            <td className="item-avaliacoes td3">Estado do fornecedor</td>
          </tr>
          <tr id={`escondido${index}`} className="escondido">
            <td className="item-avaliacoes corpo td3">
              {evaluation?.evaluatorName || "-"}
            </td>
            <td className="item-avaliacoes corpo td3">
              {evaluation?.evaluationObservation || "-"}
            </td>
            <td className="item-avaliacoes corpo td3">
              {transformPointsAvaliationToText(evaluation?.evaluationPoints)}
            </td>
            <td className="item-avaliacoes corpo td3">
              {evaluation?.uf || "-"}
            </td>
          </tr>
        </tbody>
      </table>
      <div
        id="avaliacao-mobile"
        className={`hidden-print ${evaluation.classe}`}
      >
        <div className="header-mobile">Data Inicial</div>
        <div className="body-mobile">
          {moment(evaluation.dataInicio).format("DD/MM/YYYY HH:mm:ss")}
        </div>
        <div className="header-mobile">Data Final</div>
        <div className="body-mobile">
          {evaluation.dataFinal
            ? moment(evaluation.dataFinal).format("DD/MM/YYYY HH:mm:ss")
            : "(Não informado)"}
        </div>
        <div className="header-mobile">Total Efetivado</div>
        <div className="body-mobile">
          {!evaluation?.cancelado
            ? isNaN(Number(evaluation.totalEfetivado))
              ? evaluation.totalEfetivado
              : `R$ ${Number(evaluation.totalEfetivado).toFixed(2)}`
            : Intl.NumberFormat("pt-BR", {
                style: "currency",
                currency: "BRL",
              }).format(Number(evaluation.totalDinheiroEfetivado) || 0)}
        </div>
        <div className="header-mobile">Tipo</div>
        <div className="body-mobile">
          {!evaluation?.cancelado
            ? evaluation.tipo === "Vale"
              ? import.meta.env.VITE_GIRACREDITO
              : evaluation?.tipo
              ? evaluation?.tipo
              : "(Não informado)"
            : "Dinheiro"}
        </div>
        <div className="header-mobile">Fornecedor</div>
        <div className="body-mobile">{evaluation.cliente}</div>
      </div>
      <div
        className="item-avaliacoes hidden-print"
        style={{ display: "flex", justifyContent: "center", flexWrap: "wrap" }}
      >
        <button
          disabled={isOffline || evaluation?.statusAvaliation === 'Em aberto'}
          className="item2"
          onClick={onItemClick}
          style={{
            border: "none",
            fontWeight: "600",
            fontSize: "initial",
          }}
        >
          <span
            id={`itens-evaluation-card-tip-${index}`}
            data-tooltip-content={
              isOffline ? "Não é possivel executar essa operação offline" : ""
            }
          >
            Itens
          </span>
        </button>
        <button
          disabled={
            isOffline || !evaluation?.images?.length || !evaluation?.images
          }
          className="item2"
          onClick={onViewImagesClick}
          style={{
            border: "none",
            fontWeight: "600",
            fontSize: "initial",
            width: "180px",
          }}
        >
          <span
            id={`itens-evaluation-card-tip-${index}`}
            data-tooltip-content={
              isOffline
                ? "Não é possivel executar essa operação offline"
                : !evaluation?.images?.length || !evaluation?.images
                ? "Não há imagens para visualizar"
                : ""
            }
            style={{
              whiteSpace: "nowrap",
            }}
          >
            Visualizar imagens
          </span>
        </button>
        <button
          disabled={isOffline || evaluation?.statusAvaliation === 'Em aberto'}
          id="reprintBtn"
          className="item2"
          style={{
            border: "none",
            fontWeight: "600",
            fontSize: "initial",
          }}
          onClick={onReprintClick}
        >
          <span
            id={`reprint-evaluation-card-tip-${index}`}
            data-tooltip-content={
              isOffline ? "Não é possivel executar essa operação offline" : evaluation?.statusAvaliation === 'Em aberto' ? 'Não é possivel fazer a reimpressão de uma avaliação em aberto' : ""
            }
          >
            Reimpressão
          </span>
        </button>
        <button
          disabled={isOffline || evaluation.cancelado === true ? true : false || evaluation?.statusAvaliation === 'Em aberto'}
          className="item2 lista-avaliacao-btn-precificar"
          style={{
            border: "none",
            fontWeight: "600",
            fontSize: "initial",
            backgroundColor: "#ffcc00",
          }}
          onClick={handleChangeTypeClick}
        >
          <span
            id={`precify-evaluation-card-tip-${index}`}
            data-tooltip-content={
              isOffline
                ? "Não é possivel executar essa operação offline"
                : evaluation.cancelado === true
                ? "Avaliação cancelada!"
                : ""
            }
          >
            Alterar Tipo
          </span>
        </button>

        <button
          disabled={isOffline || evaluation.cancelado === true ? true : false || evaluation?.statusAvaliation === 'Em aberto' || evaluation?.manual}
          className="item2 lista-avaliacao-btn-precificar"
          style={{
            border: "none",
            fontWeight: "600",
            fontSize: "initial",
            backgroundColor: "#ffcc00",
          }}
          onClick={onPrecifyClick}
        >
          <span> Precificar</span>
        </button>

        {nfeButton !== "query" ? (
          <button
            disabled={
              isOffline || evaluation.cancelado
                ? true
                : false || nfeButton === "disabled"
              || evaluation?.statusAvaliation === 'Em aberto'
            }
            className="item2"
            onClick={onGenerateNfeClick}
            style={{
              backgroundColor: "#28d",
              border: "none",
              fontWeight: "bold",
            }}
          >
            <span
              id={`generate-evaluation-card-tip-${index}`}
              data-tooltip-content={
                isOffline
                  ? "Não é possivel executar essa operação offline"
                  : evaluation?.cancelado === true
                  ? "Avaliação cancelada"
                  : ""
              }
            >
              Gerar NFe
            </span>
          </button>
        ) : (
          <button
            disabled={isOffline || evaluation.cancelado ? true : false}
            className="item2"
            onClick={onGenerateNfeClick}
            style={{
              backgroundColor: "#28d",
              border: "none",
              fontWeight: "bold",
            }}
          >
            <span
              id={`generate-evaluation-card-tip-${index}`}
              data-tooltip-content={
                isOffline
                  ? "Não é possivel executar essa operação offline"
                  : evaluation?.cancelado === true
                  ? "Avaliação cancelada"
                  : ""
              }
            >
              Consultar NFe
            </span>
          </button>
        )}

        <button
          disabled={isOffline || evaluation.cancelado === true ? true : false}
          className="item2"
          style={{
            border: "none",
            fontWeight: "600",
            fontSize: "initial",
            backgroundColor: "red",
          }}
          onClick={onCancelClick}
        >
          <span
            id={`cancel-evaluation-card-tip-${index}`}
            data-tooltip-content={
              isOffline
                ? "Não é possivel executar essa operação offline"
                : evaluation.cancelado === true
                ? "Avaliação cancelada!"
                : ""
            }
          >
            Cancelar
          </span>
        </button>
      </div>
      <ReactTooltip anchorId={`itens-evaluation-card-tip-${index}`} />
      <ReactTooltip anchorId={`reprint-evaluation-card-tip-${index}`} />
      <ReactTooltip anchorId={`precify-evaluation-card-tip-${index}`} />
      <ReactTooltip anchorId={`generate-evaluation-card-tip-${index}`} />
      <ReactTooltip anchorId={`cancel-evaluation-card-tip-${index}`} />
      <ReactTooltip anchorId={`offline-tip-${index}`} />
    </div>
  );
};

export default memo(EvaluationCard);
