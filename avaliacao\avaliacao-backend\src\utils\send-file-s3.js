const S3 = require('aws-sdk/clients/s3')
const { v4 } = require('uuid')

const getFileExtensionName = fileName => fileName.match(/\.(tx2|txt|jpg|jpeg|png|svg)$/i).shift()

module.exports = async (file, filePath) => {
  try {
    const fileName = v4() + getFileExtensionName(file.name)
    const s3 = new S3({
      apiVersion: '2006-03-01',
      region: process.env.APP_AWS_REGION,
    })

    const params = {
      Bucket: process.env.APP_AWS_S3_BUCKET,
      Key: `${filePath}/${fileName}`,
      Body: file.data,
    }

    const data = await s3.upload(params).promise()

    return {
      fileName: fileName,
      fileUrl: data.Location,
    }
  } catch (error) {
    console.log(error)
  }
}
