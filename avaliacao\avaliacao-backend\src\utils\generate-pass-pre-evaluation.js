const utils = require('./index')
const PreEvaluationModel = require('../models/Pre_Avaliacao')
const moment = require('moment')

module.exports.generatePasswordOfPreEvaluation = async companyRegisterNumber => {
  let passPreEvaluation
  do {
    passPreEvaluation = getRandomNumber()
  } while (await isValidPassword(passPreEvaluation, companyRegisterNumber))
  return passPreEvaluation
}

const getRandomNumber = () => Math.floor(Math.random() * (999 - 1 + 1)) + 1

const isValidPassword = async (pass, companyRegisterNumber) => {
  const preEvaluation = await PreEvaluationModel.find({
    cnpj: companyRegisterNumber,
    pre_avaliacao_senha: pass,
    data: { $gte: moment(utils.getCurrentTime()).format('YYYY-MM-DD') },
    finalizado: false,
  }).lean()
  return !!preEvaluation.length
}
