const fs = require('fs')
const path = require('path')
const sendFileToS3 = require('../utils/send-file-s3')

const uploadTx2File = tx2FileName => {
  const tx2Path = path.resolve(process.cwd())

  const tx2File = fs.readFileSync(`${tx2Path}/${tx2FileName}`)

  const tx2Buffer = {
    data: Buffer.from(tx2File),
    name: tx2FileName,
  }

  return sendFileToS3(tx2Buffer, `${process.env.ENVIRONMENT}/avaliacao/nfes/tx2`)
}

module.exports = { uploadTx2File }
