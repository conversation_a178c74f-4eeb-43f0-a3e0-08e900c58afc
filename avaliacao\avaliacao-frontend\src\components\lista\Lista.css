#input-desc1 {
  display: block;
}

#btn-adc1 {
  display: block;
}

#checkbox4 {
  display: none;
}

#lista5 {
  display: none;
}

.input-container {
  margin-top: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.2rem;
}

.input-container label {
  font-weight: bold;
}

.descricao {
  margin-top: 0;
}

.product-image-input {
  border: 1px solid #333;
  padding: .5rem;
  border-radius: 4px;
  cursor: pointer;
}

.attach-product-menu-list {
  background: #FFF;
  width: 200px;
  height: 90px;
  border-radius: 10px;
  border: 1px solid #ececec;
  animation: fadeIn 0.3s;
  position: absolute;
  margin-top: 3rem;
  margin-left: 7rem;
  box-shadow: 0px 0px 15px 0px #999;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  color: #000;
}

.attach-product-menu-list label {
  font-size: 18px;
  padding: .5rem;
  cursor: pointer;
  transition: 0.3s;
  display: flex;
  gap: .5rem;
  align-items: center;
}

.attach-product-menu-list label:hover {
  opacity: 0.8;
}

.delete-attached-image-button {
  border: none;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #cc0000;
  color: #FFF;
  display: flex;
  align-items: center;
  justify-content: center;

  position: absolute;
  margin-left: 80px;
  margin-top: -120px;

  transition: 0.3s;
  cursor: pointer;
}

.delete-attached-image-button:hover {
  opacity: 0.8;
}
