const locApi = require('./api');

const createProduct = async (req, token) => {
  const { data } = await locApi.post(
    'products/create',
    req,
    {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }
  );
  return data;
}

const createProductLocation = async (req, token) => {
  const { data } = await locApi.post(
    'products-location/create',
    req,
    {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }
  );
  return data;
}

const updateProduct = async (req, token) => {
  console.log(token)

  const { data } = await locApi.put(
    'products/update',
    req,
    {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }
  );
  return data
}

const deleteProduct = async (id, token) => {
  const { data } = await locApi.delete(
    `products/${id}`,
    {
      headers: {
        Authorization: `Bearer ${token}`
      }
    }
  );
  return data
}

module.exports = {
  createProduct,
  updateProduct,
  deleteProduct,
  createProductLocation
}
