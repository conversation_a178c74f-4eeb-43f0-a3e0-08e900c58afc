const { Router } = require('express')
const router = Router()

const isAuth = require('../../../middlewares/isAuth')
const isTotemAuth = require('../../../middlewares/isTotemAuth')

const ClientsClass = require('../../../controllers/clients')
const ClientsController = new ClientsClass()

router.post('/', isAuth, ClientsController.createClient)
router.put('/', isAuth, ClientsController.updateClient)
router.post('/findClientByCpf', isAuth, ClientsController.getClientsByCpf)
router.post('/findByCpf', isTotemAuth, ClientsController.getTotemClientsByCpf)
router.get('/history', isAuth, ClientsController.getClientHistory)
router.get('/totem', isAuth, ClientsController.getAllClientsTotem);
router.get('/', isAuth, ClientsController.getAllClients);

module.exports = router
