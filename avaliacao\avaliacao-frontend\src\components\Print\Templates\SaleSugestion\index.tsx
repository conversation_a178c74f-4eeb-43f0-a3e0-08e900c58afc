import { arredondarValores } from '../../../../utils/avaliationUtils';
import { IPrintSaleSugestionData } from '../../types';
import './styles.css'

interface SaleSugestionProps {
  data: IPrintSaleSugestionData;
}

const SaleSugestion: React.FC<SaleSugestionProps> = ({ data: {
  username,
  closures,
  reprintItems,
  client,
}}) => {
  return (
    <div id="print_sugestion" style={{ display: 'block', fontFamily: "'Poppins', sans-serif" }}>
      <section style={{ width: '100%', textAlign: 'center' }}>
        <p style={{ fontSize: '0.7rem', margin: 0 }}>
          <strong>Avaliador:</strong> {username}
        </p>
        <p style={{ fontSize: '0.7rem', margin: 0 }}>
          <strong>Fornecedor:</strong> {client?.name}
        </p>
        <p style={{ fontSize: '0.7rem', margin: 0 }}>
          <strong>CPF:</strong> {client?.cpf}
        </p>
      </section>

      <section className="productInfoTable" style={{ marginBottom: '15px' }}>
        <p style={{ fontSize: '12px', textAlign: "center", margin: 0 }}>------------------------------</p>
        <h3 style={{ textAlign: "center" }}>Produtos Avaliação</h3>
        <table style={{ fontSize: '12px', borderCollapse: 'collapse' }}>
          <thead>
            <tr>
              <th>Descrição</th>
              <th>Qtd</th>
              <th>Valor Minimo</th>
              <th>Valor Máximo</th>
            </tr>
          </thead>

          <tbody style={{ textAlign: 'center' }}>
            {closures
              ?.filter(item => item.fechamento[item.fechamento.length - 1]?.qtd > 0)
              ?.map((item, index) => (
                <tr key={index} style={{ borderBottom: '1px dashed #ffcc00' }}>
                  <td>
                    {item.fechamento[0].item.descricao} {item.fechamento[1]?.item?.descricao}{' '}
                    {item.fechamento[2]?.item?.descricao} {item.fechamento[3]?.item?.descricao}
                  </td>
                  <td>{item.fechamento[item.fechamento.length - 1].qtd}</td>
                  <td>
                    {item.fechamento[item.fechamento.length - 1]?.item?.valorMin === null
                      ? arredondarValores(
                          Number(item.fechamento[item.fechamento.length - 1]?.item?.valor),
                          2
                        ).toLocaleString('pt-br', { style: 'currency', currency: 'BRL' })
                      : arredondarValores(
                          Number(item.fechamento[item.fechamento.length - 1]?.item?.valorMin),
                          2
                        ).toLocaleString('pt-br', { style: 'currency', currency: 'BRL' })}
                  </td>
                  <td>
                    {item.fechamento[item.fechamento.length - 1]?.item?.vlrVenda === null
                      ? arredondarValores(
                          Number(item.fechamento[item.fechamento.length - 1]?.item?.valor),
                          1.5
                        ).toLocaleString('pt-br', { style: 'currency', currency: 'BRL' })
                      : arredondarValores(
                          Number(item.fechamento[item.fechamento.length - 1]?.item?.vlrVenda),
                          1.5
                        ).toLocaleString('pt-br', { style: 'currency', currency: 'BRL' })}
                  </td>
                </tr>
              ))}
            {reprintItems
              ?.filter(item => item.items.qtd > 0)
              ?.map((item, index) => {
                return (
                  <tr key={index} style={{ borderBottom: '1px dashed black' }}>
                    <td>{item.prod_item.descricao}</td>
                    <td>{item.items.qtd}</td>
                    <td>
                      {item.prod_item.pesquisa
                        ? arredondarValores(Number(item.items.precoBruto), 2).toLocaleString(
                            'pt-br',
                            { style: 'currency', currency: 'BRL' }
                          )
                        : item.prod_item?.vlrMin?.toLocaleString('pt-br', {
                            style: 'currency',
                            currency: 'BRL',
                          })}
                    </td>
                    <td>
                      {item.prod_item.pesquisa
                        ? arredondarValores(
                            Number(item.items.precoBruto),
                            1.5
                          ).toLocaleString('pt-br', { style: 'currency', currency: 'BRL' })
                        : item.prod_item?.vlrVenda?.toLocaleString('pt-br', {
                            style: 'currency',
                            currency: 'BRL',
                          })}
                    </td>
                  </tr>
                )
              })}
          </tbody>
        </table>
        <br />
      </section>
    </div>
  );
}

export default SaleSugestion;
