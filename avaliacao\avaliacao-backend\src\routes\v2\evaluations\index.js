const { Router } = require('express')
const router = Router()

const isAuth = require('../../../middlewares/isAuth')
const { Upload } = require('../../../modules/multer')

const EvaluationClass = require('../../../controllers/evaluations')
const EvaluationController = new EvaluationClass()

router.post('/get-by-id', isAuth, EvaluationController.getEvaluationById)
router.post('/getOpenEvaluations', isAuth, EvaluationController.getOpenEvaluations)
router.put('/updateEvaluation', isAuth, EvaluationController.updateEvaluation)
router.post('/createEvaluation', isAuth, EvaluationController.createEvaluation)
router.post('/checkOperations', isAuth, EvaluationController.checkOperations)
router.put('/cancelEvaluation', isAuth, EvaluationController.cancelEvaluation)
router.patch('/changeType/:id', isAuth, EvaluationController.changeEvaluationType)
router.patch('/:id', isAuth, Upload.array('images'), EvaluationController.addBatchImages)
router.put('/precify/:evaluationId', isAuth, EvaluationController.updatePricingType)

module.exports = router
