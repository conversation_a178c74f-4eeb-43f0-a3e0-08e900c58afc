import { IPrintCashierConference } from '../../types';
import './styles.css';

interface CashierConferenceProps {
  data: IPrintCashierConference
}

const CashierConference: React.FC<CashierConferenceProps> = ({ data: {
  evaluations,
  paymentForms,
  totalValue
}}) => {
  return (
    <div style={{ display: 'block' }}>
      <h4 style={{ textAlign: 'center' }}>Conferência de Avaliação</h4>
      {paymentForms?.map((form, index) => {
        const typeEvaluations = evaluations?.filter(evaluation => evaluation?.tipo === form && evaluation?.finalizado && !evaluation?.cancelado && !evaluation?.rejected)

        let total = 0
        typeEvaluations?.forEach((evaluation) => {
          const value = form === "Dinheiro" ? evaluation?.totalDinheiroEfetivado : form === 'Vale' ? evaluation?.totalValeEfetivado : evaluation?.totalPixEfetivado
          total = total += Number(value)
        });

        return (
          <div style={{ textAlign: 'center' }} key={index}>
            <h4>{form === 'Vale' ? "Giracrédito" : form}</h4>
            {typeEvaluations?.length ? (
              <table>
                <thead>
                  <tr>
                    <th>Nome</th>
                    <th style={{ width: '40%' }}>CPF</th>
                    <th>Valor</th>
                  </tr>
                </thead>
                <tbody
                  style={{
                    fontSize: '10px',
                  }}
                >
                  {typeEvaluations?.map((evaluation, index) => (
                    <>
                      <tr key={`${evaluation?._id}-${index}`}>
                        <td>{evaluation?.cliente}</td>
                        <td style={{ width: '40%' }}>{evaluation?.cpf?.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, "$1.$2.$3-$4")}</td>
                        <td>
                          {Intl.NumberFormat('pt-BR', { currency: 'BRL', style: 'currency'}).format(Number(form === 'Dinheiro' ? evaluation?.totalDinheiroEfetivado : form === 'Vale' ? evaluation?.totalValeEfetivado : evaluation?.totalPixEfetivado))}
                        </td>
                      </tr>
                    </>
                  ))}
                </tbody>
              </table>
            ) : null}
            <h5>{`Quantidade: ${typeEvaluations?.length || 0}`}</h5>
            <h5>{`Total ${form === 'Vale' ? 'Giracrédito' : form}: ${Intl.NumberFormat('pt-BR', { currency: 'BRL', style: 'currency' }).format(total)}`}</h5>
            <div className="line">
              <p>------------------------------</p>
            </div>
          </div>
        )
      })}
      <h4 style={{ textAlign: 'center' }}>Total geral</h4>
      <div style={{ textAlign: 'center' }}>
        <h5>{`Quantidade: ${evaluations?.filter(evaluation => evaluation?.finalizado && !evaluation?.cancelado && !evaluation?.rejected)?.length}`}</h5>
        <h5>{`Valor Total: ${Intl.NumberFormat('pt-BR', { currency: 'BRL', style: 'currency' }).format(Number(totalValue) || 0)}`}</h5>
      </div>
      <p>.</p>
    </div>
  )
}

export default CashierConference;
