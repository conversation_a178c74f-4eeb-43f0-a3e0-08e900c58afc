const { Router } = require('express')
const router = Router()

const ProductsRouters = require('./products')
const EnterprisesRouters = require('./enterprises')
const UsersRouters = require('./users')
const MessageRouters = require('./messages')
const ClientsRouters = require('./clients')
const PreEvaluationsRouters = require('./pre-evaluations')
const LevelsRouters = require('./levels')
const EvaluationsRouters = require('./evaluations')
const AddressRouters = require('./address')
const PrinterOriginsRouters = require('./printer-origins')
const CashierRouters = require('./cashier')
const ReportsRouters = require('./reports')
const PointsRouters = require('./points')
const PointsRulesRouters = require('./pointsRules')
const ReviewsRouters = require('./reviews')
const HigienizationRouters = require('./higienization')
const KitsRouters = require('./kits')
const PrintersRoutes = require('./printers')
const AlertsRoutes = require('./alerts')

router.use('/products', ProductsRouters)
router.use('/enterprises', EnterprisesRouters)
router.use('/users', UsersRouters)
router.use('/messages', MessageRouters)
router.use('/clients', ClientsRouters)
router.use('/pre-evaluation', PreEvaluationsRouters)
router.use('/levels', LevelsRouters)
router.use('/evaluations', EvaluationsRouters)
router.use('/address', AddressRouters)
router.use('/printer-origins', PrinterOriginsRouters)
router.use('/cashier', CashierRouters)
router.use('/reports', ReportsRouters)
router.use('/points', PointsRouters)
router.use('/pointsRules', PointsRulesRouters)
router.use('/reviews', ReviewsRouters)
router.use('/higienizations', HigienizationRouters)
router.use('/kits', KitsRouters)
router.use('/printers', PrintersRoutes)
router.use('/alerts', AlertsRoutes)

module.exports = router
