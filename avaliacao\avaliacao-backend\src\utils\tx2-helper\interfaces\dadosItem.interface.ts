enum OrigemMercadoria {
  Nacional = 0,
  EstrangeiraImportacaoDireta = 1,
  EstrangeiraAdquiridaBrasil = 2,
  EstrangeiraImportacaoIndireta = 3,
  EstrangeiraNacionalConteudoImportacaoSuperior40 = 4,
  EstrangeiraNacionalProcessosBasicos = 5,
  EstrangeiraNacionalConteudoImportacaoInferior40 = 6,
  EstrangeiraImportacaoSemSimilar = 7,
  EstrangeiraNacionalMercosul = 8,
}

enum TributacaoICMS {
  TributacaoIntegral = "00",
  TributacaoReduzida = "20",
  Isento = "40",
  NaoTributado = "41",
  Suspenso = "50",
  Diferimento = "51",
  ICMSSubstituto = "60",
  ICMSNaoCobranca = "70",
  ICMSOutros = "90",
}

enum CFOP {
  Venda = "5102",
  Retorno = "5949",
  Devolucao = "5202",
  TransferenciaEntreFiliais = "5152",
  VendaParaEntregaFutura = "5922",
}

interface DadosItem {
  /** Número do item. */
  nItem_H02: number;
  /** GTIN do produto, antigo código EAN ou código de barras. */
  cEAN_I03: string;
  /** Código do produto ou serviço. */
  cProd_I02: string;
  /** Descrição do produto ou serviço. */
  xProd_I04: string;
  /** Código NCM com 8 dígitos. */
  NCM_I05: string;
  /** Código CEST. */
  CEST_I05c: string;
  /** Código fiscal de operações e prestações. */
  CFOP_I08: CFOP;
  /** Unidade comercial. */
  uCom_I09: string;
  /** Quantidade comercial. */
  qCom_I10: number;
  /** Valor unitário de comercialização. */
  vUnCom_I10a: number;
  /** Valor total bruto dos produtos ou serviços. */
  vProd_I11: number;
  /** GTIN da unidade tributável, antigo código EAN. */
  cEANTrib_I12: string;
  /** Unidade tributável. */
  uTrib_I13: string;
  /** Quantidade tributável. */
  qTrib_I14a: number;
  /** Valor unitário de tributação. */
  vUnTrib_I14a: number;
  /** Indica se valor do item (vProd) entra no valor total da NF-e (vProd); 0 = Falso, 1 = Verdadeiro */
  indTot_I17b: number;
  /** Origem da mercadoria. */
  orig_N11: OrigemMercadoria;
  /** Tributação do ICMS = 00 */
  CST_N12: TributacaoICMS;
}

export default DadosItem;
