$main-bg: #dfe6e9;
$purple: #5d9cec;
$deep-purple: darken($purple, 10%);
$light-green: #55efc4;
$ocean-blue: #28d;
$shock-pink: #d43f8d;

ul {
  margin: 0;
  padding: 1em;

  li {
    list-style: none;

    a {
      padding: 1.07em 1em;
      color: white;
      text-decoration: none;

      /* -webkit-transition: background-color .5s;
			-o-transition: background-color .5s;
			transition: background-color .5s, color .5s; */
      &:hover {
        /* background: -webkit-linear-gradient(bottom, $shock-pink, $ocean-blue); */
        color: darken($color: white, $amount: 20%);
      }

      .contact {
        border: 1px solid white;
      }
    }
  }
}

.modal-container-message {
  padding: 10px 0;
}

.notificationsTitle {
  color: white;
  text-align: center;
}

.messages-history {
  margin-top: 25px;
}

.message-single {
  color: white;
  margin-top: 10px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid white;
  word-break: break-all;
}

.message-single .message {
  padding: 0 5px;
  max-width: 70%;

  @media (max-width: 960px) {
    font-size: 10px;
  }
}

.icon-message {
  margin-right: 10px;
}

.visualized-date p {
  font-size: 0.7rem;
}

.nav {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  background: black;
  color: white;
  height: 30px;
  position: fixed;
  z-index: 2;
  width: 100%;
  padding: 1rem 0;
  margin-left: 72px;
  flex-direction: row;

  .slam-left {
    text-align: left;
    margin-top: auto;
  }
}

.nav select {
  border: 1px solid #262626;
  border-radius: 5px;
  height: 30px;
  width: 200px;
  color: white;
  background-color: #161610;
  font-size: 15px;
  width: 100%;
}

#unidades {
  margin-left: 1rem;
  margin-right: -15px;
}

.right-itens {
  display: flex;
  justify-content: space-between;
  right: 0;
  position: fixed;
  font-size: larger;
}

.nav-item {
  // width: 50%;
  text-align: center;
}

#navbar .nav #hamburguer {
  display: none;
}

#sair {
  display: none;
}

#btn-sair {
  cursor: pointer;
  background: #262626;
  padding: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 4px;
  margin-top: -15px;
}

.modal-container-message {
  padding: 10px 0;
}

.notificationsTitle {
  color: white;
  text-align: center;
}

.messages-history {
  margin-top: 25px;
}

.message-single {
  color: white;
  margin-top: 10px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid white;
}

.message-single .message {
  padding: 0 5px;
}

.icon-message {
  margin-right: 10px;
}

.visualized-date p {
  font-size: 0.7rem;
}

.responsive-nav {
  display: flex;
  gap: 1rem;
}
#appstatus {
  display: none;
}

@media (max-width: 540px) {
  .right-itens {
    justify-content: end;
  }

  .nav-item {
    margin-left: 0;
    margin-right: 0;
    padding: 0;
    font-size: 10px;
  }
}

@media (max-width: 968px) {
  #unidades {
    display: none;
  }

  #navbar .nav {
    width: 100%;
    display: flex;
    flex-direction: column;
    margin-left: 0px;
  }
}

@media print {
  .hidden-print,
  .hidden-print * {
    display: none !important;
  }
}
