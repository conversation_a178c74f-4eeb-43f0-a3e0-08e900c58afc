"use strict";function _objectWithoutProperties(source, excluded) {if (source == null) return {};var target = _objectWithoutPropertiesLoose(source, excluded);var key, i;if (Object.getOwnPropertySymbols) {var sourceSymbolKeys = Object.getOwnPropertySymbols(source);for (i = 0; i < sourceSymbolKeys.length; i++) {key = sourceSymbolKeys[i];if (excluded.indexOf(key) >= 0) continue;if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;target[key] = source[key];}}return target;}function _objectWithoutPropertiesLoose(source, excluded) {if (source == null) return {};var target = {};var sourceKeys = Object.keys(source);var key, i;for (i = 0; i < sourceKeys.length; i++) {key = sourceKeys[i];if (excluded.indexOf(key) >= 0) continue;target[key] = source[key];}return target;}function ownKeys(object, enumerableOnly) {var keys = Object.keys(object);if (Object.getOwnPropertySymbols) {var symbols = Object.getOwnPropertySymbols(object);if (enumerableOnly) symbols = symbols.filter(function (sym) {return Object.getOwnPropertyDescriptor(object, sym).enumerable;});keys.push.apply(keys, symbols);}return keys;}function _objectSpread(target) {for (var i = 1; i < arguments.length; i++) {var source = arguments[i] != null ? arguments[i] : {};if (i % 2) {ownKeys(source, true).forEach(function (key) {_defineProperty(target, key, source[key]);});} else if (Object.getOwnPropertyDescriptors) {Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));} else {ownKeys(source).forEach(function (key) {Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));});}}return target;}function _defineProperty(obj, key, value) {if (key in obj) {Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });} else {obj[key] = value;}return obj;}function _classCallCheck(instance, Constructor) {if (!(instance instanceof Constructor)) {throw new TypeError("Cannot call a class as a function");}}function _defineProperties(target, props) {for (var i = 0; i < props.length; i++) {var descriptor = props[i];descriptor.enumerable = descriptor.enumerable || false;descriptor.configurable = true;if ("value" in descriptor) descriptor.writable = true;Object.defineProperty(target, descriptor.key, descriptor);}}function _createClass(Constructor, protoProps, staticProps) {if (protoProps) _defineProperties(Constructor.prototype, protoProps);if (staticProps) _defineProperties(Constructor, staticProps);return Constructor;}var ClientModel = require('../../models/Cliente');
var EvaluationModel = require('../../models/Avaliacao');
var EnterpriseModel = require('../../models/Empresa');
var ReviewsModel = require('../../models/Reviews');var _require =
require('../../helpers/getLatLongByAddress'),getLatLongByAddress = _require.getLatLongByAddress;
var sendFileToS3 = require('../../utils/send-file-s3');
var moment = require('moment');var _require2 =
require('../../utils/crypto'),encrypt = _require2.encrypt,decrypt = _require2.decrypt;var
ClientsClass = /*#__PURE__*/function () {
  function ClientsClass() {_classCallCheck(this, ClientsClass);}_createClass(ClientsClass, [{ key: "getClientsByCpf", value: function getClientsByCpf(

    req, res) {var _decrypt, cpf, client;return regeneratorRuntime.async(function getClientsByCpf$(_context) {while (1) {switch (_context.prev = _context.next) {case 0:_decrypt =
              decrypt(req.body.data, process.env.PRIVATE_KEY), cpf = _decrypt.cpf;if (!(

              !cpf || cpf?.length < 3)) {_context.next = 3;break;}return _context.abrupt("return",
              res.status(400).json({
                status: false,
                error: 'CPF inválido' }));case 3:_context.next = 5;return regeneratorRuntime.awrap(



              ClientModel.findOne({ cpf: cpf }).lean());case 5:client = _context.sent;return _context.abrupt("return",
              res.status(200).json({
                data: encrypt(client, process.env.PRIVATE_KEY).toString() }));case 7:case "end":return _context.stop();}}});} }, { key: "getTotemClientsByCpf", value: function getTotemClientsByCpf(



    req, res) {var _ref, cpf, client;return regeneratorRuntime.async(function getTotemClientsByCpf$(_context2) {while (1) {switch (_context2.prev = _context2.next) {case 0:_ref =
              req.body || {}, cpf = _ref.cpf;if (!(

              !cpf || cpf?.length < 3)) {_context2.next = 3;break;}return _context2.abrupt("return",
              res.status(400).json({
                status: false,
                error: 'CPF inválido' }));case 3:_context2.next = 5;return regeneratorRuntime.awrap(



              ClientModel.findOne({ cpf: cpf }).lean());case 5:client = _context2.sent;return _context2.abrupt("return",
              res.status(200).json(client));case 7:case "end":return _context2.stop();}}});} }, { key: "getClientHistory", value: function getClientHistory(


    req, res) {var search, filter, clientEvaluations, clientEvaluationsHistory;return regeneratorRuntime.async(function getClientHistory$(_context4) {while (1) {switch (_context4.prev = _context4.next) {case 0:
              search = req.query.search;

              filter = {};

              if (!isNaN(Number(search))) {
                filter = {
                  cpf: { $regex: diacriticSensitiveRegex(search) } };

              } else {
                filter = {
                  cliente: { $regex: new RegExp(diacriticSensitiveRegex(search), 'i') } };

              }_context4.next = 5;return regeneratorRuntime.awrap(

              EvaluationModel.aggregate([
              {
                $match: _objectSpread(_objectSpread({},
                filter), {}, {
                  finalizado: true }) },


              {
                $sort: {
                  dataInicio: -1 } },


              {
                $limit: 30 },

              {
                $lookup: {
                  as: 'empresa',
                  from: 'empresas',
                  localField: 'cnpj',
                  foreignField: 'cnpj' } },


              {
                $project: {
                  nomeUnidade: '$empresa.nomeUnidade',
                  dataFinal: 1,
                  status: 1,
                  motivo: 1,
                  cancelado: 1,
                  reject_at: 1,
                  cliente: 1,
                  idPreAvaliacao: 1,
                  finalizado: 1,
                  cpf: 1 } }]));case 5:clientEvaluations = _context4.sent;_context4.next = 8;return regeneratorRuntime.awrap(




              Promise.all(
              clientEvaluations?.map(function _callee(evaluation) {var evaluationData, evaluationHistory;return regeneratorRuntime.async(function _callee$(_context3) {while (1) {switch (_context3.prev = _context3.next) {case 0:
                        evaluationData = _objectSpread({}, evaluation);_context3.next = 3;return regeneratorRuntime.awrap(

                        ReviewsModel.findOne({ referenceId: evaluation?._id }));case 3:evaluationHistory = _context3.sent;

                        if (evaluationHistory) {
                          evaluationData.points = evaluationHistory?.points;
                          evaluationData.observation = evaluationHistory?.observation;
                        }return _context3.abrupt("return",

                        evaluationData);case 6:case "end":return _context3.stop();}}});})));case 8:clientEvaluationsHistory = _context4.sent;return _context4.abrupt("return",



              res.status(200).json({
                data: encrypt(
                {
                  status: true,
                  evaluations: clientEvaluationsHistory },

                process.env.PRIVATE_KEY).
                toString() }));case 10:case "end":return _context4.stop();}}});} }, { key: "createClient", value: function createClient(



    req, res) {var _req$body, file, cliente, clientAlreadyExists, addressFormatted, geo, client, fileURL;return regeneratorRuntime.async(function createClient$(_context5) {while (1) {switch (_context5.prev = _context5.next) {case 0:_context5.prev = 0;_req$body =

              req.body, file = _req$body.file, cliente = _objectWithoutProperties(_req$body, ["file"]);_context5.next = 4;return regeneratorRuntime.awrap(

              ClientModel.findOne({ cpf: cliente.cpf }));case 4:clientAlreadyExists = _context5.sent;if (!

              clientAlreadyExists) {_context5.next = 7;break;}return _context5.abrupt("return",
              res.status(400).json({
                status: false,
                error: 'Cliente já cadastrado' }));case 7:



              addressFormatted = [
              cliente.rua,
              cliente.numero,
              cliente.bairro,
              cliente.cidade,
              cliente.estado].
              join(', ');_context5.next = 10;return regeneratorRuntime.awrap(

              getLatLongByAddress(addressFormatted));case 10:geo = _context5.sent;

              if (geo.latitude) {
                cliente.geo = {
                  latitude: geo.latitude,
                  longitude: geo.longitude };

              }

              client = null;if (

              clientAlreadyExists) {_context5.next = 19;break;}_context5.next = 16;return regeneratorRuntime.awrap(
              ClientModel.create(cliente));case 16:client = _context5.sent;_context5.next = 20;break;case 19:

              client = clientAlreadyExists;case 20:if (!


              file) {_context5.next = 27;break;}_context5.next = 23;return regeneratorRuntime.awrap(
              uploadBase64ToS3(
              file, "".concat(
              process.env.ENVIRONMENT, "/avaliacao/clientes/")));case 23:fileURL = _context5.sent;

              client.image = fileURL;_context5.next = 27;return regeneratorRuntime.awrap(
              client.save());case 27:return _context5.abrupt("return",


              res.status(200).json(client));case 30:_context5.prev = 30;_context5.t0 = _context5["catch"](0);

              console.log(_context5.t0);return _context5.abrupt("return",
              res.status(400).json({
                status: false,
                error: 'Erro ao cadastrar cliente' }));case 34:case "end":return _context5.stop();}}}, null, null, [[0, 30]]);} }, { key: "updateClient", value: function updateClient(




    req, res) {var _ref2, nome, cpf, dtNascimento, celular, rua, bairro, numero, cep, cod_uf, cod_cidade, estado, cidade, complemento, image, file, findCliente, clienteRequest, addressFormatted, geo, update;return regeneratorRuntime.async(function updateClient$(_context6) {while (1) {switch (_context6.prev = _context6.next) {case 0:_context6.prev = 0;_ref2 =

















              req.body || {}, nome = _ref2.nome, cpf = _ref2.cpf, dtNascimento = _ref2.dtNascimento, celular = _ref2.celular, rua = _ref2.rua, bairro = _ref2.bairro, numero = _ref2.numero, cep = _ref2.cep, cod_uf = _ref2.cod_uf, cod_cidade = _ref2.cod_cidade, estado = _ref2.estado, cidade = _ref2.cidade, complemento = _ref2.complemento, image = _ref2.image, file = _ref2.file;if (!(

              !cpf || cpf.length < 4)) {_context6.next = 4;break;}return _context6.abrupt("return",
              res.json({
                status: false,
                message: 'Cliente não encontrado!' }));case 4:_context6.next = 6;return regeneratorRuntime.awrap(



              ClientModel.findOne({ cpf: cpf }).lean());case 6:findCliente = _context6.sent;if (

              findCliente) {_context6.next = 9;break;}return _context6.abrupt("return",
              res.json({
                status: false,
                message: 'Cliente não encontrado!' }));case 9:



              clienteRequest = {
                nome: nome,
                cpf: cpf,
                dtNascimento: dtNascimento,
                celular: celular,
                rua: rua,
                bairro: bairro,
                numero: numero,
                cep: cep,
                cod_uf: cod_uf,
                cod_cidade: cod_cidade,
                estado: estado,
                cidade: cidade,
                complemento: complemento,
                image: image };


              // Se for a mesma rua ou número ou o usuário não tiver geo, consulta e atualiza
              if (!(
              clienteRequest.rua != findCliente.rua ||
              Number(clienteRequest.numero) !== findCliente.numero ||
              !findCliente?.['geo']?.['latitude'])) {_context6.next = 16;break;}

              addressFormatted = [
              clienteRequest.rua,
              clienteRequest.numero,
              clienteRequest.bairro,
              clienteRequest.cidade,
              clienteRequest.estado].
              join(', ');_context6.next = 14;return regeneratorRuntime.awrap(

              getLatLongByAddress(addressFormatted));case 14:geo = _context6.sent;

              if (geo?.latitude) {
                clienteRequest.geo = {
                  latitude: geo.latitude,
                  longitude: geo.longitude };

              }case 16:if (!


              file) {_context6.next = 20;break;}_context6.next = 19;return regeneratorRuntime.awrap(
              uploadBase64ToS3(
              file, "".concat(
              process.env.ENVIRONMENT, "/avaliacao/clientes/")));case 19:clienteRequest.image = _context6.sent;case 20:_context6.next = 22;return regeneratorRuntime.awrap(


              ClientModel.findOneAndUpdate({ cpf: cpf }, clienteRequest, {
                "new": true }).
              lean());case 22:update = _context6.sent;
              res.json(update);_context6.next = 29;break;case 26:_context6.prev = 26;_context6.t0 = _context6["catch"](0);

              res.json(_context6.t0);case 29:case "end":return _context6.stop();}}}, null, null, [[0, 26]]);} }, { key: "getAllClients", value: function getAllClients(



    req, res) {var clients;return regeneratorRuntime.async(function getAllClients$(_context7) {while (1) {switch (_context7.prev = _context7.next) {case 0:
              // salvar clientes do redis e recuperar, gerar uma rota dinamica e por na cron
              // verificar se existem dados no redis antes de realizar a query abaixo
              // verificar se consumirá mt processamento durante a semana
              // const clients = await ClientModel.find({
              //   cod_cidade: req.usuario.enterprise.codCidade
              // }).lean();
              clients = [];return _context7.abrupt("return",
              res.status(200).json({
                data: encrypt(clients, process.env.PRIVATE_KEY).toString() }));case 2:case "end":return _context7.stop();}}});} }]);return ClientsClass;}();




function uploadBase64ToS3(file, path) {var fileBuffer, fileName, fileFormmat, fileData;return regeneratorRuntime.async(function uploadBase64ToS3$(_context8) {while (1) {switch (_context8.prev = _context8.next) {case 0:
          fileBuffer = Buffer.from(file.split(';base64,').pop(), 'base64');
          fileName = "".concat(Date.now(), ".png");

          fileFormmat = {
            name: fileName,
            data: fileBuffer };_context8.next = 5;return regeneratorRuntime.awrap(

          sendFileToS3(fileFormmat, path));case 5:fileData = _context8.sent;if (!(

          !fileData && !fileData.fileUrl)) {_context8.next = 8;break;}return _context8.abrupt("return", null);case 8:return _context8.abrupt("return",

          fileData.fileUrl);case 9:case "end":return _context8.stop();}}});}


function diacriticSensitiveRegex() {var string = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';
  return string.
  replace(/a/g, '[a,á,à,ä,â]').
  replace(/A/g, '[A,a,á,à,ä,â]').
  replace(/e/g, '[e,é,ë,è]').
  replace(/E/g, '[E,e,é,ë,è]').
  replace(/i/g, '[i,í,ï,ì]').
  replace(/I/g, '[I,i,í,ï,ì]').
  replace(/o/g, '[o,ó,ö,ò]').
  replace(/O/g, '[O,o,ó,ö,ò]').
  replace(/u/g, '[u,ü,ú,ù]').
  replace(/U/g, '[U,u,ü,ú,ù]').
  replace(/^a-z/g, '[]');
}

module.exports = ClientsClass;