const { model, Schema } = require('mongoose')

// Definindo Schema
const BlackListSchema = Schema({
  cpf: {
    type: String,
  },
  cnpj: {
    type: String,
  },
  deletedAt: {
    type: Date,
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  },
})

// Definindo collection
const Alert = model('blacklists', BlackListSchema)
module.exports = Alert
