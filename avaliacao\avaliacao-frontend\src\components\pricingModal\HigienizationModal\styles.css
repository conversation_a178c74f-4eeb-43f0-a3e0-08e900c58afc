.concat-items-modal {
  background: #FFF;
  border-radius: 5px;
  width: 30%;
  height: 30%;
  padding: 1rem;
  display: flex;
  flex-flow: column;
  gap: 1rem;
  justify-content: space-between;
}

.concat-items-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.concat-items-modal-header svg {
  cursor: pointer
}

.concat-items-modal-content {
  display: flex;
  gap: 1rem;
  flex-flow: column;
}

.concat-items-modal-footer {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  align-items: center;
}

.concat-items-modal-row {
  display: flex;
  gap: 1rem;
  align-items: flex-end;
}

.concat-items-modal-row button {
  height: 38px;
}

@media (max-height: 657px) {
  .concat-items-modal {
    height: 50%;
  }
}

@media (max-width: 922px) {
  .concat-items-modal {
    width: 50%;
    height: 50%;
  }
}

@media (max-width: 1281px) {
  .concat-items-modal {
    width: 50%;
    height: 50%;
  }
}
